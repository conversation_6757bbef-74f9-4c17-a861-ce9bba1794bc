{"name": "my-v0-project", "version": "0.1.5", "private": true, "scripts": {"dev": "next dev -p 8081", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hello-pangea/dnd": "latest", "@hookform/resolvers": "^3.9.1", "@langchain/community": "^0.3.46", "@langchain/core": "^0.3.58", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@stagewise/toolbar-next": "^0.2.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "^0.4.1", "@supabase/supabase-js": "^2.43.4", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/uuid": "^10.0.0", "@types/wavesurfer.js": "^6.0.12", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "date-fns": "latest", "embla-carousel-react": "8.5.1", "emoji-picker-react": "^4.12.2", "filepond": "^4.32.7", "filepond-plugin-file-validate-size": "^2.2.8", "filepond-plugin-file-validate-type": "^1.2.9", "filepond-plugin-image-preview": "^4.6.12", "framer-motion": "^12.22.0", "input-otp": "1.4.1", "lodash": "^4.17.21", "lucide-react": "^0.454.0", "next": "^14.2.3", "next-themes": "^0.4.4", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-filepond": "^7.1.3", "react-hook-form": "^7.54.1", "react-piano": "^3.1.3", "react-quill": "^2.0.0", "react-resizable-panels": "^2.1.9", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.6", "vexchords": "^1.2.0", "wavesurfer.js": "^7.9.5", "zod": "^3.24.1", "zustand": "^5.0.5"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/lodash": "^4.17.17", "@types/node": "22.15.27", "@types/quill": "^2.0.14", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-dropzone": "^4.2.2", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "^14.2.3", "postcss": "^8.5.4", "supabase": "^2.22.12", "tailwindcss": "^3.4.17", "typescript": "5.8.3"}}