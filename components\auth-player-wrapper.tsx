"use client";
import { useEffect, useState } from "react";
import { createBrowserClient } from "@/lib/supabase/client";
import { GlobalAudioPlayer } from "@/components/audio/global-audio-player";
import { useSidebar } from "@/components/ui/sidebar-provider";
import { useAudioPlayerStore } from "@/lib/stores/audioPlayerStore";
import { usePathname } from "next/navigation";
import { User } from "@supabase/supabase-js";

export function AuthPlayerWrapper() {
  const [user, setUser] = useState<User | null>(null);
  const pathname = usePathname();
  const { currentSong } = useAudioPlayerStore();
  const { open: isSidebarActuallyOpen, isMobile } = useSidebar();

  useEffect(() => {
    const supabase = createBrowserClient();

    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    checkUser();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  const shouldPlayerComponentRender = !!user && pathname !== "/" && !!currentSong;
  const isLeftSidebarOpen = isSidebarActuallyOpen && !isMobile;

  if (!shouldPlayerComponentRender) {
    return null;
  }
  
  return (
    <div
      style={{
        position: "fixed",
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 40, // Positioned under the sidebar (z-50)
      }}
    >
      <GlobalAudioPlayer 
        isPlayerVisible={shouldPlayerComponentRender}
        isLeftSidebarOpen={isLeftSidebarOpen}
        isRightSidebarOpen={false}
      />
    </div>
  );
}
