"use client";

import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lette, Bell, ShieldCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export default function PreferencesPage() {
  const router = useRouter();

  return (
    <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8 bg-background text-foreground">
      <header className="flex items-center gap-4 mb-8 pb-4 border-b border-border">
        <Button variant="outline" size="icon" onClick={() => router.back()}>
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
            <Settings className="h-8 w-8 text-primary" />
            Préférences
          </h1>
          <p className="text-muted-foreground"><PERSON><PERSON><PERSON> les paramètres de l'application et de votre compte.</p>
        </div>
      </header>

      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        <div className="p-6 bg-card rounded-lg border border-border">
          <h2 className="text-xl font-semibold flex items-center gap-2 mb-4"><Palette /> Apparence</h2>
          <p className="text-muted-foreground">Personnalisez l'apparence de l'interface (bientôt disponible).</p>
        </div>
        <div className="p-6 bg-card rounded-lg border border-border">
          <h2 className="text-xl font-semibold flex items-center gap-2 mb-4"><Bell /> Notifications</h2>
          <p className="text-muted-foreground">Choisissez comment et quand vous recevez des notifications (bientôt disponible).</p>
        </div>
        <div className="p-6 bg-card rounded-lg border border-border">
          <h2 className="text-xl font-semibold flex items-center gap-2 mb-4"><ShieldCheck /> Sécurité et Confidentialité</h2>
          <p className="text-muted-foreground">Gérez les paramètres de sécurité de votre compte (bientôt disponible).</p>
        </div>
      </div>
    </div>
  );
}
