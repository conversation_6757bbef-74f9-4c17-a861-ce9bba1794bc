"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bell, 
  MessageCircle, 
  Heart, 
  UserPlus, 
  Music, 
  Radio,
  Settings,
  Check,
  CheckCheck,
  Trash2,
  Filter,
  MoreHorizontal
} from 'lucide-react';

export default function NotificationsPage() {
  const [activeTab, setActiveTab] = useState('all');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);

  // Données de démonstration
  const notifications = [
    {
      id: '1',
      type: 'friend_request',
      title: 'Nouvelle demande d\'amitié',
      content: '<PERSON> souhaite vous ajouter comme ami',
      actor: {
        name: '<PERSON>',
        avatar: '/avatars/diana.jpg',
        username: '@diana_vocals'
      },
      timestamp: 'Il y a 5 minutes',
      isRead: false,
      isNew: true,
      priority: 'normal',
      actionUrl: '/friends'
    },
    {
      id: '2',
      type: 'message',
      title: 'Nouveau message',
      content: 'Alice Martin vous a envoyé un message dans la conversation "Projet Jazz"',
      actor: {
        name: 'Alice Martin',
        avatar: '/avatars/alice.jpg',
        username: '@alice_music'
      },
      timestamp: 'Il y a 15 minutes',
      isRead: false,
      isNew: true,
      priority: 'high',
      actionUrl: '/messages?conversation=1'
    },
    {
      id: '3',
      type: 'like',
      title: 'Votre morceau a été aimé',
      content: 'Bob Wilson et 12 autres personnes ont aimé votre morceau "Midnight Blues"',
      actor: {
        name: 'Bob Wilson',
        avatar: '/avatars/bob.jpg',
        username: '@bob_beats'
      },
      timestamp: 'Il y a 1 heure',
      isRead: true,
      isNew: false,
      priority: 'normal',
      actionUrl: '/manage-songs/midnight-blues'
    },
    {
      id: '4',
      type: 'comment',
      title: 'Nouveau commentaire',
      content: 'Charlie Dubois a commenté votre album "Electric Dreams"',
      actor: {
        name: 'Charlie Dubois',
        avatar: '/avatars/charlie.jpg',
        username: '@charlie_guitar'
      },
      timestamp: 'Il y a 2 heures',
      isRead: true,
      isNew: false,
      priority: 'normal',
      actionUrl: '/albums/electric-dreams'
    },
    {
      id: '5',
      type: 'community_activity',
      title: 'Activité dans votre communauté',
      content: 'Nouveau contenu tendance dans la Communauté Jazz',
      actor: {
        name: 'Communauté Jazz',
        avatar: '/communities/jazz.jpg',
        username: 'jazz-community'
      },
      timestamp: 'Il y a 3 heures',
      isRead: true,
      isNew: false,
      priority: 'low',
      actionUrl: '/communities/jazz'
    },
    {
      id: '6',
      type: 'collaboration',
      title: 'Invitation à collaborer',
      content: 'Erik Johnson vous invite à rejoindre son projet "Rock Fusion"',
      actor: {
        name: 'Erik Johnson',
        avatar: '/avatars/erik.jpg',
        username: '@erik_drums'
      },
      timestamp: 'Hier',
      isRead: false,
      isNew: false,
      priority: 'high',
      actionUrl: '/collaborations/rock-fusion'
    }
  ];

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'friend_request': return <UserPlus className="h-5 w-5 text-blue-400" />;
      case 'message': return <MessageCircle className="h-5 w-5 text-green-400" />;
      case 'like': return <Heart className="h-5 w-5 text-red-400" />;
      case 'comment': return <MessageCircle className="h-5 w-5 text-purple-400" />;
      case 'community_activity': return <Radio className="h-5 w-5 text-orange-400" />;
      case 'collaboration': return <Music className="h-5 w-5 text-yellow-400" />;
      default: return <Bell className="h-5 w-5 text-slate-400" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-l-red-500';
      case 'normal': return 'border-l-blue-500';
      case 'low': return 'border-l-gray-500';
      default: return 'border-l-slate-500';
    }
  };

  const filterNotifications = (type: string) => {
    if (type === 'all') return notifications;
    if (type === 'unread') return notifications.filter(n => !n.isRead);
    return notifications.filter(n => n.type === type);
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const markAsRead = (notificationId: string) => {
    // Logique pour marquer comme lu
    console.log('Marquer comme lu:', notificationId);
  };

  const markAllAsRead = () => {
    // Logique pour marquer tout comme lu
    console.log('Marquer tout comme lu');
  };

  const deleteNotification = (notificationId: string) => {
    // Logique pour supprimer
    console.log('Supprimer notification:', notificationId);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">
                🔔 Notifications
              </h1>
              <p className="text-slate-400">
                Restez informé de toute l'activité autour de votre musique
              </p>
            </div>
            <div className="flex items-center gap-2">
              {unreadCount > 0 && (
                <Button 
                  onClick={markAllAsRead}
                  variant="outline" 
                  className="border-slate-600 text-slate-300"
                >
                  <CheckCheck className="h-4 w-4 mr-2" />
                  Tout marquer comme lu
                </Button>
              )}
              <Button variant="outline" className="border-slate-600 text-slate-300">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Statistiques rapides */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-white">{unreadCount}</div>
              <div className="text-sm text-slate-400">Non lues</div>
            </CardContent>
          </Card>
          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-white">{notifications.filter(n => n.type === 'message').length}</div>
              <div className="text-sm text-slate-400">Messages</div>
            </CardContent>
          </Card>
          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-white">{notifications.filter(n => n.type === 'like').length}</div>
              <div className="text-sm text-slate-400">Likes</div>
            </CardContent>
          </Card>
          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-white">{notifications.filter(n => n.type === 'friend_request').length}</div>
              <div className="text-sm text-slate-400">Demandes d'amitié</div>
            </CardContent>
          </Card>
        </div>

        {/* Onglets de filtrage */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="bg-slate-800 border-slate-700">
            <TabsTrigger value="all" className="data-[state=active]:bg-slate-700">
              <Bell className="h-4 w-4 mr-2" />
              Toutes ({notifications.length})
            </TabsTrigger>
            <TabsTrigger value="unread" className="data-[state=active]:bg-slate-700">
              <Filter className="h-4 w-4 mr-2" />
              Non lues ({unreadCount})
            </TabsTrigger>
            <TabsTrigger value="message" className="data-[state=active]:bg-slate-700">
              <MessageCircle className="h-4 w-4 mr-2" />
              Messages
            </TabsTrigger>
            <TabsTrigger value="friend_request" className="data-[state=active]:bg-slate-700">
              <UserPlus className="h-4 w-4 mr-2" />
              Amis
            </TabsTrigger>
          </TabsList>

          {/* Liste des notifications */}
          <TabsContent value={activeTab}>
            <div className="space-y-2">
              {filterNotifications(activeTab).map((notification) => (
                <Card 
                  key={notification.id} 
                  className={`bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors border-l-4 ${getPriorityColor(notification.priority)} ${
                    !notification.isRead ? 'bg-slate-800/80' : ''
                  }`}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-4">
                      {/* Icône de type */}
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>

                      {/* Avatar de l'acteur */}
                      <Avatar className="h-12 w-12 flex-shrink-0">
                        <AvatarImage src={notification.actor.avatar} />
                        <AvatarFallback className="bg-slate-600 text-white">
                          {notification.actor.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>

                      {/* Contenu */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className={`font-medium ${!notification.isRead ? 'text-white' : 'text-slate-300'}`}>
                              {notification.title}
                              {notification.isNew && (
                                <Badge className="ml-2 bg-blue-500 text-white text-xs">Nouveau</Badge>
                              )}
                            </h3>
                            <p className="text-slate-400 text-sm mt-1">
                              {notification.content}
                            </p>
                            <p className="text-slate-500 text-xs mt-2">
                              {notification.timestamp}
                            </p>
                          </div>
                          
                          {/* Actions */}
                          <div className="flex items-center gap-1 ml-4">
                            {!notification.isRead && (
                              <Button 
                                size="sm" 
                                variant="ghost" 
                                onClick={() => markAsRead(notification.id)}
                                className="text-slate-400 hover:text-white"
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                            )}
                            <Button 
                              size="sm" 
                              variant="ghost" 
                              onClick={() => deleteNotification(notification.id)}
                              className="text-slate-400 hover:text-red-400"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                            <Button 
                              size="sm" 
                              variant="ghost" 
                              className="text-slate-400 hover:text-white"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Actions spécifiques au type */}
                        {notification.type === 'friend_request' && (
                          <div className="flex gap-2 mt-3">
                            <Button size="sm" className="bg-green-500 hover:bg-green-600">
                              Accepter
                            </Button>
                            <Button size="sm" variant="outline" className="border-slate-600">
                              Refuser
                            </Button>
                          </div>
                        )}

                        {notification.type === 'collaboration' && (
                          <div className="flex gap-2 mt-3">
                            <Button size="sm" className="bg-blue-500 hover:bg-blue-600">
                              Voir le projet
                            </Button>
                            <Button size="sm" variant="outline" className="border-slate-600">
                              Plus tard
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {filterNotifications(activeTab).length === 0 && (
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardContent className="p-8 text-center">
                    <Bell className="h-16 w-16 mx-auto mb-4 text-slate-500" />
                    <h3 className="text-lg font-medium text-slate-300 mb-2">
                      Aucune notification
                    </h3>
                    <p className="text-slate-400">
                      {activeTab === 'unread' 
                        ? 'Toutes vos notifications sont à jour !' 
                        : 'Vous n\'avez pas encore de notifications de ce type.'}
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>

        {/* Feature Flag Notice */}
        <div className="mt-8">
          <Card className="bg-amber-500/10 border-amber-500/20">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-amber-400">
                <Settings className="h-5 w-5" />
                <span className="font-medium">Version Bêta</span>
              </div>
              <p className="text-amber-300 mt-1">
                Le système de notifications intelligentes est en cours de déploiement. Les notifications push et email seront bientôt disponibles.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
