"use client";

import { AnimatePresence, motion } from 'framer-motion';
import {
  ChevronsRightLeft,
  Maximize2,
  Minimize2,
  Pause,
  Play,
  SkipBack,
  SkipForward,
  Volume2,
  VolumeX,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import * as React from 'react';

import { AddToPlaylistButton } from '@/components/playlists/add-to-playlist-button';
import { LikeButton } from '@/components/social/like-button';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { useSidebar } from '@/components/ui/sidebar-logic';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useUser } from '@/contexts/user-context';
import { useAudioPlayerStore } from '@/lib/stores/audioPlayerStore';
import { type Song } from '@/types/types';

const formatTime = (seconds: number): string => {
  if (isNaN(seconds) || seconds < 0) return '0:00';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

export function GlobalAudioPlayer() {
  const { currentSong, playerMode } = useAudioPlayerStore();
  const { user } = useUser();
  const { state: sidebarState, isMobile } = useSidebar();

  if (!currentSong || !currentSong.id || !user) return null;

  const sidebarWidth = isMobile ? '0px' : sidebarState === 'expanded' ? '16rem' : '4rem';

  const renderPlayer = () => {
    switch (playerMode) {
      case 'normal':
        return <NormalPlayer style={{ left: sidebarWidth }} />;
      case 'mini':
        return <MiniPlayer />;
      case 'mega':
        return <MegaPlayer />;
      default:
        return <NormalPlayer style={{ left: sidebarWidth }} />;
    }
  };

  return <AnimatePresence>{renderPlayer()}</AnimatePresence>;
}

interface PlayerViewProps {
  style?: React.CSSProperties;
}

function NormalPlayer({ style }: PlayerViewProps) {
    const store = useAudioPlayerStore();
    const { currentSong, isPlaying, togglePlayPause, nextSong, previousSong, seek, volume, setVolume, duration, currentTime } = store;

    if (!currentSong) return null;

    return (
        <motion.div
            initial={{ y: '100%' }}
            animate={{ y: '0%' }}
            exit={{ y: '100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            className="fixed bottom-0 right-0 h-24 bg-background/80 backdrop-blur-lg border-t border-border/50 shadow-lg z-50"
            style={style}
        >
            <div className="flex items-center justify-between h-full px-4 gap-4">
                <div className="flex items-center gap-3 w-1/4 min-w-[200px]">
                    <Image src={currentSong.cover_image_path || '/images/placeholder.svg'} alt={currentSong.title || 'Song cover'} width={64} height={64} className="rounded-md aspect-square object-cover" />
                    <div className="flex flex-col overflow-hidden">
                        {currentSong.id ? (
                            <Link href={`/song/${currentSong.id}`} className="font-bold text-base truncate hover:underline">
                                {currentSong.title}
                            </Link>
                        ) : (
                            <span className="font-bold text-base truncate">{currentSong.title}</span>
                        )}
                        {currentSong.user_id ? (
                            <Link href={`/artist/${currentSong.user_id}`} className="text-sm text-muted-foreground truncate hover:underline">
                                {currentSong.artist_name}
                            </Link>
                        ) : (
                            <span className="text-sm text-muted-foreground truncate">{currentSong.artist_name}</span>
                        )}
                    </div>
                </div>

                <div className="flex-1 flex flex-col items-center justify-center gap-2 max-w-xl">
                    <div className="flex items-center gap-4">
                        <Button variant="ghost" size="icon" onClick={previousSong}><SkipBack className="h-6 w-6" /></Button>
                        <Button variant="default" size="icon" className="h-12 w-12 rounded-full" onClick={togglePlayPause}>
                            {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
                        </Button>
                        <Button variant="ghost" size="icon" onClick={nextSong}><SkipForward className="h-6 w-6" /></Button>
                    </div>
                    <div className="w-full flex items-center gap-2 text-xs text-muted-foreground">
                        <span>{formatTime(currentTime)}</span>
                        <Slider
                            value={[currentTime]}
                            max={duration}
                            step={1}
                            onValueChange={(value) => seek(value[0])}
                            className="flex-1"
                        />
                        <span>{formatTime(duration)}</span>
                    </div>
                </div>

                <div className="flex items-center gap-4 w-1/4 justify-end min-w-[200px]">
                    {currentSong.id && <LikeButton songId={currentSong.id} />}
                    {currentSong.id && <AddToPlaylistButton song={currentSong as Song} />}
                    <div className="flex items-center gap-2 w-24">
                        <Button variant="ghost" size="icon" onClick={() => setVolume(volume > 0 ? 0 : 1)} >
                            {volume === 0 ? <VolumeX className="h-5 w-5" /> : <Volume2 className="h-5 w-5" />}
                        </Button>
                        <Slider value={[volume]} max={1} step={0.01} onValueChange={(value) => setVolume(value[0])} />
                    </div>
                    <div className="flex items-center gap-1">
                         <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button variant="ghost" size="icon" onClick={store.cyclePlayerMode}><ChevronsRightLeft className="h-5 w-5" /></Button>
                                </TooltipTrigger>
                                <TooltipContent><p>Vue compacte</p></TooltipContent>
                            </Tooltip>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button variant="ghost" size="icon" onClick={() => store.setPlayerMode('mega')}><Maximize2 className="h-5 w-5" /></Button>
                                </TooltipTrigger>
                                <TooltipContent><p>Plein écran</p></TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                </div>
            </div>
        </motion.div>
    );
}

function MiniPlayer() {
    const store = useAudioPlayerStore();
    const { currentSong, isPlaying, togglePlayPause, nextSong } = store;

    if (!currentSong) return null;

    return (
        <motion.div
            initial={{ y: '100%' }}
            animate={{ y: '0%' }}
            exit={{ y: '100%' }}
            className="fixed bottom-4 right-4 w-auto h-16 bg-background/80 backdrop-blur-lg border rounded-lg shadow-2xl z-50 flex items-center p-2 gap-3"
        >
            <Image src={currentSong.cover_image_path || '/images/placeholder.svg'} alt={currentSong.title || 'Song cover'} width={48} height={48} className="rounded-md aspect-square object-cover" />
            <div className="flex flex-col">
                 <p className="font-bold truncate max-w-[150px]">{currentSong.title}</p>
                 <p className="text-sm text-muted-foreground truncate max-w-[150px]">{currentSong.artist_name}</p>
            </div>
            <div className="flex items-center gap-1">
                <Button variant="ghost" size="icon" onClick={togglePlayPause}>
                    {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
                </Button>
                <Button variant="ghost" size="icon" onClick={nextSong}><SkipForward className="h-5 w-5" /></Button>
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button variant="ghost" size="icon" onClick={store.cyclePlayerMode}><ChevronsRightLeft className="h-5 w-5" /></Button>
                        </TooltipTrigger>
                        <TooltipContent><p>Vue normale</p></TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
        </motion.div>
    );
}

function MegaPlayer() {
    const store = useAudioPlayerStore();
    const { currentSong, isPlaying, togglePlayPause, nextSong, previousSong, seek, duration, currentTime } = store;

    if (!currentSong) return null;

    return (
        <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed inset-0 bg-gradient-to-br from-background to-slate-900 z-[100] flex flex-col items-center justify-center p-8 gap-8"
        >
            <div className="absolute top-4 right-4">
                <Button variant="ghost" size="icon" onClick={() => store.setPlayerMode('normal')}><Minimize2 className="h-6 w-6" /></Button>
            </div>
            
            <motion.div initial={{ scale: 0.8, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} transition={{ delay: 0.1, type: 'spring' }}>
                <Image src={currentSong.cover_image_path || '/images/placeholder.svg'} alt={currentSong.title || 'Song cover'} width={400} height={400} className="rounded-lg shadow-2xl aspect-square object-cover" />
            </motion.div>
            
            <div className="text-center">
                {currentSong.id ? (
                    <Link href={`/song/${currentSong.id}`} className="text-4xl font-bold hover:underline">
                        {currentSong.title}
                    </Link>
                ) : (
                    <h1 className="text-4xl font-bold">{currentSong.title}</h1>
                )}
                {currentSong.user_id ? (
                    <Link href={`/artist/${currentSong.user_id}`} className="text-2xl text-muted-foreground hover:underline">
                        {currentSong.artist_name}
                    </Link>
                ) : (
                    <p className="text-2xl text-muted-foreground">{currentSong.artist_name}</p>
                )}
            </div>

            <div className="w-full max-w-2xl flex flex-col gap-4">
                <div className="w-full flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{formatTime(currentTime)}</span>
                    <Slider value={[currentTime]} max={duration} step={1} onValueChange={(value) => seek(value[0])} className="flex-1" />
                    <span>{formatTime(duration)}</span>
                </div>
                <div className="flex justify-center items-center gap-6">
                    <Button variant="ghost" size="icon" onClick={previousSong}><SkipBack className="h-8 w-8" /></Button>
                    <Button variant="default" size="icon" className="h-20 w-20 rounded-full shadow-lg" onClick={togglePlayPause}>
                        {isPlaying ? <Pause className="h-10 w-10" /> : <Play className="h-10 w-10" />}
                    </Button>
                    <Button variant="ghost" size="icon" onClick={nextSong}><SkipForward className="h-8 w-8" /></Button>
                </div>
            </div>

            <div className="flex items-center gap-6">
                {currentSong.id && <LikeButton songId={currentSong.id} size="lg" />}
                {currentSong.id && <AddToPlaylistButton song={currentSong as Song} size="lg" />}
            </div>

        </motion.div>
    );
}

export default GlobalAudioPlayer;
