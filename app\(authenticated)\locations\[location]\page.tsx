"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  MapPin, 
  Users, 
  Music, 
  Calendar, 
  Heart, 
  Share2, 
  Plus,
  TrendingUp,
  Star,
  Radio,
  Mic,
  Building,
  Clock,
  ExternalLink,
  Upload,
  PlusCircle,
  Disc3,
  PartyPopper,
  Coffee,
  Volume2
} from 'lucide-react';

interface LocationPageProps {
  params: {
    location: string;
  };
}

export default function LocationPage({ params }: LocationPageProps) {
  const [activeTab, setActiveTab] = useState('overview');
  
  // Décoder la localisation depuis l'URL
  const locationName = decodeURIComponent(params.location)
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  // Données de démonstration pour la localisation
  const locationData = {
    name: locationName,
    country: 'France', // Peut être déterminé dynamiquement
    description: `Découvrez la scène musicale vibrante de ${locationName}, ses artistes locaux, ses événements et ses lieux emblématiques.`,
    stats: {
      artists: 342,
      venues: 28,
      events: 156,
      songs: 2340,
      followers: 15670
    },
    localArtists: [
      { id: '1', name: 'Alice Martin', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face', genre: 'Jazz', profileUrl: '/profile/alice-martin' },
      { id: '2', name: 'Bob Wilson', avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face', genre: 'Electronic', profileUrl: '/profile/bob-wilson' },
      { id: '3', name: 'Luna Collective', avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face', genre: 'Ambient', profileUrl: '/profile/luna-collective' }
    ],
    upcomingEvents: [
      { id: '1', title: 'Jazz Night', venue: 'Le Blue Note', date: '2024-07-15', time: '20:00', price: '25€' },
      { id: '2', title: 'Electronic Festival', venue: 'Warehouse Club', date: '2024-07-20', time: '22:00', price: '35€' },
      { id: '3', title: 'Acoustic Session', venue: 'Café des Arts', date: '2024-07-25', time: '19:00', price: 'Gratuit' }
    ],
    venues: [
      { id: '1', name: 'Le Blue Note', type: 'Club de Jazz', capacity: 150, rating: 4.8 },
      { id: '2', name: 'Warehouse Club', type: 'Club Electronic', capacity: 500, rating: 4.6 },
      { id: '3', name: 'Café des Arts', type: 'Café Concert', capacity: 80, rating: 4.7 },
      { id: '4', name: 'Studio Central', type: 'Studio d\'enregistrement', capacity: 20, rating: 4.9 }
    ],
    localSongs: [
      { id: '1', title: 'Paris Nights', artist: 'Alice Martin', plays: 12340, url: '/songs/paris-nights' },
      { id: '2', title: 'City Lights', artist: 'Bob Wilson', plays: 8920, url: '/songs/city-lights' },
      { id: '3', title: 'Urban Dreams', artist: 'Luna Collective', plays: 15670, url: '/songs/urban-dreams' }
    ]
  };

  const getLocationEmoji = (location: string) => {
    const emojis = {
      'paris': '🗼',
      'lyon': '🦁',
      'marseille': '⛵',
      'toulouse': '🌸',
      'nice': '🌴',
      'nantes': '🏰',
      'strasbourg': '🏛️',
      'montpellier': '☀️',
      'bordeaux': '🍷',
      'lille': '🏭',
      'london': '🇬🇧',
      'berlin': '🇩🇪',
      'madrid': '🇪🇸',
      'rome': '🇮🇹',
      'amsterdam': '🇳🇱'
    };
    return emojis[location.toLowerCase() as keyof typeof emojis] || '🏙️';
  };

  const getLocationColor = (location: string) => {
    const colors = {
      'paris': 'from-blue-500 to-purple-600',
      'lyon': 'from-red-500 to-orange-600',
      'marseille': 'from-cyan-500 to-blue-600',
      'toulouse': 'from-pink-500 to-rose-600',
      'nice': 'from-yellow-500 to-orange-600',
      'nantes': 'from-green-500 to-emerald-600',
      'strasbourg': 'from-indigo-500 to-purple-600',
      'montpellier': 'from-orange-500 to-red-600',
      'bordeaux': 'from-purple-500 to-pink-600',
      'lille': 'from-gray-500 to-slate-600'
    };
    return colors[location.toLowerCase() as keyof typeof colors] || 'from-slate-500 to-slate-600';
  };

  const getVenueIcon = (type: string) => {
    if (type.includes('Jazz') || type.includes('Club')) return Volume2;
    if (type.includes('Café')) return Coffee;
    if (type.includes('Studio')) return Mic;
    if (type.includes('Festival')) return PartyPopper;
    return Building;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header avec gradient de la localisation */}
        <div className={`relative mb-8 rounded-2xl bg-gradient-to-r ${getLocationColor(locationName)} p-8 text-white overflow-hidden`}>
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <div className="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm text-4xl">
                  {getLocationEmoji(locationName)}
                </div>
                <div>
                  <h1 className="text-5xl font-bold mb-2">{locationName}</h1>
                  <p className="text-xl opacity-90 mb-4">{locationData.description}</p>
                  <div className="flex items-center gap-6">
                    <div className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      <span>{locationData.stats.artists} artistes</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Building className="h-5 w-5" />
                      <span>{locationData.stats.venues} lieux</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      <span>{locationData.stats.events} événements</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Music className="h-5 w-5" />
                      <span>{locationData.stats.songs} morceaux</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Heart className="h-5 w-5" />
                      <span>{locationData.stats.followers.toLocaleString()} followers</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex gap-3">
                <Button size="lg" className="bg-white/20 hover:bg-white/30 backdrop-blur-sm">
                  <Heart className="h-5 w-5 mr-2" />
                  Suivre
                </Button>
                <Button size="lg" className="bg-white/20 hover:bg-white/30 backdrop-blur-sm">
                  <Share2 className="h-5 w-5 mr-2" />
                  Partager
                </Button>
              </div>
            </div>
          </div>
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
        </div>

        {/* Onglets de contenu */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="bg-slate-800 border-slate-700 grid grid-cols-5 w-full">
            <TabsTrigger value="overview" className="data-[state=active]:bg-slate-700">
              <TrendingUp className="h-4 w-4 mr-2" />
              Vue d'ensemble
            </TabsTrigger>
            <TabsTrigger value="artists" className="data-[state=active]:bg-slate-700">
              <Users className="h-4 w-4 mr-2" />
              Artistes Locaux
            </TabsTrigger>
            <TabsTrigger value="events" className="data-[state=active]:bg-slate-700">
              <Calendar className="h-4 w-4 mr-2" />
              Événements
            </TabsTrigger>
            <TabsTrigger value="venues" className="data-[state=active]:bg-slate-700">
              <Building className="h-4 w-4 mr-2" />
              Lieux
            </TabsTrigger>
            <TabsTrigger value="community" className="data-[state=active]:bg-slate-700">
              <Heart className="h-4 w-4 mr-2" />
              Communauté
            </TabsTrigger>
          </TabsList>

          {/* Vue d'ensemble */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Artistes locaux */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Star className="h-5 w-5 text-yellow-400" />
                    Artistes Locaux en Vue
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {locationData.localArtists.map((artist, index) => (
                    <div key={artist.id} className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-colors cursor-pointer">
                      <div className="flex items-center gap-3 flex-1">
                        <span className="text-slate-400 font-bold text-lg w-6">#{index + 1}</span>
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={artist.avatar} />
                          <AvatarFallback className="bg-slate-600 text-white">
                            {artist.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <Button
                            variant="link"
                            className="p-0 h-auto font-medium text-white hover:text-blue-400"
                            onClick={() => window.open(artist.profileUrl, '_blank')}
                          >
                            {artist.name}
                            <ExternalLink className="h-3 w-3 ml-1" />
                          </Button>
                          <p className="text-slate-400 text-sm">{artist.genre}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                  <Button className="w-full bg-blue-500 hover:bg-blue-600">
                    <Users className="h-4 w-4 mr-2" />
                    Découvrir tous les artistes
                  </Button>
                </CardContent>
              </Card>

              {/* Événements à venir */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-green-400" />
                    Événements à Venir
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {locationData.upcomingEvents.map((event) => (
                    <div key={event.id} className="p-3 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-colors">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium text-white">{event.title}</h4>
                        <Badge variant="outline" className="text-xs border-green-500/30 text-green-400">
                          {event.price}
                        </Badge>
                      </div>
                      <p className="text-slate-400 text-sm mb-1">{event.venue}</p>
                      <div className="flex items-center gap-4 text-xs text-slate-500">
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(event.date).toLocaleDateString('fr-FR')}
                        </span>
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {event.time}
                        </span>
                      </div>
                    </div>
                  ))}
                  <Button className="w-full bg-green-500 hover:bg-green-600">
                    <Calendar className="h-4 w-4 mr-2" />
                    Voir tous les événements
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Lieux emblématiques et morceaux locaux */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
              {/* Lieux emblématiques */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Building className="h-5 w-5 text-purple-400" />
                    Lieux Emblématiques
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {locationData.venues.slice(0, 3).map((venue) => {
                    const VenueIcon = getVenueIcon(venue.type);
                    return (
                      <div key={venue.id} className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-colors">
                        <div className="w-10 h-10 bg-slate-600 rounded-lg flex items-center justify-center">
                          <VenueIcon className="h-5 w-5 text-slate-300" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-white">{venue.name}</h4>
                          <p className="text-slate-400 text-sm">{venue.type}</p>
                          <div className="flex items-center gap-4 text-xs text-slate-500 mt-1">
                            <span>{venue.capacity} places</span>
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3 text-yellow-400" />
                              <span>{venue.rating}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  <Button className="w-full bg-purple-500 hover:bg-purple-600">
                    <Building className="h-4 w-4 mr-2" />
                    Explorer tous les lieux
                  </Button>
                </CardContent>
              </Card>

              {/* Morceaux locaux */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Music className="h-5 w-5 text-pink-400" />
                    Sons de {locationName}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {locationData.localSongs.map((song) => (
                    <div key={song.id} className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-colors">
                      <div className="w-10 h-10 bg-slate-600 rounded-lg flex items-center justify-center">
                        <Music className="h-5 w-5 text-slate-300" />
                      </div>
                      <div className="flex-1">
                        <Button
                          variant="link"
                          className="p-0 h-auto font-medium text-white hover:text-blue-400"
                          onClick={() => window.open(song.url, '_blank')}
                        >
                          {song.title}
                          <ExternalLink className="h-3 w-3 ml-1" />
                        </Button>
                        <p className="text-slate-400 text-sm">{song.artist}</p>
                        <p className="text-slate-500 text-xs">{song.plays.toLocaleString()} écoutes</p>
                      </div>
                    </div>
                  ))}
                  <Button className="w-full bg-pink-500 hover:bg-pink-600">
                    <Music className="h-4 w-4 mr-2" />
                    Découvrir plus de sons locaux
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Autres onglets avec invitations à contribuer */}
          <TabsContent value="artists">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-12 text-center">
                <Users className="h-24 w-24 mx-auto mb-6 text-slate-500" />
                <h3 className="text-2xl font-bold text-white mb-4">Scène artistique de {locationName}</h3>
                <p className="text-slate-400 mb-6 max-w-md mx-auto">
                  Rejoignez la communauté d'artistes de {locationName} et connectez-vous avec des talents locaux !
                </p>
                <div className="flex gap-4 justify-center">
                  <Button size="lg" className="bg-blue-500 hover:bg-blue-600">
                    <Star className="h-5 w-5 mr-2" />
                    Rejoindre la scène locale
                  </Button>
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300">
                    <Users className="h-5 w-5 mr-2" />
                    Trouver des collaborateurs
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="events">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-12 text-center">
                <Calendar className="h-24 w-24 mx-auto mb-6 text-slate-500" />
                <h3 className="text-2xl font-bold text-white mb-4">Organisez votre événement à {locationName}</h3>
                <p className="text-slate-400 mb-6 max-w-md mx-auto">
                  Créez des événements musicaux et rassemblez la communauté locale autour de votre passion !
                </p>
                <div className="flex gap-4 justify-center">
                  <Button size="lg" className="bg-green-500 hover:bg-green-600">
                    <Plus className="h-5 w-5 mr-2" />
                    Créer un événement
                  </Button>
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300">
                    <Calendar className="h-5 w-5 mr-2" />
                    Proposer un lieu
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="venues">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-12 text-center">
                <Building className="h-24 w-24 mx-auto mb-6 text-slate-500" />
                <h3 className="text-2xl font-bold text-white mb-4">Lieux musicaux de {locationName}</h3>
                <p className="text-slate-400 mb-6 max-w-md mx-auto">
                  Référencez votre lieu musical et accueillez des artistes dans votre espace !
                </p>
                <div className="flex gap-4 justify-center">
                  <Button size="lg" className="bg-purple-500 hover:bg-purple-600">
                    <Building className="h-5 w-5 mr-2" />
                    Ajouter un lieu
                  </Button>
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300">
                    <MapPin className="h-5 w-5 mr-2" />
                    Explorer la carte
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="community">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-12 text-center">
                <Heart className="h-24 w-24 mx-auto mb-6 text-slate-500" />
                <h3 className="text-2xl font-bold text-white mb-4">Communauté musicale de {locationName}</h3>
                <p className="text-slate-400 mb-6 max-w-md mx-auto">
                  Rejoignez la communauté locale et participez à la vie musicale de votre ville !
                </p>
                <div className="flex gap-4 justify-center">
                  <Button size="lg" className="bg-pink-500 hover:bg-pink-600">
                    <Heart className="h-5 w-5 mr-2" />
                    Rejoindre la communauté
                  </Button>
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300">
                    <Share2 className="h-5 w-5 mr-2" />
                    Organiser une rencontre
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
