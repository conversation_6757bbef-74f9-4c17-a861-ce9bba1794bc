"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Guitar, 
  Users, 
  Music, 
  Play, 
  Heart, 
  Share2, 
  Plus,
  TrendingUp,
  Star,
  Headphones,
  Radio,
  Mic,
  Piano,
  Calendar,
  MapPin,
  ExternalLink,
  Upload,
  PlusCircle,
  Volume2,
  Disc3
} from 'lucide-react';

interface InstrumentPageProps {
  params: {
    instrument: string;
  };
}

export default function InstrumentPage({ params }: InstrumentPageProps) {
  const [activeTab, setActiveTab] = useState('overview');
  
  // Décoder l'instrument depuis l'URL
  const instrumentName = decodeURIComponent(params.instrument)
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  // Données de démonstration pour l'instrument
  const instrumentData = {
    name: instrumentName,
    description: `Explorez l'univers du ${instrumentName}, découvrez les artistes qui le maîtrisent et les morceaux qui le mettent en valeur.`,
    stats: {
      artists: 234,
      songs: 1567,
      albums: 123,
      tutorials: 45,
      followers: 8920
    },
    topArtists: [
      { id: '1', name: 'Alice Martin', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face', level: 'Expert', profileUrl: '/profile/alice-martin' },
      { id: '2', name: 'Bob Wilson', avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face', level: 'Avancé', profileUrl: '/profile/bob-wilson' },
      { id: '3', name: 'Luna Collective', avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face', level: 'Professionnel', profileUrl: '/profile/luna-collective' }
    ],
    featuredSongs: [
      { id: '1', title: 'Piano Dreams', artist: 'Alice Martin', cover: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop', instrument_focus: 'Solo', url: '/songs/piano-dreams' },
      { id: '2', title: 'Guitar Harmony', artist: 'Bob Wilson', cover: 'https://images.unsplash.com/photo-1571974599782-87624638275c?w=300&h=300&fit=crop', instrument_focus: 'Lead', url: '/songs/guitar-harmony' },
      { id: '3', title: 'Synth Waves', artist: 'Luna Collective', cover: 'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?w=300&h=300&fit=crop', instrument_focus: 'Ambient', url: '/songs/synth-waves' }
    ],
    tutorials: [
      { id: '1', title: `Débuter le ${instrumentName}`, level: 'Débutant', duration: '15 min', views: 2340 },
      { id: '2', title: `Techniques avancées ${instrumentName}`, level: 'Avancé', duration: '25 min', views: 1890 },
      { id: '3', title: `Improvisation ${instrumentName}`, level: 'Expert', duration: '30 min', views: 1456 }
    ]
  };

  const getInstrumentIcon = (instrument: string) => {
    const icons = {
      'piano': Piano,
      'guitare': Guitar,
      'guitar': Guitar,
      'synthé': Volume2,
      'synthe': Volume2,
      'synthesizer': Volume2,
      'drum': Radio,
      'batterie': Radio,
      'violon': Music,
      'violin': Music,
      'basse': Guitar,
      'bass': Guitar
    };
    return icons[instrument.toLowerCase() as keyof typeof icons] || Guitar;
  };

  const getInstrumentColor = (instrument: string) => {
    const colors = {
      'piano': 'from-purple-500 to-indigo-600',
      'guitare': 'from-orange-500 to-red-600',
      'guitar': 'from-orange-500 to-red-600',
      'synthé': 'from-blue-500 to-cyan-600',
      'synthe': 'from-blue-500 to-cyan-600',
      'synthesizer': 'from-blue-500 to-cyan-600',
      'drum': 'from-red-500 to-pink-600',
      'batterie': 'from-red-500 to-pink-600',
      'violon': 'from-green-500 to-emerald-600',
      'violin': 'from-green-500 to-emerald-600',
      'basse': 'from-yellow-500 to-orange-600',
      'bass': 'from-yellow-500 to-orange-600'
    };
    return colors[instrument.toLowerCase() as keyof typeof colors] || 'from-slate-500 to-slate-600';
  };

  const InstrumentIcon = getInstrumentIcon(instrumentName);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header avec gradient de l'instrument */}
        <div className={`relative mb-8 rounded-2xl bg-gradient-to-r ${getInstrumentColor(instrumentName)} p-8 text-white overflow-hidden`}>
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <div className="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                  <InstrumentIcon className="h-12 w-12" />
                </div>
                <div>
                  <h1 className="text-5xl font-bold mb-2">{instrumentName}</h1>
                  <p className="text-xl opacity-90 mb-4">{instrumentData.description}</p>
                  <div className="flex items-center gap-6">
                    <div className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      <span>{instrumentData.stats.artists} artistes</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Music className="h-5 w-5" />
                      <span>{instrumentData.stats.songs} morceaux</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Disc3 className="h-5 w-5" />
                      <span>{instrumentData.stats.albums} albums</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Play className="h-5 w-5" />
                      <span>{instrumentData.stats.tutorials} tutoriels</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Heart className="h-5 w-5" />
                      <span>{instrumentData.stats.followers.toLocaleString()} passionnés</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex gap-3">
                <Button size="lg" className="bg-white/20 hover:bg-white/30 backdrop-blur-sm">
                  <Heart className="h-5 w-5 mr-2" />
                  Suivre
                </Button>
                <Button size="lg" className="bg-white/20 hover:bg-white/30 backdrop-blur-sm">
                  <Share2 className="h-5 w-5 mr-2" />
                  Partager
                </Button>
              </div>
            </div>
          </div>
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
        </div>

        {/* Onglets de contenu */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="bg-slate-800 border-slate-700 grid grid-cols-5 w-full">
            <TabsTrigger value="overview" className="data-[state=active]:bg-slate-700">
              <TrendingUp className="h-4 w-4 mr-2" />
              Vue d'ensemble
            </TabsTrigger>
            <TabsTrigger value="artists" className="data-[state=active]:bg-slate-700">
              <Users className="h-4 w-4 mr-2" />
              Artistes
            </TabsTrigger>
            <TabsTrigger value="songs" className="data-[state=active]:bg-slate-700">
              <Music className="h-4 w-4 mr-2" />
              Morceaux
            </TabsTrigger>
            <TabsTrigger value="tutorials" className="data-[state=active]:bg-slate-700">
              <Play className="h-4 w-4 mr-2" />
              Tutoriels
            </TabsTrigger>
            <TabsTrigger value="community" className="data-[state=active]:bg-slate-700">
              <Heart className="h-4 w-4 mr-2" />
              Communauté
            </TabsTrigger>
          </TabsList>

          {/* Vue d'ensemble */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Artistes */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Star className="h-5 w-5 text-yellow-400" />
                    Maîtres du {instrumentName}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {instrumentData.topArtists.map((artist, index) => (
                    <div key={artist.id} className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-colors cursor-pointer">
                      <div className="flex items-center gap-3 flex-1">
                        <span className="text-slate-400 font-bold text-lg w-6">#{index + 1}</span>
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={artist.avatar} />
                          <AvatarFallback className="bg-slate-600 text-white">
                            {artist.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <Button
                            variant="link"
                            className="p-0 h-auto font-medium text-white hover:text-blue-400"
                            onClick={() => window.open(artist.profileUrl, '_blank')}
                          >
                            {artist.name}
                            <ExternalLink className="h-3 w-3 ml-1" />
                          </Button>
                          <p className="text-slate-400 text-sm">Niveau {artist.level}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                  <Button className="w-full bg-blue-500 hover:bg-blue-600">
                    <Users className="h-4 w-4 mr-2" />
                    Découvrir plus d'artistes
                  </Button>
                </CardContent>
              </Card>

              {/* Morceaux en vedette */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Music className="h-5 w-5 text-green-400" />
                    Morceaux en Vedette
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {instrumentData.featuredSongs.map((song) => (
                    <div key={song.id} className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-colors">
                      <div className="w-12 h-12 bg-slate-600 rounded-lg flex items-center justify-center">
                        <InstrumentIcon className="h-6 w-6 text-slate-300" />
                      </div>
                      <div className="flex-1">
                        <Button
                          variant="link"
                          className="p-0 h-auto font-medium text-white hover:text-blue-400"
                          onClick={() => window.open(song.url, '_blank')}
                        >
                          {song.title}
                          <ExternalLink className="h-3 w-3 ml-1" />
                        </Button>
                        <p className="text-slate-400 text-sm">{song.artist}</p>
                        <Badge variant="outline" className="text-xs mt-1 border-green-500/30 text-green-400">
                          {song.instrument_focus}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  <Button className="w-full bg-green-500 hover:bg-green-600">
                    <Music className="h-4 w-4 mr-2" />
                    Explorer plus de morceaux
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Tutoriels populaires */}
            <Card className="bg-slate-800/50 border-slate-700 mt-6">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Play className="h-5 w-5 text-purple-400" />
                  Tutoriels Populaires
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {instrumentData.tutorials.map((tutorial) => (
                    <div key={tutorial.id} className="p-4 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-colors cursor-pointer">
                      <div className="flex items-center gap-2 mb-2">
                        <Play className="h-4 w-4 text-purple-400" />
                        <Badge variant="outline" className="text-xs border-purple-500/30 text-purple-400">
                          {tutorial.level}
                        </Badge>
                      </div>
                      <h4 className="font-medium text-white mb-1">{tutorial.title}</h4>
                      <div className="flex items-center justify-between text-sm text-slate-400">
                        <span>{tutorial.duration}</span>
                        <span>{tutorial.views.toLocaleString()} vues</span>
                      </div>
                    </div>
                  ))}
                </div>
                <Button className="w-full mt-4 bg-purple-500 hover:bg-purple-600">
                  <Play className="h-4 w-4 mr-2" />
                  Voir tous les tutoriels
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Autres onglets avec invitations à contribuer */}
          <TabsContent value="artists">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-12 text-center">
                <Users className="h-24 w-24 mx-auto mb-6 text-slate-500" />
                <h3 className="text-2xl font-bold text-white mb-4">Communauté {instrumentName} en croissance</h3>
                <p className="text-slate-400 mb-6 max-w-md mx-auto">
                  Rejoignez les artistes passionnés de {instrumentName} et partagez votre talent avec la communauté !
                </p>
                <div className="flex gap-4 justify-center">
                  <Button size="lg" className="bg-blue-500 hover:bg-blue-600">
                    <Star className="h-5 w-5 mr-2" />
                    Devenir artiste {instrumentName}
                  </Button>
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300">
                    <Users className="h-5 w-5 mr-2" />
                    Trouver des collaborateurs
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="songs">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-12 text-center">
                <Music className="h-24 w-24 mx-auto mb-6 text-slate-500" />
                <h3 className="text-2xl font-bold text-white mb-4">Partagez votre talent au {instrumentName}</h3>
                <p className="text-slate-400 mb-6 max-w-md mx-auto">
                  Uploadez vos morceaux mettant en valeur le {instrumentName} et inspirez d'autres musiciens !
                </p>
                <div className="flex gap-4 justify-center">
                  <Button size="lg" className="bg-green-500 hover:bg-green-600">
                    <Upload className="h-5 w-5 mr-2" />
                    Uploader un morceau
                  </Button>
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300">
                    <PlusCircle className="h-5 w-5 mr-2" />
                    Créer une collaboration
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tutorials">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-12 text-center">
                <Play className="h-24 w-24 mx-auto mb-6 text-slate-500" />
                <h3 className="text-2xl font-bold text-white mb-4">Partagez votre savoir-faire</h3>
                <p className="text-slate-400 mb-6 max-w-md mx-auto">
                  Créez des tutoriels {instrumentName} et aidez d'autres musiciens à progresser !
                </p>
                <div className="flex gap-4 justify-center">
                  <Button size="lg" className="bg-purple-500 hover:bg-purple-600">
                    <Play className="h-5 w-5 mr-2" />
                    Créer un tutoriel
                  </Button>
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300">
                    <Heart className="h-5 w-5 mr-2" />
                    Demander un tutoriel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="community">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-12 text-center">
                <Heart className="h-24 w-24 mx-auto mb-6 text-slate-500" />
                <h3 className="text-2xl font-bold text-white mb-4">Communauté {instrumentName} passionnée</h3>
                <p className="text-slate-400 mb-6 max-w-md mx-auto">
                  Connectez-vous avec d'autres passionnés de {instrumentName} et échangez conseils et expériences !
                </p>
                <div className="flex gap-4 justify-center">
                  <Button size="lg" className="bg-pink-500 hover:bg-pink-600">
                    <Heart className="h-5 w-5 mr-2" />
                    Rejoindre la communauté
                  </Button>
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300">
                    <Share2 className="h-5 w-5 mr-2" />
                    Organiser un événement
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
