-- 📊 SYSTÈME DE MÉTRIQUES UNIFIÉ - MOUVIK
-- Version: 1.0 - Juillet 2024
-- Description: Système complet de tracking et analytics pour tous les contenus

-- ============================================================================
-- 1. TABLE PRINCIPALE DES MÉTRIQUES UNIFIÉES
-- ============================================================================

-- Table unifiée pour toutes les métriques
CREATE TABLE IF NOT EXISTS unified_metrics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Identification de la ressource
  resource_type VARCHAR(20) NOT NULL CHECK (resource_type IN (
    'song', 'album', 'playlist', 'profile', 'band', 'community', 'comment', 'message'
  )),
  resource_id UUID NOT NULL,
  
  -- Type de métrique
  metric_type VARCHAR(20) NOT NULL CHECK (metric_type IN (
    'play', 'view', 'like', 'share', 'download', 'comment', 'follow', 'unfollow',
    'add_to_playlist', 'remove_from_playlist', 'collaboration_request', 'message_sent'
  )),
  
  -- Informations utilisateur (optionnel pour les vues anonymes)
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id VARCHAR(100),
  
  -- Informations techniques
  ip_address INET,
  user_agent TEXT,
  referrer TEXT,
  
  -- Données spécifiques à la métrique
  duration INTEGER, -- Pour les plays (durée d'écoute en secondes)
  quality VARCHAR(10), -- Pour les plays (qualité audio: low, medium, high)
  completion_rate DECIMAL(5,2), -- Pourcentage de completion (0-100)
  
  -- Informations contextuelles
  device_type VARCHAR(20) CHECK (device_type IN ('mobile', 'desktop', 'tablet', 'smart_speaker')),
  platform VARCHAR(20), -- web, ios, android, etc.
  location_country VARCHAR(2), -- Code pays ISO
  location_city VARCHAR(100),
  
  -- Métadonnées additionnelles (JSON flexible)
  metadata JSONB DEFAULT '{}'::jsonb,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Index pour les requêtes fréquentes
  CONSTRAINT unique_user_resource_metric UNIQUE (user_id, resource_type, resource_id, metric_type, created_at)
);

-- ============================================================================
-- 2. TABLES DE MÉTADONNÉES ENRICHIES
-- ============================================================================

-- Métadonnées enrichies pour les genres
CREATE TABLE IF NOT EXISTS genre_metadata (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  genre_name VARCHAR(100) NOT NULL UNIQUE,
  display_name VARCHAR(100) NOT NULL,
  description TEXT,
  
  -- Caractéristiques musicales
  characteristics JSONB DEFAULT '{}'::jsonb, -- ["Rythmes dynamiques", "Mélodies expressives"]
  typical_instruments TEXT[], -- ["Guitare", "Basse", "Batterie"]
  bpm_range INT4RANGE, -- [80, 140]
  key_signatures VARCHAR(10)[], -- ["Em", "Am", "Dm"]
  related_genres VARCHAR(100)[], -- ["Alternative", "Indie"]
  
  -- Métriques de popularité
  popularity_score INTEGER DEFAULT 0 CHECK (popularity_score >= 0 AND popularity_score <= 100),
  trending_score DECIMAL(5,2) DEFAULT 0,
  weekly_growth_rate DECIMAL(5,2) DEFAULT 0,
  
  -- Statistiques
  total_songs INTEGER DEFAULT 0,
  total_artists INTEGER DEFAULT 0,
  total_plays BIGINT DEFAULT 0,
  
  -- Configuration
  is_active BOOLEAN DEFAULT TRUE,
  featured_order INTEGER DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Métadonnées enrichies pour les relations d'amitié
CREATE TABLE IF NOT EXISTS relationship_metadata (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  friendship_id UUID NOT NULL REFERENCES friendships(id) ON DELETE CASCADE,
  
  -- Type de relation enrichi
  relationship_type VARCHAR(50) NOT NULL CHECK (relationship_type IN (
    'close_friend', 'friend', 'acquaintance',
    'collaborator', 'band_member', 'producer', 'manager',
    'mentor', 'student', 'inspiration',
    'fan', 'supporter', 'follower',
    'sound_engineer', 'lyricist', 'composer', 'instrumentalist'
  )),
  
  -- Historique de collaboration
  collaboration_history JSONB DEFAULT '[]'::jsonb,
  shared_projects UUID[], -- IDs des projets partagés
  
  -- Métriques d'interaction
  interaction_frequency INTEGER DEFAULT 0, -- Messages/interactions par mois
  last_interaction TIMESTAMP WITH TIME ZONE,
  total_collaborations INTEGER DEFAULT 0,
  
  -- Notes et tags personnalisés
  notes TEXT,
  custom_tags TEXT[],
  
  -- Métadonnées
  metadata JSONB DEFAULT '{}'::jsonb,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(friendship_id)
);

-- Analytics pour les communautés
CREATE TABLE IF NOT EXISTS community_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  community_id UUID NOT NULL REFERENCES tag_communities(id) ON DELETE CASCADE,
  
  -- Métriques d'engagement
  daily_active_users INTEGER DEFAULT 0,
  weekly_active_users INTEGER DEFAULT 0,
  monthly_active_users INTEGER DEFAULT 0,
  
  -- Métriques de contenu
  content_creation_rate DECIMAL(5,2) DEFAULT 0, -- Contenus créés par jour
  engagement_score DECIMAL(5,2) DEFAULT 0, -- Score d'engagement global
  collaboration_rate DECIMAL(5,2) DEFAULT 0, -- Collaborations par membre
  
  -- Métriques de croissance
  growth_rate DECIMAL(5,2) DEFAULT 0, -- Croissance hebdomadaire
  retention_rate DECIMAL(5,2) DEFAULT 0, -- Taux de rétention
  
  -- Contenu tendance
  trending_topics TEXT[],
  top_contributors UUID[], -- IDs des contributeurs principaux
  
  -- Date de la mesure
  date DATE NOT NULL,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(community_id, date)
);

-- ============================================================================
-- 3. VUES MATÉRIALISÉES POUR PERFORMANCES
-- ============================================================================

-- Vue pour les statistiques en temps réel par ressource
CREATE MATERIALIZED VIEW IF NOT EXISTS resource_stats_realtime AS
SELECT 
  resource_type,
  resource_id,
  COUNT(*) FILTER (WHERE metric_type = 'play') as total_plays,
  COUNT(*) FILTER (WHERE metric_type = 'view') as total_views,
  COUNT(*) FILTER (WHERE metric_type = 'like') as total_likes,
  COUNT(*) FILTER (WHERE metric_type = 'share') as total_shares,
  COUNT(*) FILTER (WHERE metric_type = 'download') as total_downloads,
  COUNT(*) FILTER (WHERE metric_type = 'comment') as total_comments,
  COUNT(DISTINCT user_id) as unique_users,
  AVG(duration) FILTER (WHERE metric_type = 'play' AND duration IS NOT NULL) as avg_play_duration,
  AVG(completion_rate) FILTER (WHERE completion_rate IS NOT NULL) as avg_completion_rate,
  MAX(created_at) as last_activity
FROM unified_metrics 
GROUP BY resource_type, resource_id;

-- Index unique pour la vue matérialisée
CREATE UNIQUE INDEX IF NOT EXISTS idx_resource_stats_realtime_unique 
ON resource_stats_realtime (resource_type, resource_id);

-- Vue pour les tendances par genre
CREATE MATERIALIZED VIEW IF NOT EXISTS genre_trends AS
SELECT 
  gm.genre_name,
  gm.display_name,
  COUNT(DISTINCT s.id) as song_count,
  COUNT(DISTINCT s.user_id) as artist_count,
  SUM(rs.total_plays) as total_plays,
  SUM(rs.total_likes) as total_likes,
  AVG(rs.avg_completion_rate) as avg_completion_rate,
  
  -- Calcul du score de tendance (basé sur activité récente)
  (
    COUNT(*) FILTER (WHERE um.created_at > NOW() - INTERVAL '7 days') * 1.0 /
    NULLIF(COUNT(*) FILTER (WHERE um.created_at > NOW() - INTERVAL '14 days'), 0)
  ) as weekly_trend_score
  
FROM genre_metadata gm
LEFT JOIN songs s ON s.genre ILIKE '%' || gm.genre_name || '%'
LEFT JOIN resource_stats_realtime rs ON rs.resource_type = 'song' AND rs.resource_id = s.id
LEFT JOIN unified_metrics um ON um.resource_type = 'song' AND um.resource_id = s.id
GROUP BY gm.id, gm.genre_name, gm.display_name;

-- ============================================================================
-- 4. INDEX POUR PERFORMANCES
-- ============================================================================

-- Index principaux pour unified_metrics
CREATE INDEX IF NOT EXISTS idx_unified_metrics_resource ON unified_metrics(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_unified_metrics_user ON unified_metrics(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_unified_metrics_type ON unified_metrics(metric_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_unified_metrics_date ON unified_metrics(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_unified_metrics_composite ON unified_metrics(resource_type, resource_id, metric_type, created_at DESC);

-- Index pour les requêtes de tendances
CREATE INDEX IF NOT EXISTS idx_unified_metrics_recent ON unified_metrics(created_at DESC) WHERE created_at > NOW() - INTERVAL '30 days';
CREATE INDEX IF NOT EXISTS idx_unified_metrics_plays ON unified_metrics(resource_type, resource_id, duration) WHERE metric_type = 'play';

-- Index pour genre_metadata
CREATE INDEX IF NOT EXISTS idx_genre_metadata_popularity ON genre_metadata(popularity_score DESC, trending_score DESC);
CREATE INDEX IF NOT EXISTS idx_genre_metadata_active ON genre_metadata(is_active, featured_order);

-- Index pour relationship_metadata
CREATE INDEX IF NOT EXISTS idx_relationship_metadata_type ON relationship_metadata(relationship_type);
CREATE INDEX IF NOT EXISTS idx_relationship_metadata_interaction ON relationship_metadata(last_interaction DESC);

-- Index pour community_analytics
CREATE INDEX IF NOT EXISTS idx_community_analytics_date ON community_analytics(date DESC);
CREATE INDEX IF NOT EXISTS idx_community_analytics_engagement ON community_analytics(engagement_score DESC, date DESC);

-- ============================================================================
-- 5. FONCTIONS ET TRIGGERS
-- ============================================================================

-- Fonction pour mettre à jour les vues matérialisées
CREATE OR REPLACE FUNCTION refresh_metrics_views()
RETURNS VOID AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY resource_stats_realtime;
  REFRESH MATERIALIZED VIEW CONCURRENTLY genre_trends;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour calculer le score de tendance
CREATE OR REPLACE FUNCTION calculate_trending_score(
  resource_type_param VARCHAR(20),
  resource_id_param UUID,
  days_back INTEGER DEFAULT 7
)
RETURNS DECIMAL(5,2) AS $$
DECLARE
  recent_activity INTEGER;
  previous_activity INTEGER;
  trend_score DECIMAL(5,2);
BEGIN
  -- Activité récente
  SELECT COUNT(*) INTO recent_activity
  FROM unified_metrics
  WHERE resource_type = resource_type_param
    AND resource_id = resource_id_param
    AND created_at > NOW() - (days_back || ' days')::INTERVAL;
  
  -- Activité période précédente
  SELECT COUNT(*) INTO previous_activity
  FROM unified_metrics
  WHERE resource_type = resource_type_param
    AND resource_id = resource_id_param
    AND created_at BETWEEN 
      NOW() - (days_back * 2 || ' days')::INTERVAL 
      AND NOW() - (days_back || ' days')::INTERVAL;
  
  -- Calcul du score de tendance
  IF previous_activity = 0 THEN
    trend_score := CASE WHEN recent_activity > 0 THEN 100.0 ELSE 0.0 END;
  ELSE
    trend_score := LEAST(((recent_activity::DECIMAL / previous_activity) - 1) * 100, 100.0);
  END IF;
  
  RETURN GREATEST(trend_score, 0.0);
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour automatiquement les statistiques
CREATE OR REPLACE FUNCTION update_resource_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Programmer un rafraîchissement des vues matérialisées
  -- (en production, utiliser un job scheduler comme pg_cron)
  PERFORM pg_notify('refresh_metrics', NEW.resource_type || ':' || NEW.resource_id);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger sur les nouvelles métriques
DROP TRIGGER IF EXISTS trigger_update_resource_stats ON unified_metrics;
CREATE TRIGGER trigger_update_resource_stats
  AFTER INSERT ON unified_metrics
  FOR EACH ROW
  EXECUTE FUNCTION update_resource_stats();

-- ============================================================================
-- 6. DONNÉES INITIALES
-- ============================================================================

-- Genres principaux avec métadonnées
INSERT INTO genre_metadata (genre_name, display_name, description, characteristics, typical_instruments, bpm_range, key_signatures, related_genres, popularity_score) VALUES
('rock', 'Rock', 'Genre musical énergique caractérisé par des guitares électriques et des rythmes puissants', 
 '["Rythmes puissants", "Guitares électriques", "Énergie brute", "Rebellion"]'::jsonb,
 ARRAY['Guitare électrique', 'Basse', 'Batterie', 'Voix'],
 '[120,180]'::int4range,
 ARRAY['E', 'A', 'D', 'G', 'B'],
 ARRAY['Alternative', 'Metal', 'Punk', 'Blues'],
 85),

('jazz', 'Jazz', 'Genre musical sophistiqué basé sur l\'improvisation et l\'harmonie complexe',
 '["Improvisation", "Harmonies complexes", "Swing", "Sophistication"]'::jsonb,
 ARRAY['Piano', 'Saxophone', 'Trompette', 'Contrebasse', 'Batterie'],
 '[60,200]'::int4range,
 ARRAY['Bb', 'F', 'C', 'G', 'D'],
 ARRAY['Blues', 'Fusion', 'Bebop', 'Smooth Jazz'],
 75),

('electronic', 'Électronique', 'Musique créée principalement avec des instruments électroniques et des technologies numériques',
 '["Synthétiseurs", "Rythmes programmés", "Innovation technologique", "Danse"]'::jsonb,
 ARRAY['Synthétiseur', 'Drum Machine', 'Sampler', 'Séquenceur'],
 '[80,180]'::int4range,
 ARRAY['Am', 'Em', 'Dm', 'Gm', 'Cm'],
 ARRAY['Techno', 'House', 'Ambient', 'Dubstep'],
 90),

('pop', 'Pop', 'Musique populaire accessible caractérisée par des mélodies accrocheuses',
 '["Mélodies accrocheuses", "Structure accessible", "Production soignée", "Commercial"]'::jsonb,
 ARRAY['Voix', 'Guitare', 'Piano', 'Synthé', 'Batterie'],
 '[100,140]'::int4range,
 ARRAY['C', 'G', 'Am', 'F', 'D'],
 ARRAY['R&B', 'Dance', 'Folk', 'Rock'],
 95)

ON CONFLICT (genre_name) DO UPDATE SET
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  characteristics = EXCLUDED.characteristics,
  typical_instruments = EXCLUDED.typical_instruments,
  bpm_range = EXCLUDED.bpm_range,
  key_signatures = EXCLUDED.key_signatures,
  related_genres = EXCLUDED.related_genres,
  popularity_score = EXCLUDED.popularity_score,
  updated_at = NOW();

-- ============================================================================
-- VÉRIFICATION FINALE
-- ============================================================================

DO $$
DECLARE
  table_count INTEGER;
  view_count INTEGER;
  function_count INTEGER;
BEGIN
  -- Compter les nouvelles tables
  SELECT COUNT(*) INTO table_count
  FROM information_schema.tables 
  WHERE table_name IN ('unified_metrics', 'genre_metadata', 'relationship_metadata', 'community_analytics');
  
  -- Compter les vues matérialisées
  SELECT COUNT(*) INTO view_count
  FROM pg_matviews 
  WHERE matviewname IN ('resource_stats_realtime', 'genre_trends');
  
  -- Compter les fonctions
  SELECT COUNT(*) INTO function_count
  FROM pg_proc 
  WHERE proname IN ('refresh_metrics_views', 'calculate_trending_score', 'update_resource_stats');
  
  RAISE NOTICE '✅ SYSTÈME DE MÉTRIQUES UNIFIÉ DÉPLOYÉ !';
  RAISE NOTICE '📊 Tables créées: %', table_count;
  RAISE NOTICE '📈 Vues matérialisées: %', view_count;
  RAISE NOTICE '⚡ Fonctions créées: %', function_count;
  RAISE NOTICE '🎵 Analytics MOUVIK opérationnels !';
  
  IF table_count = 4 AND view_count = 2 AND function_count = 3 THEN
    RAISE NOTICE '🎉 DÉPLOIEMENT COMPLET RÉUSSI !';
  ELSE
    RAISE WARNING '⚠️ Déploiement partiel - Vérifier les erreurs';
  END IF;
END $$;
