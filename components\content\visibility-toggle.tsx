"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Eye, 
  EyeOff, 
  Globe, 
  Lock, 
  Users, 
  ChevronDown,
  Check,
  Clock,
  Archive
} from 'lucide-react';
import { createBrowserClient } from '@/lib/supabase/client';

export type ContentType = 'song' | 'album' | 'playlist' | 'band';
export type VisibilityStatus = 'public' | 'private' | 'unlisted' | 'friends';
export type PublishStatus = 'draft' | 'published' | 'archived' | 'scheduled';

interface VisibilityConfig {
  visibility: VisibilityStatus;
  status: PublishStatus;
  scheduledFor?: Date;
}

interface VisibilityToggleProps {
  contentType: ContentType;
  contentId: string;
  currentVisibility: VisibilityStatus;
  currentStatus: PublishStatus;
  scheduledFor?: Date;
  onVisibilityChange?: (config: VisibilityConfig) => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const visibilityOptions = [
  {
    value: 'public' as VisibilityStatus,
    label: 'Public',
    description: 'Visible par tous les utilisateurs',
    icon: Globe,
    color: 'text-green-500',
    bgColor: 'bg-green-500/10 border-green-500/20'
  },
  {
    value: 'unlisted' as VisibilityStatus,
    label: 'Non répertorié',
    description: 'Accessible uniquement via le lien direct',
    icon: Eye,
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-500/10 border-yellow-500/20'
  },
  {
    value: 'friends' as VisibilityStatus,
    label: 'Amis seulement',
    description: 'Visible uniquement par vos amis',
    icon: Users,
    color: 'text-blue-500',
    bgColor: 'bg-blue-500/10 border-blue-500/20'
  },
  {
    value: 'private' as VisibilityStatus,
    label: 'Privé',
    description: 'Visible uniquement par vous',
    icon: Lock,
    color: 'text-red-500',
    bgColor: 'bg-red-500/10 border-red-500/20'
  }
];

const statusOptions = [
  {
    value: 'draft' as PublishStatus,
    label: 'Brouillon',
    description: 'En cours de création',
    icon: EyeOff,
    color: 'text-gray-500'
  },
  {
    value: 'published' as PublishStatus,
    label: 'Publié',
    description: 'Disponible selon la visibilité',
    icon: Check,
    color: 'text-green-500'
  },
  {
    value: 'scheduled' as PublishStatus,
    label: 'Programmé',
    description: 'Publication programmée',
    icon: Clock,
    color: 'text-blue-500'
  },
  {
    value: 'archived' as PublishStatus,
    label: 'Archivé',
    description: 'Retiré de la publication',
    icon: Archive,
    color: 'text-orange-500'
  }
];

export default function VisibilityToggle({
  contentType,
  contentId,
  currentVisibility,
  currentStatus,
  scheduledFor,
  onVisibilityChange,
  disabled = false,
  size = 'md'
}: VisibilityToggleProps) {
  const [isLoading, setIsLoading] = useState(false);
  const supabase = createBrowserClient();

  const currentVisibilityOption = visibilityOptions.find(opt => opt.value === currentVisibility);
  const currentStatusOption = statusOptions.find(opt => opt.value === currentStatus);

  const updateVisibility = async (newVisibility: VisibilityStatus, newStatus?: PublishStatus) => {
    if (disabled || isLoading) return;

    setIsLoading(true);
    try {
      const finalStatus = newStatus || currentStatus;
      const tableName = getTableName(contentType);
      const updateData = getUpdateData(contentType, newVisibility, finalStatus);

      const { error } = await supabase
        .from(tableName)
        .update(updateData)
        .eq('id', contentId);

      if (error) {
        console.error('Erreur lors de la mise à jour de la visibilité:', error);
        return;
      }

      // Callback pour notifier le parent
      onVisibilityChange?.({
        visibility: newVisibility,
        status: finalStatus,
        scheduledFor
      });

    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateStatus = async (newStatus: PublishStatus) => {
    if (disabled || isLoading) return;

    setIsLoading(true);
    try {
      const tableName = getTableName(contentType);
      const updateData = getUpdateData(contentType, currentVisibility, newStatus);

      const { error } = await supabase
        .from(tableName)
        .update(updateData)
        .eq('id', contentId);

      if (error) {
        console.error('Erreur lors de la mise à jour du statut:', error);
        return;
      }

      onVisibilityChange?.({
        visibility: currentVisibility,
        status: newStatus,
        scheduledFor
      });

    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getTableName = (type: ContentType): string => {
    switch (type) {
      case 'song': return 'songs';
      case 'album': return 'albums';
      case 'playlist': return 'playlists';
      case 'band': return 'bands';
      default: return 'songs';
    }
  };

  const getUpdateData = (type: ContentType, visibility: VisibilityStatus, status: PublishStatus) => {
    const baseData = { updated_at: new Date().toISOString() };

    switch (type) {
      case 'song':
      case 'album':
        return {
          ...baseData,
          visibility,
          status
        };
      case 'playlist':
        return {
          ...baseData,
          is_public: visibility === 'public',
          // Note: Les playlists n'ont pas de status dans le schéma actuel
        };
      case 'band':
        return {
          ...baseData,
          visibility,
          status
        };
      default:
        return baseData;
    }
  };

  const getEffectiveVisibility = () => {
    if (currentStatus === 'draft' || currentStatus === 'archived') {
      return 'private'; // Les brouillons et archives sont toujours privés
    }
    return currentVisibility;
  };

  const effectiveVisibility = getEffectiveVisibility();
  const effectiveVisibilityOption = visibilityOptions.find(opt => opt.value === effectiveVisibility);

  const buttonSize = size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'default';

  return (
    <div className="flex items-center gap-2">
      {/* Badge de statut */}
      <Badge 
        variant="outline" 
        className={`${currentStatusOption?.color} border-current`}
      >
        <currentStatusOption?.icon className="h-3 w-3 mr-1" />
        {currentStatusOption?.label}
      </Badge>

      {/* Dropdown de visibilité */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size={buttonSize}
            disabled={disabled || isLoading}
            className={`${effectiveVisibilityOption?.bgColor} ${effectiveVisibilityOption?.color} border-current hover:bg-current/20`}
          >
            <effectiveVisibilityOption?.icon className="h-4 w-4 mr-2" />
            {size !== 'sm' && effectiveVisibilityOption?.label}
            <ChevronDown className="h-3 w-3 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64 bg-slate-800 border-slate-700">
          <DropdownMenuLabel className="text-slate-300">Statut de publication</DropdownMenuLabel>
          {statusOptions.map((option) => (
            <DropdownMenuItem
              key={option.value}
              onClick={() => updateStatus(option.value)}
              className="text-slate-300 hover:bg-slate-700 cursor-pointer"
              disabled={isLoading}
            >
              <div className="flex items-center gap-3 w-full">
                <option.icon className={`h-4 w-4 ${option.color}`} />
                <div className="flex-1">
                  <div className="font-medium">{option.label}</div>
                  <div className="text-xs text-slate-400">{option.description}</div>
                </div>
                {currentStatus === option.value && (
                  <Check className="h-4 w-4 text-green-400" />
                )}
              </div>
            </DropdownMenuItem>
          ))}

          <DropdownMenuSeparator className="bg-slate-700" />
          <DropdownMenuLabel className="text-slate-300">Visibilité</DropdownMenuLabel>
          
          {visibilityOptions.map((option) => (
            <DropdownMenuItem
              key={option.value}
              onClick={() => updateVisibility(option.value)}
              className="text-slate-300 hover:bg-slate-700 cursor-pointer"
              disabled={isLoading || currentStatus === 'draft' || currentStatus === 'archived'}
            >
              <div className="flex items-center gap-3 w-full">
                <option.icon className={`h-4 w-4 ${option.color}`} />
                <div className="flex-1">
                  <div className="font-medium">{option.label}</div>
                  <div className="text-xs text-slate-400">{option.description}</div>
                </div>
                {currentVisibility === option.value && (
                  <Check className="h-4 w-4 text-green-400" />
                )}
              </div>
            </DropdownMenuItem>
          ))}

          {(currentStatus === 'draft' || currentStatus === 'archived') && (
            <div className="p-2 text-xs text-slate-400 bg-slate-700/50 rounded-md mx-2 mt-2">
              💡 Publiez d'abord pour modifier la visibilité
            </div>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
