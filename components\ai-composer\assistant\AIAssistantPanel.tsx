'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  <PERSON>rkles, Settings, MessageSquare, History, 
  Wand2, Music, FileText, RotateCcw 
} from 'lucide-react';

interface AIAssistantPanelProps {
  isConfigured?: boolean;
  aiLoading?: boolean;
  aiError?: string | null;
  onAIGenerate?: (prompt: string, type: string) => Promise<void>;
  currentSection?: string;
  lyricsContent?: string;
  chordPlacements?: any[];
  aiHistory?: any[];
  className?: string;
}

/**
 * Panneau principal de l'assistant IA
 * Hub unifié pour toutes les interactions IA dans l'AI Composer
 */
export const AIAssistantPanel: React.FC<AIAssistantPanelProps> = ({
  isConfigured = false,
  aiLoading = false,
  aiError = null,
  onAIGenerate,
  currentSection = 'verse-1',
  lyricsContent = '',
  chordPlacements = [],
  aiHistory = [],
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('quick-actions');
  const [customPrompt, setCustomPrompt] = useState('');

  const handleQuickAction = useCallback(async (action: string) => {
    if (!isConfigured || !onAIGenerate) return;

    let prompt = '';
    switch (action) {
      case 'continue-lyrics':
        prompt = `Continue les paroles suivantes dans le style actuel :\n\n${lyricsContent}`;
        break;
      case 'suggest-chords':
        prompt = `Suggère des accords pour ces paroles :\n\n${lyricsContent}`;
        break;
      case 'improve-lyrics':
        prompt = `Améliore ces paroles en gardant le sens :\n\n${lyricsContent}`;
        break;
      case 'analyze-structure':
        prompt = `Analyse la structure de cette chanson :\n\n${lyricsContent}`;
        break;
      default:
        prompt = customPrompt;
    }

    try {
      await onAIGenerate(prompt, action);
    } catch (error) {
      console.error('Erreur lors de l\'action IA:', error);
    }
  }, [isConfigured, onAIGenerate, lyricsContent, customPrompt]);

  const quickActions = [
    { id: 'continue-lyrics', label: 'Continuer les paroles', icon: FileText },
    { id: 'suggest-chords', label: 'Suggérer des accords', icon: Music },
    { id: 'improve-lyrics', label: 'Améliorer les paroles', icon: Wand2 },
    { id: 'analyze-structure', label: 'Analyser la structure', icon: RotateCcw },
  ];

  if (!isConfigured) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="w-5 h-5" />
            Assistant IA
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Settings className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground mb-4">
              Configurez votre assistant IA pour commencer
            </p>
            <Button variant="outline">
              <Settings className="w-4 h-4 mr-2" />
              Configurer l'IA
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="w-5 h-5" />
          Assistant IA
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="quick-actions">Actions</TabsTrigger>
            <TabsTrigger value="chat">Chat</TabsTrigger>
            <TabsTrigger value="history">Historique</TabsTrigger>
          </TabsList>
          
          <TabsContent value="quick-actions" className="space-y-4">
            <div className="grid grid-cols-2 gap-2">
              {quickActions.map((action) => (
                <Button
                  key={action.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction(action.id)}
                  disabled={aiLoading}
                  className="h-auto p-3 flex flex-col items-center gap-2"
                >
                  <action.icon className="w-4 h-4" />
                  <span className="text-xs text-center">{action.label}</span>
                </Button>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="chat" className="space-y-4">
            <div className="text-center py-8 text-muted-foreground">
              <MessageSquare className="w-12 h-12 mx-auto mb-4" />
              <p>Interface de chat IA à implémenter</p>
            </div>
          </TabsContent>
          
          <TabsContent value="history" className="space-y-4">
            <div className="text-center py-8 text-muted-foreground">
              <History className="w-12 h-12 mx-auto mb-4" />
              <p>Historique des interactions IA</p>
              {aiHistory.length > 0 && (
                <p className="text-sm">{aiHistory.length} interactions</p>
              )}
            </div>
          </TabsContent>
        </Tabs>
        
        {aiError && (
          <div className="mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
            <p className="text-sm text-destructive">{aiError}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
