"use client";

import React, { useState, useEffect } from 'react';
import Link from "next/link";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  Music2, 
  Disc3, 
  Users, 
  TrendingUp, 
  Heart, 
  Play, 
  Share2,
  MessageCircle,
  UserPlus,
  Radio,
  Sparkles,
  BarChart3,
  Calendar,
  Guitar,
  Mic,
  Headphones,
  Star,
  Crown,
  Zap
} from 'lucide-react';

interface GenreAdvancedPageProps {
  genre: string;
  initialData: {
    songs: any[];
    albums: any[];
    artists: any[];
    bands: any[];
    playlists: any[];
    community: any;
    stats: any;
  };
}

export default function GenreAdvancedPage({ genre, initialData }: GenreAdvancedPageProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [isJoinedCommunity, setIsJoinedCommunity] = useState(false);

  // Données enrichies pour la démonstration
  const genreData = {
    name: genre.charAt(0).toUpperCase() + genre.slice(1),
    description: `Le ${genre} est un genre musical riche et diversifié qui continue d'évoluer et d'inspirer de nouveaux artistes.`,
    characteristics: [
      'Rythmes dynamiques',
      'Mélodies expressives', 
      'Harmonies complexes',
      'Innovation constante'
    ],
    typicalInstruments: ['Guitare', 'Basse', 'Batterie', 'Synthé', 'Voix'],
    bpmRange: { min: 80, max: 140 },
    keySignatures: ['Em', 'Am', 'Dm', 'G', 'C'],
    relatedGenres: ['Alternative', 'Indie', 'Progressive', 'Post-Rock'],
    popularityScore: 85,
    trendingUp: true
  };

  const liveStats = {
    activeSongs: 1247,
    recentAlbums: 89,
    activeArtists: 156,
    connectedFans: 2341,
    weeklyGrowth: 12.5,
    engagementRate: 78.3,
    collaborationsThisMonth: 45,
    newReleasesToday: 8
  };

  const trendingContent = [
    {
      id: '1',
      type: 'song',
      title: 'Midnight Echoes',
      artist: 'Luna Collective',
      plays: 15420,
      trend: '+23%',
      cover: '/covers/midnight-echoes.jpg'
    },
    {
      id: '2', 
      type: 'album',
      title: 'Neon Dreams',
      artist: 'Cyber Waves',
      plays: 8930,
      trend: '+18%',
      cover: '/covers/neon-dreams.jpg'
    },
    {
      id: '3',
      type: 'playlist',
      title: `Best of ${genre} 2024`,
      creator: 'MusicCurator',
      followers: 3240,
      trend: '+31%',
      cover: '/covers/best-of-2024.jpg'
    }
  ];

  const collaborationOpportunities = [
    {
      id: '1',
      type: 'artist',
      name: 'Alex Rivera',
      role: 'Guitariste/Compositeur',
      location: 'Paris, France',
      experience: 'Intermédiaire',
      lookingFor: 'Bassiste pour projet album',
      genres: [genre, 'Alternative', 'Indie'],
      avatar: '/avatars/alex-rivera.jpg',
      matchScore: 94
    },
    {
      id: '2',
      type: 'band',
      name: 'Electric Minds',
      members: 3,
      location: 'Lyon, France', 
      experience: 'Avancé',
      lookingFor: 'Chanteur/Chanteuse',
      genres: [genre, 'Electronic', 'Progressive'],
      avatar: '/avatars/electric-minds.jpg',
      matchScore: 87
    },
    {
      id: '3',
      type: 'producer',
      name: 'Sophie Chen',
      role: 'Productrice/Ingénieure son',
      location: 'Marseille, France',
      experience: 'Expert',
      lookingFor: 'Artistes pour collaborations',
      genres: [genre, 'Ambient', 'Experimental'],
      avatar: '/avatars/sophie-chen.jpg',
      matchScore: 91
    }
  ];

  const communityInsights = {
    totalMembers: 2341,
    activeToday: 456,
    postsThisWeek: 127,
    collaborationsStarted: 23,
    eventsThisMonth: 8,
    topContributors: [
      { name: 'MusicMaster', contributions: 45, avatar: '/avatars/musicmaster.jpg' },
      { name: 'BeatCreator', contributions: 38, avatar: '/avatars/beatcreator.jpg' },
      { name: 'SoundWave', contributions: 32, avatar: '/avatars/soundwave.jpg' }
    ]
  };

  const creativeTools = [
    {
      name: 'Accords Typiques',
      description: `Progressions d'accords populaires en ${genre}`,
      icon: <Guitar className="h-6 w-6" />,
      action: 'Explorer'
    },
    {
      name: 'Templates AI',
      description: 'Modèles de composition assistée par IA',
      icon: <Sparkles className="h-6 w-6" />,
      action: 'Créer'
    },
    {
      name: 'Samples & Loops',
      description: 'Bibliothèque de samples authentiques',
      icon: <Headphones className="h-6 w-6" />,
      action: 'Écouter'
    },
    {
      name: 'Guide de Composition',
      description: `Techniques spécifiques au ${genre}`,
      icon: <Mic className="h-6 w-6" />,
      action: 'Apprendre'
    }
  ];

  const getMatchScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 80) return 'text-yellow-400';
    return 'text-orange-400';
  };

  const getTrendIcon = (trend: string) => {
    return trend.startsWith('+') ? (
      <TrendingUp className="h-4 w-4 text-green-400" />
    ) : (
      <TrendingUp className="h-4 w-4 text-red-400 rotate-180" />
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-slate-700">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-6 py-12">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-6">
              <div className="h-24 w-24 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center">
                <Music2 className="h-12 w-12 text-white" />
              </div>
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-5xl font-bold text-white">{genreData.name}</h1>
                  {genreData.trendingUp && (
                    <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                      <TrendingUp className="h-4 w-4 mr-1" />
                      Tendance
                    </Badge>
                  )}
                </div>
                <p className="text-xl text-slate-300 max-w-2xl">{genreData.description}</p>
                <div className="flex items-center gap-4 mt-4">
                  <Badge variant="outline" className="border-slate-600 text-slate-300">
                    Popularité: {genreData.popularityScore}/100
                  </Badge>
                  <Badge variant="outline" className="border-slate-600 text-slate-300">
                    BPM: {genreData.bpmRange.min}-{genreData.bpmRange.max}
                  </Badge>
                </div>
              </div>
            </div>
            <div className="text-right">
              <Button 
                size="lg" 
                className={`${isJoinedCommunity ? 'bg-green-500 hover:bg-green-600' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
                onClick={() => setIsJoinedCommunity(!isJoinedCommunity)}
              >
                <Radio className="h-5 w-5 mr-2" />
                {isJoinedCommunity ? 'Membre de la communauté' : 'Rejoindre la communauté'}
              </Button>
            </div>
          </div>

          {/* Stats Live */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-white">{liveStats.activeSongs.toLocaleString()}</div>
                <div className="text-sm text-slate-400">Morceaux actifs</div>
              </CardContent>
            </Card>
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-white">{liveStats.activeArtists}</div>
                <div className="text-sm text-slate-400">Artistes actifs</div>
              </CardContent>
            </Card>
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-white">{liveStats.connectedFans.toLocaleString()}</div>
                <div className="text-sm text-slate-400">Fans connectés</div>
              </CardContent>
            </Card>
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-400">+{liveStats.weeklyGrowth}%</div>
                <div className="text-sm text-slate-400">Croissance semaine</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="max-w-7xl mx-auto px-6 py-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="bg-slate-800 border-slate-700 grid grid-cols-6 w-full">
            <TabsTrigger value="overview" className="data-[state=active]:bg-slate-700">
              <BarChart3 className="h-4 w-4 mr-2" />
              Vue d'ensemble
            </TabsTrigger>
            <TabsTrigger value="content" className="data-[state=active]:bg-slate-700">
              <Music2 className="h-4 w-4 mr-2" />
              Contenu
            </TabsTrigger>
            <TabsTrigger value="community" className="data-[state=active]:bg-slate-700">
              <Users className="h-4 w-4 mr-2" />
              Communauté
            </TabsTrigger>
            <TabsTrigger value="collaborate" className="data-[state=active]:bg-slate-700">
              <UserPlus className="h-4 w-4 mr-2" />
              Collaborer
            </TabsTrigger>
            <TabsTrigger value="create" className="data-[state=active]:bg-slate-700">
              <Sparkles className="h-4 w-4 mr-2" />
              Créer
            </TabsTrigger>
            <TabsTrigger value="insights" className="data-[state=active]:bg-slate-700">
              <Zap className="h-4 w-4 mr-2" />
              Insights
            </TabsTrigger>
          </TabsList>

          {/* Vue d'ensemble */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Contenu Tendance */}
              <div className="lg:col-span-2">
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      Contenu en Tendance
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {trendingContent.map((item) => (
                      <div key={item.id} className="flex items-center gap-4 p-4 bg-slate-700/50 rounded-lg hover:bg-slate-700/70 transition-colors">
                        <div className="h-16 w-16 bg-slate-600 rounded-lg flex items-center justify-center">
                          {item.type === 'song' && <Music2 className="h-8 w-8 text-slate-300" />}
                          {item.type === 'album' && <Disc3 className="h-8 w-8 text-slate-300" />}
                          {item.type === 'playlist' && <Radio className="h-8 w-8 text-slate-300" />}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-white">{item.title}</h3>
                          <p className="text-slate-400">{item.artist || item.creator}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-sm text-slate-300">
                              {item.plays ? `${item.plays.toLocaleString()} écoutes` : `${item.followers?.toLocaleString()} abonnés`}
                            </span>
                            <div className="flex items-center gap-1 text-green-400">
                              {getTrendIcon(item.trend)}
                              <span className="text-sm">{item.trend}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white">
                            <Play className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white">
                            <Heart className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white">
                            <Share2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>

              {/* Caractéristiques du Genre */}
              <div>
                <Card className="bg-slate-800/50 border-slate-700 mb-6">
                  <CardHeader>
                    <CardTitle className="text-white">Caractéristiques</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium text-slate-300 mb-2">Instruments Typiques</h4>
                      <div className="flex flex-wrap gap-1">
                        {genreData.typicalInstruments.map((instrument) => (
                          <Badge key={instrument} variant="outline" className="border-slate-600 text-slate-300 text-xs">
                            {instrument}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-slate-300 mb-2">Tonalités Populaires</h4>
                      <div className="flex flex-wrap gap-1">
                        {genreData.keySignatures.map((key) => (
                          <Badge key={key} variant="outline" className="border-slate-600 text-slate-300 text-xs">
                            {key}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-slate-300 mb-2">Genres Connexes</h4>
                      <div className="flex flex-wrap gap-1">
                        {genreData.relatedGenres.map((relatedGenre) => (
                          <Link key={relatedGenre} href={`/genres/${relatedGenre.toLowerCase()}`}>
                            <Badge variant="outline" className="border-blue-500/30 text-blue-400 text-xs hover:bg-blue-500/10 cursor-pointer">
                              {relatedGenre}
                            </Badge>
                          </Link>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Activité Communauté */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white">Activité Communauté</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-slate-300">Membres actifs aujourd'hui</span>
                      <span className="text-white font-semibold">{communityInsights.activeToday}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-300">Posts cette semaine</span>
                      <span className="text-white font-semibold">{communityInsights.postsThisWeek}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-300">Collaborations démarrées</span>
                      <span className="text-white font-semibold">{communityInsights.collaborationsStarted}</span>
                    </div>
                    <div>
                      <span className="text-slate-300 text-sm">Taux d'engagement</span>
                      <Progress value={liveStats.engagementRate} className="mt-2" />
                      <span className="text-xs text-slate-400">{liveStats.engagementRate}%</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Autres onglets à développer... */}
          <TabsContent value="content">
            <div className="text-center py-12">
              <Music2 className="h-16 w-16 mx-auto mb-4 text-slate-500" />
              <h3 className="text-lg font-medium text-slate-300 mb-2">Contenu en développement</h3>
              <p className="text-slate-400">Cette section affichera tous les morceaux, albums et playlists du genre.</p>
            </div>
          </TabsContent>

          <TabsContent value="community">
            <div className="text-center py-12">
              <Users className="h-16 w-16 mx-auto mb-4 text-slate-500" />
              <h3 className="text-lg font-medium text-slate-300 mb-2">Communauté en développement</h3>
              <p className="text-slate-400">Cette section affichera les discussions et activités de la communauté.</p>
            </div>
          </TabsContent>

          <TabsContent value="collaborate">
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-white mb-2">Opportunités de Collaboration</h2>
                <p className="text-slate-400">Trouvez des artistes, groupes et producteurs pour vos projets</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {collaborationOpportunities.map((opportunity) => (
                  <Card key={opportunity.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4 mb-4">
                        <Avatar className="h-16 w-16">
                          <AvatarImage src={opportunity.avatar} />
                          <AvatarFallback className="bg-slate-600 text-white text-lg">
                            {opportunity.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold text-white">{opportunity.name}</h3>
                            <Badge className={`${getMatchScoreColor(opportunity.matchScore)} bg-transparent border-current`}>
                              {opportunity.matchScore}% match
                            </Badge>
                          </div>
                          <p className="text-slate-400 text-sm">{opportunity.role}</p>
                          <p className="text-slate-500 text-xs">{opportunity.location}</p>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <p className="text-sm text-slate-300 font-medium">Recherche :</p>
                          <p className="text-sm text-slate-400">{opportunity.lookingFor}</p>
                        </div>

                        <div className="flex flex-wrap gap-1">
                          {opportunity.genres.map((genre, index) => (
                            <Badge key={index} variant="outline" className="border-slate-600 text-slate-300 text-xs">
                              {genre}
                            </Badge>
                          ))}
                        </div>

                        <div className="flex gap-2 pt-2">
                          <Button size="sm" className="flex-1 bg-blue-500 hover:bg-blue-600">
                            <MessageCircle className="h-4 w-4 mr-2" />
                            Contacter
                          </Button>
                          <Button size="sm" variant="outline" className="border-slate-600">
                            <UserPlus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="create">
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-white mb-2">Outils de Création</h2>
                <p className="text-slate-400">Ressources spécialisées pour créer dans le style {genre}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {creativeTools.map((tool, index) => (
                  <Card key={index} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="h-12 w-12 bg-blue-500/20 rounded-lg flex items-center justify-center text-blue-400">
                          {tool.icon}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-white mb-2">{tool.name}</h3>
                          <p className="text-slate-400 text-sm mb-4">{tool.description}</p>
                          <Button size="sm" className="bg-blue-500 hover:bg-blue-600">
                            {tool.action}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="insights">
            <div className="text-center py-12">
              <Zap className="h-16 w-16 mx-auto mb-4 text-slate-500" />
              <h3 className="text-lg font-medium text-slate-300 mb-2">Insights IA en développement</h3>
              <p className="text-slate-400">Cette section affichera les analyses et prédictions IA pour le genre.</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
