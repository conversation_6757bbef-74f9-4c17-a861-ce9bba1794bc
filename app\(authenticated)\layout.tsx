import type React from "react";
import { redirect } from "next/navigation";
import { createSupabaseServerClient } from "@/lib/supabase/server";
import { getUserProfileForSidebar } from "@/lib/supabase/queries/user";
import MainLayout from "./main-layout";

interface AuthenticatedLayoutProps {
  children: React.ReactNode;
}

export default async function AuthenticatedLayout({ children }: AuthenticatedLayoutProps) {
  const supabase = createSupabaseServerClient();

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    redirect("/");
  }

  const userProfile = await getUserProfileForSidebar(supabase, user.id, user);

  if (!userProfile) {
    // Handle case where profile is not found, maybe redirect or show an error
    // For now, we'll redirect to home, but a dedicated error page might be better
    console.warn(`AuthenticatedLayout: Could not fetch sidebar profile for user ${user.id}.`);
    redirect('/');
  }

  return <MainLayout user={userProfile}>{children}</MainLayout>;
}
