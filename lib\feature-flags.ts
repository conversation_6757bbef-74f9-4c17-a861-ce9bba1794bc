/**
 * 🚀 SYSTÈME DE FEATURE FLAGS - MOUVIK
 * 
 * Contrôle le déploiement progressif des nouvelles fonctionnalités
 * Version: 1.0 - Juillet 2024
 */

export interface FeatureFlag {
  key: string;
  name: string;
  description: string;
  enabled: boolean;
  rolloutPercentage: number;
  environments: ('development' | 'staging' | 'production')[];
  dependencies?: string[];
  metadata?: Record<string, any>;
}

/**
 * Configuration des feature flags
 */
export const FEATURE_FLAGS: Record<string, FeatureFlag> = {
  // 💬 Système de Messagerie Unifié
  MESSAGING_SYSTEM: {
    key: 'MESSAGING_SYSTEM',
    name: 'Système de Messagerie Unifié',
    description: 'Chat temps réel, conversations, messages',
    enabled: true,
    rolloutPercentage: 100,
    environments: ['development', 'staging'],
    metadata: {
      version: '1.0',
      components: ['messages', 'conversations', 'chat']
    }
  },

  // 👫 Système d'Amis Avancé
  FRIENDS_SYSTEM: {
    key: 'FRIENDS_SYSTEM',
    name: 'Système d\'Amis Avancé',
    description: 'Gestion des amis, cercles, suggestions IA',
    enabled: true,
    rolloutPercentage: 100,
    environments: ['development', 'staging'],
    dependencies: ['MESSAGING_SYSTEM'],
    metadata: {
      version: '1.0',
      components: ['friends', 'circles', 'suggestions']
    }
  },

  // 🏷️ Communautés Automatiques
  AUTO_COMMUNITIES: {
    key: 'AUTO_COMMUNITIES',
    name: 'Communautés Automatiques',
    description: 'Communautés basées sur tags/genres',
    enabled: true,
    rolloutPercentage: 80,
    environments: ['development', 'staging'],
    metadata: {
      version: '1.0',
      components: ['communities', 'auto-join', 'tag-based']
    }
  },

  // 🔔 Notifications Intelligentes
  SMART_NOTIFICATIONS: {
    key: 'SMART_NOTIFICATIONS',
    name: 'Notifications Intelligentes',
    description: 'Système de notifications unifié avec IA',
    enabled: true,
    rolloutPercentage: 90,
    environments: ['development', 'staging'],
    dependencies: ['MESSAGING_SYSTEM', 'FRIENDS_SYSTEM'],
    metadata: {
      version: '1.0',
      components: ['notifications', 'smart-timing', 'multi-channel']
    }
  },

  // 📊 Feed Social Avancé
  SOCIAL_FEED: {
    key: 'SOCIAL_FEED',
    name: 'Feed Social Avancé',
    description: 'Feed d\'activité sociale intelligent',
    enabled: false,
    rolloutPercentage: 0,
    environments: ['development'],
    dependencies: ['FRIENDS_SYSTEM', 'AUTO_COMMUNITIES'],
    metadata: {
      version: '0.9',
      components: ['feed', 'activity', 'recommendations']
    }
  },

  // 💭 Commentaires Avancés
  ADVANCED_COMMENTS: {
    key: 'ADVANCED_COMMENTS',
    name: 'Commentaires Avancés',
    description: 'Réactions, threads, mentions dans les commentaires',
    enabled: true,
    rolloutPercentage: 100,
    environments: ['development', 'staging'],
    metadata: {
      version: '1.0',
      components: ['reactions', 'threads', 'mentions']
    }
  },

  // 🎵 Partage Musical Intégré
  MUSIC_SHARING: {
    key: 'MUSIC_SHARING',
    name: 'Partage Musical Intégré',
    description: 'Partage de morceaux dans les conversations',
    enabled: false,
    rolloutPercentage: 0,
    environments: ['development'],
    dependencies: ['MESSAGING_SYSTEM'],
    metadata: {
      version: '0.8',
      components: ['song-share', 'album-share', 'playlist-share']
    }
  },

  // 🔍 Recherche Avancée
  ADVANCED_SEARCH: {
    key: 'ADVANCED_SEARCH',
    name: 'Recherche Avancée',
    description: 'Recherche dans messages, communautés, amis',
    enabled: false,
    rolloutPercentage: 0,
    environments: ['development'],
    dependencies: ['MESSAGING_SYSTEM', 'FRIENDS_SYSTEM', 'AUTO_COMMUNITIES'],
    metadata: {
      version: '0.7',
      components: ['full-text-search', 'filters', 'suggestions']
    }
  }
};

/**
 * Vérifie si une feature flag est activée
 */
export function isFeatureEnabled(flagKey: string, userId?: string): boolean {
  const flag = FEATURE_FLAGS[flagKey];
  
  if (!flag) {
    console.warn(`Feature flag '${flagKey}' not found`);
    return false;
  }

  // Vérifier l'environnement
  const currentEnv = process.env.NODE_ENV as 'development' | 'staging' | 'production';
  if (!flag.environments.includes(currentEnv)) {
    return false;
  }

  // Vérifier si la feature est globalement activée
  if (!flag.enabled) {
    return false;
  }

  // Vérifier les dépendances
  if (flag.dependencies) {
    for (const dependency of flag.dependencies) {
      if (!isFeatureEnabled(dependency, userId)) {
        return false;
      }
    }
  }

  // Vérifier le pourcentage de rollout
  if (flag.rolloutPercentage < 100) {
    if (!userId) {
      return false;
    }
    
    // Hash simple basé sur l'ID utilisateur pour un rollout cohérent
    const hash = simpleHash(userId + flagKey);
    const userPercentage = hash % 100;
    
    return userPercentage < flag.rolloutPercentage;
  }

  return true;
}

/**
 * Récupère toutes les features activées pour un utilisateur
 */
export function getEnabledFeatures(userId?: string): string[] {
  return Object.keys(FEATURE_FLAGS).filter(key => isFeatureEnabled(key, userId));
}

/**
 * Récupère les métadonnées d'une feature flag
 */
export function getFeatureMetadata(flagKey: string): Record<string, any> | undefined {
  return FEATURE_FLAGS[flagKey]?.metadata;
}

/**
 * Vérifie si un composant spécifique est activé
 */
export function isComponentEnabled(flagKey: string, componentName: string, userId?: string): boolean {
  if (!isFeatureEnabled(flagKey, userId)) {
    return false;
  }

  const metadata = getFeatureMetadata(flagKey);
  if (!metadata?.components) {
    return true;
  }

  return metadata.components.includes(componentName);
}

/**
 * Hook React pour utiliser les feature flags
 */
export function useFeatureFlag(flagKey: string, userId?: string) {
  const isEnabled = isFeatureEnabled(flagKey, userId);
  const metadata = getFeatureMetadata(flagKey);
  
  return {
    isEnabled,
    metadata,
    isComponentEnabled: (componentName: string) => 
      isComponentEnabled(flagKey, componentName, userId)
  };
}

/**
 * Fonction de hash simple pour le rollout cohérent
 */
function simpleHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

/**
 * Configuration pour l'environnement de développement
 */
export const DEV_OVERRIDES = {
  // Forcer l'activation de toutes les features en développement
  FORCE_ALL_ENABLED: process.env.NODE_ENV === 'development' && process.env.FORCE_ALL_FEATURES === 'true',
  
  // Features spécifiquement désactivées en dev
  DISABLED_IN_DEV: [] as string[],
  
  // Features avec rollout forcé à 100% en dev
  FULL_ROLLOUT_IN_DEV: ['MESSAGING_SYSTEM', 'FRIENDS_SYSTEM', 'SMART_NOTIFICATIONS'] as string[]
};

/**
 * Utilitaire pour les tests
 */
export const FeatureFlagTestUtils = {
  /**
   * Active temporairement une feature pour les tests
   */
  enableFeature: (flagKey: string) => {
    if (process.env.NODE_ENV === 'test') {
      FEATURE_FLAGS[flagKey] = {
        ...FEATURE_FLAGS[flagKey],
        enabled: true,
        rolloutPercentage: 100
      };
    }
  },

  /**
   * Désactive temporairement une feature pour les tests
   */
  disableFeature: (flagKey: string) => {
    if (process.env.NODE_ENV === 'test') {
      FEATURE_FLAGS[flagKey] = {
        ...FEATURE_FLAGS[flagKey],
        enabled: false
      };
    }
  },

  /**
   * Remet les flags à leur état initial
   */
  resetFlags: () => {
    // Logique de reset pour les tests
  }
};

/**
 * Export des constantes pour faciliter l'utilisation
 */
export const FLAGS = {
  MESSAGING_SYSTEM: 'MESSAGING_SYSTEM',
  FRIENDS_SYSTEM: 'FRIENDS_SYSTEM',
  AUTO_COMMUNITIES: 'AUTO_COMMUNITIES',
  SMART_NOTIFICATIONS: 'SMART_NOTIFICATIONS',
  SOCIAL_FEED: 'SOCIAL_FEED',
  ADVANCED_COMMENTS: 'ADVANCED_COMMENTS',
  MUSIC_SHARING: 'MUSIC_SHARING',
  ADVANCED_SEARCH: 'ADVANCED_SEARCH'
} as const;

/**
 * Type pour les clés de feature flags
 */
export type FeatureFlagKey = keyof typeof FLAGS;
