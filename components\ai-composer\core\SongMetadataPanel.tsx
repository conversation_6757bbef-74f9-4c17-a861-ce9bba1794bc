'use client';

import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, User, Image, Music2, Upload, Play, Pause, 
  Save, Edit, Trash2, Download, ExternalLink
} from 'lucide-react';

interface SongMetadata {
  id?: string;
  title: string;
  artist: string;
  description?: string;
  genre?: string[];
  bpm?: number;
  key?: string;
  coverUrl?: string;
  audioUrl?: string;
  duration?: number;
  createdAt?: string;
  updatedAt?: string;
}

interface SongMetadataPanelProps {
  metadata: SongMetadata;
  onMetadataChange: (metadata: SongMetadata) => void;
  onSave?: () => void;
  onCoverUpload?: (file: File) => Promise<string>;
  onAudioUpload?: (file: File) => Promise<string>;
  isEditing?: boolean;
  onEditToggle?: () => void;
  className?: string;
}

/**
 * Panneau de gestion des métadonnées de chanson
 * Réutilise la logique du SongForm pour une cohérence parfaite
 */
export const SongMetadataPanel: React.FC<SongMetadataPanelProps> = ({
  metadata,
  onMetadataChange,
  onSave,
  onCoverUpload,
  onAudioUpload,
  isEditing = false,
  onEditToggle,
  className = ''
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null);

  // Gestion des changements de métadonnées
  const handleFieldChange = useCallback((field: keyof SongMetadata, value: any) => {
    onMetadataChange({
      ...metadata,
      [field]: value,
      updatedAt: new Date().toISOString()
    });
  }, [metadata, onMetadataChange]);

  // Gestion de l'upload de cover
  const handleCoverUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !onCoverUpload) return;

    setIsUploading(true);
    try {
      const coverUrl = await onCoverUpload(file);
      handleFieldChange('coverUrl', coverUrl);
    } catch (error) {
      console.error('Erreur lors de l\'upload de la cover:', error);
    } finally {
      setIsUploading(false);
    }
  }, [onCoverUpload, handleFieldChange]);

  // Gestion de l'upload audio
  const handleAudioUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !onAudioUpload) return;

    setIsUploading(true);
    try {
      const audioUrl = await onAudioUpload(file);
      handleFieldChange('audioUrl', audioUrl);
    } catch (error) {
      console.error('Erreur lors de l\'upload audio:', error);
    } finally {
      setIsUploading(false);
    }
  }, [onAudioUpload, handleFieldChange]);

  // Gestion de la lecture audio
  const handlePlayPause = useCallback(() => {
    if (!metadata.audioUrl) return;

    if (isPlaying) {
      audioElement?.pause();
      setIsPlaying(false);
    } else {
      if (audioElement) {
        audioElement.play();
      } else {
        const audio = new Audio(metadata.audioUrl);
        audio.addEventListener('ended', () => setIsPlaying(false));
        audio.addEventListener('error', () => setIsPlaying(false));
        audio.play();
        setAudioElement(audio);
      }
      setIsPlaying(true);
    }
  }, [metadata.audioUrl, isPlaying, audioElement]);

  // Formatage de la durée
  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'Inconnue';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Métadonnées de la Chanson
          </div>
          <div className="flex items-center gap-2">
            {onEditToggle && (
              <Button
                variant="outline"
                size="sm"
                onClick={onEditToggle}
              >
                <Edit className="w-4 h-4 mr-2" />
                {isEditing ? 'Annuler' : 'Éditer'}
              </Button>
            )}
            {onSave && (
              <Button
                size="sm"
                onClick={onSave}
                disabled={!isEditing}
              >
                <Save className="w-4 h-4 mr-2" />
                Sauvegarder
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Informations principales */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="title">Titre</Label>
            {isEditing ? (
              <Input
                id="title"
                value={metadata.title}
                onChange={(e) => handleFieldChange('title', e.target.value)}
                placeholder="Titre de la chanson"
              />
            ) : (
              <div className="p-2 bg-muted rounded-md">
                {metadata.title || 'Sans titre'}
              </div>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="artist">Artiste</Label>
            {isEditing ? (
              <Input
                id="artist"
                value={metadata.artist}
                onChange={(e) => handleFieldChange('artist', e.target.value)}
                placeholder="Nom de l'artiste"
              />
            ) : (
              <div className="p-2 bg-muted rounded-md">
                {metadata.artist || 'Artiste inconnu'}
              </div>
            )}
          </div>
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          {isEditing ? (
            <Textarea
              id="description"
              value={metadata.description || ''}
              onChange={(e) => handleFieldChange('description', e.target.value)}
              placeholder="Description de la chanson..."
              rows={3}
            />
          ) : (
            <div className="p-2 bg-muted rounded-md min-h-[80px]">
              {metadata.description || 'Aucune description'}
            </div>
          )}
        </div>

        {/* Informations musicales */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label htmlFor="bpm">BPM</Label>
            {isEditing ? (
              <Input
                id="bpm"
                type="number"
                value={metadata.bpm || ''}
                onChange={(e) => handleFieldChange('bpm', parseInt(e.target.value) || undefined)}
                placeholder="120"
              />
            ) : (
              <div className="p-2 bg-muted rounded-md text-center">
                {metadata.bpm || '—'}
              </div>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="key">Tonalité</Label>
            {isEditing ? (
              <Input
                id="key"
                value={metadata.key || ''}
                onChange={(e) => handleFieldChange('key', e.target.value)}
                placeholder="C"
              />
            ) : (
              <div className="p-2 bg-muted rounded-md text-center">
                {metadata.key || '—'}
              </div>
            )}
          </div>
          
          <div className="space-y-2">
            <Label>Durée</Label>
            <div className="p-2 bg-muted rounded-md text-center">
              {formatDuration(metadata.duration)}
            </div>
          </div>
          
          <div className="space-y-2">
            <Label>Genres</Label>
            <div className="p-2 bg-muted rounded-md min-h-[40px] flex flex-wrap gap-1">
              {metadata.genre?.map((g, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {g}
                </Badge>
              )) || <span className="text-muted-foreground text-sm">Aucun</span>}
            </div>
          </div>
        </div>

        {/* Cover et Audio */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Cover */}
          <div className="space-y-4">
            <Label>Image de couverture</Label>
            <div className="space-y-2">
              {metadata.coverUrl ? (
                <div className="relative">
                  <img
                    src={metadata.coverUrl}
                    alt="Cover"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  {isEditing && (
                    <Button
                      variant="destructive"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => handleFieldChange('coverUrl', undefined)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ) : (
                <div className="w-full h-48 border-2 border-dashed border-muted-foreground/30 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <Image className="w-12 h-12 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">Aucune image</p>
                  </div>
                </div>
              )}
              
              {isEditing && (
                <div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleCoverUpload}
                    className="hidden"
                    id="cover-upload"
                  />
                  <Button
                    variant="outline"
                    onClick={() => document.getElementById('cover-upload')?.click()}
                    disabled={isUploading}
                    className="w-full"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    {isUploading ? 'Upload...' : 'Changer l\'image'}
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Audio */}
          <div className="space-y-4">
            <Label>Fichier audio</Label>
            <div className="space-y-2">
              {metadata.audioUrl ? (
                <div className="p-4 border rounded-lg space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Music2 className="w-5 h-5 text-green-600" />
                      <span className="text-sm font-medium">Audio disponible</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handlePlayPause}
                    >
                      {isPlaying ? (
                        <Pause className="w-4 h-4" />
                      ) : (
                        <Play className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                  
                  {isEditing && (
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(metadata.audioUrl, '_blank')}
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Ouvrir
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleFieldChange('audioUrl', undefined)}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Supprimer
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="p-4 border-2 border-dashed border-muted-foreground/30 rounded-lg text-center">
                  <Music2 className="w-12 h-12 mx-auto mb-2 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">Aucun fichier audio</p>
                </div>
              )}
              
              {isEditing && (
                <div>
                  <input
                    type="file"
                    accept="audio/*"
                    onChange={handleAudioUpload}
                    className="hidden"
                    id="audio-upload"
                  />
                  <Button
                    variant="outline"
                    onClick={() => document.getElementById('audio-upload')?.click()}
                    disabled={isUploading}
                    className="w-full"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    {isUploading ? 'Upload...' : 'Ajouter un fichier audio'}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Informations système */}
        {(metadata.createdAt || metadata.updatedAt) && (
          <div className="pt-4 border-t">
            <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
              {metadata.createdAt && (
                <div>
                  <span className="font-medium">Créé le:</span>
                  <br />
                  {new Date(metadata.createdAt).toLocaleString()}
                </div>
              )}
              {metadata.updatedAt && (
                <div>
                  <span className="font-medium">Modifié le:</span>
                  <br />
                  {new Date(metadata.updatedAt).toLocaleString()}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
