/**
 * Tests pour le module AI Composer refactorisé
 * Validation des fonctionnalités principales
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

// Import des composants refactorisés
import { AIComposerWorkspace } from '../core/AIComposerWorkspace';
import { LyricsEditor } from '../lyrics/LyricsEditor';
import { ChordSystemProvider } from '../chords/ChordSystemProvider';
import { SongMetadataPanel } from '../core/SongMetadataPanel';

// Mock des dépendances
jest.mock('@/lib/chords/midi-chord-player', () => ({
  MidiChordPlayer: jest.fn().mockImplementation(() => ({
    playChord: jest.fn(),
    playArpeggio: jest.fn(),
    stopAll: jest.fn(),
  })),
}));

// Mock des données d'accords
jest.mock('@/lib/chords/guitar.json', () => ({
  instrument: 'guitar',
  chords: {
    'C': [{ frets: [0, 1, 0, 2, 3, 0], midi: [60, 64, 67] }],
    'Am': [{ frets: [0, 0, 2, 2, 1, 0], midi: [57, 60, 64] }],
  }
}));

jest.mock('@/lib/chords/piano.json', () => ({
  instrument: 'piano',
  chords: {
    'C': [{ midi: [60, 64, 67] }],
    'Am': [{ midi: [57, 60, 64] }],
  }
}));

jest.mock('@/lib/chords/ukulele_gcea_complete.json', () => ({
  instrument: 'ukulele',
  chords: {
    'C': [{ frets: [0, 0, 0, 3], midi: [60, 64, 67] }],
    'Am': [{ frets: [2, 0, 0, 0], midi: [57, 60, 64] }],
  }
}));

describe('AI Composer - Architecture Refactorisée', () => {
  
  describe('AIComposerWorkspace', () => {
    const mockProps = {
      songId: 'test-song-id',
      initialData: {
        title: 'Test Song',
        artist: 'Test Artist',
        lyrics: 'Test lyrics with [C] and [Am] chords',
        chords: [],
        sections: []
      },
      onSave: jest.fn(),
      onMetadataSave: jest.fn(),
      onCoverUpload: jest.fn(),
      onAudioUpload: jest.fn(),
    };

    it('devrait rendre le workspace avec tous les onglets', () => {
      render(<AIComposerWorkspace {...mockProps} />);
      
      // Vérifier la présence des 5 onglets
      expect(screen.getByText('Paroles')).toBeInTheDocument();
      expect(screen.getByText('Accords')).toBeInTheDocument();
      expect(screen.getByText('Structure')).toBeInTheDocument();
      expect(screen.getByText('Métadonnées')).toBeInTheDocument();
      expect(screen.getByText('Configuration')).toBeInTheDocument();
    });

    it('devrait afficher le titre et les informations de base', () => {
      render(<AIComposerWorkspace {...mockProps} />);
      
      expect(screen.getByText('AI Composer')).toBeInTheDocument();
      expect(screen.getByText('Assistant de composition musicale')).toBeInTheDocument();
    });

    it('devrait permettre la navigation entre les onglets', () => {
      render(<AIComposerWorkspace {...mockProps} />);
      
      // Cliquer sur l'onglet Accords
      fireEvent.click(screen.getByText('Accords'));
      expect(screen.getByText('Système d\'Accords Professionnel')).toBeInTheDocument();
      
      // Cliquer sur l'onglet Métadonnées
      fireEvent.click(screen.getByText('Métadonnées'));
      expect(screen.getByText('Métadonnées de la Chanson')).toBeInTheDocument();
    });
  });

  describe('LyricsEditor - 4 Modes de Visualisation', () => {
    const mockProps = {
      value: 'Couplet 1:\n[C]Je marche sous la [Am]pluie\n\nRefrain:\n[F]Et je [G]chante ma [C]vie',
      onChange: jest.fn(),
      chords: [],
      onChordsChange: jest.fn(),
      displayMode: 'hybrid' as const,
      onDisplayModeChange: jest.fn(),
    };

    it('devrait détecter les accords dans le texte', () => {
      render(<LyricsEditor {...mockProps} />);
      
      // Vérifier la détection des accords
      expect(screen.getByText('4 accords détectés')).toBeInTheDocument();
    });

    it('devrait afficher les boutons de mode de visualisation', () => {
      render(<LyricsEditor {...mockProps} />);
      
      // Vérifier la présence des 4 modes
      const textButton = screen.getByRole('button', { name: /texte/i });
      const chordsButton = screen.getByRole('button', { name: /accords/i });
      const hybridButton = screen.getByRole('button', { name: /hybride/i });
      const enhancedButton = screen.getByRole('button', { name: /avancé/i });
      
      expect(textButton).toBeInTheDocument();
      expect(chordsButton).toBeInTheDocument();
      expect(hybridButton).toBeInTheDocument();
      expect(enhancedButton).toBeInTheDocument();
    });

    it('devrait permettre le changement de mode', () => {
      const onDisplayModeChange = jest.fn();
      render(<LyricsEditor {...mockProps} onDisplayModeChange={onDisplayModeChange} />);
      
      // Cliquer sur le mode texte
      const textButton = screen.getByRole('button', { name: /texte/i });
      fireEvent.click(textButton);
      
      expect(onDisplayModeChange).toHaveBeenCalledWith('text');
    });

    it('devrait proposer la synchronisation des accords', () => {
      render(<LyricsEditor {...mockProps} />);
      
      expect(screen.getByText('Synchroniser')).toBeInTheDocument();
    });
  });

  describe('ChordSystemProvider - Intégration lib/chords', () => {
    const mockProps = {
      selectedChords: [],
      onChordsChange: jest.fn(),
    };

    it('devrait afficher le système d\'accords professionnel', () => {
      render(<ChordSystemProvider {...mockProps} />);
      
      expect(screen.getByText('Système d\'Accords Professionnel')).toBeInTheDocument();
    });

    it('devrait afficher les contrôles d\'instrument et de tonalité', () => {
      render(<ChordSystemProvider {...mockProps} />);
      
      expect(screen.getByText('Instrument:')).toBeInTheDocument();
      expect(screen.getByText('Tonalité:')).toBeInTheDocument();
    });

    it('devrait afficher les onglets de navigation', () => {
      render(<ChordSystemProvider {...mockProps} />);
      
      expect(screen.getByText('Navigateur')).toBeInTheDocument();
      expect(screen.getByText(/Sélectionnés/)).toBeInTheDocument();
      expect(screen.getByText('Progressions')).toBeInTheDocument();
    });

    it('devrait permettre la recherche d\'accords', () => {
      render(<ChordSystemProvider {...mockProps} />);
      
      const searchInput = screen.getByPlaceholderText('Rechercher un accord...');
      expect(searchInput).toBeInTheDocument();
    });
  });

  describe('SongMetadataPanel - Gestion Métadonnées', () => {
    const mockMetadata = {
      title: 'Test Song',
      artist: 'Test Artist',
      description: 'Test description',
      bpm: 120,
      key: 'C',
      genre: ['Rock', 'Pop'],
    };

    const mockProps = {
      metadata: mockMetadata,
      onMetadataChange: jest.fn(),
      onSave: jest.fn(),
      isEditing: false,
      onEditToggle: jest.fn(),
    };

    it('devrait afficher les métadonnées en mode lecture', () => {
      render(<SongMetadataPanel {...mockProps} />);
      
      expect(screen.getByText('Test Song')).toBeInTheDocument();
      expect(screen.getByText('Test Artist')).toBeInTheDocument();
      expect(screen.getByText('Test description')).toBeInTheDocument();
      expect(screen.getByText('120')).toBeInTheDocument();
      expect(screen.getByText('C')).toBeInTheDocument();
    });

    it('devrait permettre l\'édition des métadonnées', () => {
      render(<SongMetadataPanel {...mockProps} isEditing={true} />);
      
      // Vérifier la présence des champs d'édition
      expect(screen.getByDisplayValue('Test Song')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Artist')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test description')).toBeInTheDocument();
    });

    it('devrait afficher les boutons d\'action', () => {
      render(<SongMetadataPanel {...mockProps} />);
      
      expect(screen.getByText('Éditer')).toBeInTheDocument();
      expect(screen.getByText('Sauvegarder')).toBeInTheDocument();
    });

    it('devrait gérer l\'upload de fichiers', () => {
      render(<SongMetadataPanel {...mockProps} isEditing={true} />);
      
      expect(screen.getByText('Changer l\'image')).toBeInTheDocument();
      expect(screen.getByText('Ajouter un fichier audio')).toBeInTheDocument();
    });
  });

  describe('Intégration et Cohérence', () => {
    it('devrait exporter tous les composants principaux', () => {
      // Test d'import pour vérifier l'architecture
      expect(AIComposerWorkspace).toBeDefined();
      expect(LyricsEditor).toBeDefined();
      expect(ChordSystemProvider).toBeDefined();
      expect(SongMetadataPanel).toBeDefined();
    });

    it('devrait maintenir la cohérence des props entre composants', () => {
      // Vérifier que les interfaces sont cohérentes
      const workspaceProps = {
        songId: 'test',
        initialData: {},
        onSave: jest.fn(),
        onMetadataSave: jest.fn(),
      };

      expect(() => render(<AIComposerWorkspace {...workspaceProps} />)).not.toThrow();
    });
  });
});

describe('Performance et Optimisation', () => {
  it('devrait gérer efficacement les gros volumes d\'accords', async () => {
    const mockProps = {
      selectedChords: [],
      onChordsChange: jest.fn(),
    };

    const { container } = render(<ChordSystemProvider {...mockProps} />);
    
    // Vérifier que le composant se rend sans problème
    expect(container).toBeInTheDocument();
    
    // Simuler une recherche
    const searchInput = screen.getByPlaceholderText('Rechercher un accord...');
    fireEvent.change(searchInput, { target: { value: 'C' } });
    
    // Vérifier que la recherche ne bloque pas l'interface
    await waitFor(() => {
      expect(searchInput).toHaveValue('C');
    });
  });
});
