"use client";

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Bell, 
  Settings, 
  Volume2, 
  VolumeX, 
  Smartphone, 
  Mail,
  Moon,
  Sun
} from 'lucide-react';
import { useNotifications, NotificationSettings } from '@/hooks/use-notifications';

interface NotificationSettingsProps {
  userId?: string;
}

export default function NotificationSettingsComponent({ userId }: NotificationSettingsProps) {
  const { settings, updateSettings, isQuietHours } = useNotifications(userId);

  const handleSettingChange = (key: keyof NotificationSettings, value: any) => {
    updateSettings({ [key]: value });
  };

  const handleQuietHoursChange = (key: 'enabled' | 'start' | 'end', value: any) => {
    updateSettings({
      quietHours: {
        ...settings.quietHours,
        [key]: value
      }
    });
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="border-slate-600 text-slate-300">
          <Settings className="h-4 w-4 mr-2" />
          Paramètres notifications
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-slate-800 border-slate-700 max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Paramètres des notifications
          </DialogTitle>
          <DialogDescription className="text-slate-400">
            Configurez comment et quand vous souhaitez recevoir des notifications
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Affichage général */}
          <Card className="bg-slate-700/50 border-slate-600">
            <CardHeader className="pb-4">
              <CardTitle className="text-white text-lg">Affichage</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-slate-300">Afficher dans la sidebar</Label>
                  <p className="text-sm text-slate-400">
                    Affiche les badges de notification dans la navigation
                  </p>
                </div>
                <Switch
                  checked={settings.showInSidebar}
                  onCheckedChange={(checked) => handleSettingChange('showInSidebar', checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Notifications sonores */}
          <Card className="bg-slate-700/50 border-slate-600">
            <CardHeader className="pb-4">
              <CardTitle className="text-white text-lg flex items-center gap-2">
                {settings.enableSound ? <Volume2 className="h-5 w-5" /> : <VolumeX className="h-5 w-5" />}
                Notifications sonores
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-slate-300">Activer les sons</Label>
                  <p className="text-sm text-slate-400">
                    Joue un son lors de nouvelles notifications
                  </p>
                </div>
                <Switch
                  checked={settings.enableSound}
                  onCheckedChange={(checked) => handleSettingChange('enableSound', checked)}
                />
              </div>

              {settings.enableSound && (
                <div className="pl-4 border-l-2 border-slate-600">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="border-slate-600 text-slate-300"
                    onClick={() => {
                      // Test du son de notification
                      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
                      const oscillator = audioContext.createOscillator();
                      const gainNode = audioContext.createGain();
                      oscillator.connect(gainNode);
                      gainNode.connect(audioContext.destination);
                      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                      oscillator.start();
                      oscillator.stop(audioContext.currentTime + 0.2);
                    }}
                  >
                    <Volume2 className="h-4 w-4 mr-2" />
                    Tester le son
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Heures silencieuses */}
          <Card className="bg-slate-700/50 border-slate-600">
            <CardHeader className="pb-4">
              <CardTitle className="text-white text-lg flex items-center gap-2">
                {isQuietHours ? <Moon className="h-5 w-5" /> : <Sun className="h-5 w-5" />}
                Heures silencieuses
                {isQuietHours && (
                  <span className="text-sm bg-purple-500/20 text-purple-400 px-2 py-1 rounded">
                    Actif maintenant
                  </span>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-slate-300">Activer les heures silencieuses</Label>
                  <p className="text-sm text-slate-400">
                    Désactive les sons pendant certaines heures
                  </p>
                </div>
                <Switch
                  checked={settings.quietHours.enabled}
                  onCheckedChange={(checked) => handleQuietHoursChange('enabled', checked)}
                />
              </div>

              {settings.quietHours.enabled && (
                <div className="grid grid-cols-2 gap-4 pl-4 border-l-2 border-slate-600">
                  <div className="space-y-2">
                    <Label className="text-slate-300">Début</Label>
                    <Input
                      type="time"
                      value={settings.quietHours.start}
                      onChange={(e) => handleQuietHoursChange('start', e.target.value)}
                      className="bg-slate-600 border-slate-500 text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-slate-300">Fin</Label>
                    <Input
                      type="time"
                      value={settings.quietHours.end}
                      onChange={(e) => handleQuietHoursChange('end', e.target.value)}
                      className="bg-slate-600 border-slate-500 text-white"
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notifications push et email (futures fonctionnalités) */}
          <Card className="bg-slate-700/50 border-slate-600 opacity-60">
            <CardHeader className="pb-4">
              <CardTitle className="text-white text-lg">Notifications externes</CardTitle>
              <p className="text-sm text-slate-400">Fonctionnalités à venir</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-slate-300 flex items-center gap-2">
                    <Smartphone className="h-4 w-4" />
                    Notifications push
                  </Label>
                  <p className="text-sm text-slate-400">
                    Recevoir des notifications sur votre appareil
                  </p>
                </div>
                <Switch
                  checked={settings.enablePush}
                  onCheckedChange={(checked) => handleSettingChange('enablePush', checked)}
                  disabled
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-slate-300 flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Notifications email
                  </Label>
                  <p className="text-sm text-slate-400">
                    Recevoir un résumé quotidien par email
                  </p>
                </div>
                <Switch
                  checked={settings.enableEmail}
                  onCheckedChange={(checked) => handleSettingChange('enableEmail', checked)}
                  disabled
                />
              </div>
            </CardContent>
          </Card>

          {/* Résumé des paramètres */}
          <Card className="bg-blue-500/10 border-blue-500/20">
            <CardContent className="p-4">
              <h4 className="text-blue-400 font-medium mb-2">Résumé de vos paramètres</h4>
              <div className="space-y-1 text-sm">
                <p className="text-slate-300">
                  • Sidebar: {settings.showInSidebar ? '✅ Activée' : '❌ Désactivée'}
                </p>
                <p className="text-slate-300">
                  • Sons: {settings.enableSound ? '✅ Activés' : '❌ Désactivés'}
                </p>
                <p className="text-slate-300">
                  • Heures silencieuses: {settings.quietHours.enabled ? `✅ ${settings.quietHours.start} - ${settings.quietHours.end}` : '❌ Désactivées'}
                </p>
                {isQuietHours && (
                  <p className="text-purple-400">
                    🌙 Mode silencieux actuellement actif
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
