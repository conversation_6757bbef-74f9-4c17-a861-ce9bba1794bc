"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Radio, 
  Users, 
  Search, 
  Settings,
  Music,
  TrendingUp,
  Calendar,
  MessageCircle,
  Heart,
  Share2,
  Plus,
  Crown,
  Star
} from 'lucide-react';

export default function CommunitiesPage() {
  const [activeTab, setActiveTab] = useState('my-communities');
  const [searchQuery, setSearchQuery] = useState('');

  // Données de démonstration
  const myCommunities = [
    {
      id: '1',
      name: 'Communauté Jazz',
      description: 'Espace dédié aux passionnés de jazz et d\'improvisation',
      avatar: '/communities/jazz.jpg',
      banner: '/communities/jazz-banner.jpg',
      memberCount: 1247,
      contentCount: 89,
      isAutoJoined: true,
      role: 'member',
      lastActivity: 'Il y a 2h',
      tags: ['Jazz', 'Improvisation', 'Piano'],
      recentActivity: 'Alice a partagé un nouveau morceau'
    },
    {
      id: '2',
      name: 'Communauté Electronic',
      description: 'Producteurs et fans de musique électronique',
      avatar: '/communities/electronic.jpg',
      banner: '/communities/electronic-banner.jpg',
      memberCount: 2156,
      contentCount: 234,
      isAutoJoined: false,
      role: 'moderator',
      lastActivity: 'Il y a 1h',
      tags: ['Electronic', 'Synthwave', 'Techno'],
      recentActivity: 'Bob a organisé un événement de collaboration'
    },
    {
      id: '3',
      name: 'Communauté Rock',
      description: 'Guitaristes, batteurs et rockeurs de tous horizons',
      avatar: '/communities/rock.jpg',
      banner: '/communities/rock-banner.jpg',
      memberCount: 3421,
      contentCount: 567,
      isAutoJoined: true,
      role: 'admin',
      lastActivity: 'Il y a 30min',
      tags: ['Rock', 'Guitare', 'Alternative'],
      recentActivity: 'Charlie a lancé un défi de composition'
    }
  ];

  const discoverCommunities = [
    {
      id: '4',
      name: 'Communauté Hip-Hop',
      description: 'Rappeurs, beatmakers et culture hip-hop',
      avatar: '/communities/hiphop.jpg',
      memberCount: 1876,
      contentCount: 345,
      tags: ['Hip-Hop', 'Rap', 'Beatmaking'],
      trending: true,
      reason: 'Basé sur vos goûts musicaux'
    },
    {
      id: '5',
      name: 'Communauté Classical',
      description: 'Musique classique et orchestrale',
      avatar: '/communities/classical.jpg',
      memberCount: 892,
      contentCount: 123,
      tags: ['Classical', 'Orchestre', 'Composition'],
      trending: false,
      reason: 'Amis en commun'
    },
    {
      id: '6',
      name: 'Communauté Indie',
      description: 'Artistes indépendants et musique alternative',
      avatar: '/communities/indie.jpg',
      memberCount: 1543,
      contentCount: 289,
      tags: ['Indie', 'Alternative', 'DIY'],
      trending: true,
      reason: 'Populaire dans votre région'
    }
  ];

  const trendingContent = [
    {
      id: '1',
      type: 'song',
      title: 'Midnight Jazz Session',
      author: 'Alice Martin',
      community: 'Communauté Jazz',
      likes: 45,
      comments: 12,
      timestamp: 'Il y a 2h'
    },
    {
      id: '2',
      type: 'collaboration',
      title: 'Recherche guitariste pour projet rock',
      author: 'Charlie Dubois',
      community: 'Communauté Rock',
      likes: 23,
      comments: 8,
      timestamp: 'Il y a 4h'
    },
    {
      id: '3',
      type: 'event',
      title: 'Jam Session Electronic - Samedi 20h',
      author: 'Bob Wilson',
      community: 'Communauté Electronic',
      likes: 67,
      comments: 15,
      timestamp: 'Il y a 6h'
    }
  ];

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <Crown className="h-4 w-4 text-yellow-400" />;
      case 'moderator': return <Star className="h-4 w-4 text-blue-400" />;
      default: return null;
    }
  };

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'song': return <Music className="h-4 w-4" />;
      case 'collaboration': return <Users className="h-4 w-4" />;
      case 'event': return <Calendar className="h-4 w-4" />;
      default: return <MessageCircle className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            🏷️ Communautés Musicales
          </h1>
          <p className="text-slate-400">
            Rejoignez des communautés basées sur vos goûts musicaux et découvrez de nouveaux talents
          </p>
        </div>

        {/* Barre de recherche */}
        <Card className="bg-slate-800/50 border-slate-700 mb-6">
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
              <Input 
                placeholder="Rechercher des communautés par genre, style..." 
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 bg-slate-700 border-slate-600 text-white text-lg h-12"
              />
            </div>
          </CardContent>
        </Card>

        {/* Onglets */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="bg-slate-800 border-slate-700">
            <TabsTrigger value="my-communities" className="data-[state=active]:bg-slate-700">
              <Radio className="h-4 w-4 mr-2" />
              Mes Communautés ({myCommunities.length})
            </TabsTrigger>
            <TabsTrigger value="discover" className="data-[state=active]:bg-slate-700">
              <TrendingUp className="h-4 w-4 mr-2" />
              Découvrir
            </TabsTrigger>
            <TabsTrigger value="trending" className="data-[state=active]:bg-slate-700">
              <Star className="h-4 w-4 mr-2" />
              Tendances
            </TabsTrigger>
          </TabsList>

          {/* Onglet Mes Communautés */}
          <TabsContent value="my-communities">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {myCommunities.map((community) => (
                <Card key={community.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors overflow-hidden">
                  {/* Banner */}
                  <div className="h-24 bg-gradient-to-r from-blue-500/20 to-purple-500/20 relative">
                    <div className="absolute inset-0 bg-black/20"></div>
                    <div className="absolute bottom-2 left-4 flex items-center gap-2">
                      <Avatar className="h-12 w-12 border-2 border-white/20">
                        <AvatarImage src={community.avatar} />
                        <AvatarFallback className="bg-slate-600 text-white">
                          {community.name.split(' ')[1]?.[0] || community.name[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold text-white text-lg">{community.name}</h3>
                        <div className="flex items-center gap-1">
                          {getRoleIcon(community.role)}
                          <span className="text-sm text-slate-300 capitalize">{community.role}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <CardContent className="p-6">
                    <p className="text-slate-300 mb-4">{community.description}</p>

                    <div className="flex flex-wrap gap-1 mb-4">
                      {community.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="border-slate-600 text-slate-300">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    <div className="flex items-center justify-between text-sm text-slate-400 mb-4">
                      <span>{community.memberCount.toLocaleString()} membres</span>
                      <span>{community.contentCount} contenus</span>
                      <span>{community.lastActivity}</span>
                    </div>

                    <div className="bg-slate-700/50 rounded-lg p-3 mb-4">
                      <p className="text-sm text-slate-300">
                        <MessageCircle className="h-4 w-4 inline mr-2" />
                        {community.recentActivity}
                      </p>
                    </div>

                    <div className="flex gap-2">
                      <Button size="sm" className="flex-1 bg-blue-500 hover:bg-blue-600">
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Voir les discussions
                      </Button>
                      <Button size="sm" variant="outline" className="border-slate-600">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Onglet Découvrir */}
          <TabsContent value="discover">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {discoverCommunities.map((community) => (
                <Card key={community.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4 mb-4">
                      <Avatar className="h-16 w-16">
                        <AvatarImage src={community.avatar} />
                        <AvatarFallback className="bg-slate-600 text-white text-lg">
                          {community.name.split(' ')[1]?.[0] || community.name[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-white">{community.name}</h3>
                          {community.trending && (
                            <Badge className="bg-orange-500 text-white text-xs">
                              <TrendingUp className="h-3 w-3 mr-1" />
                              Tendance
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-blue-400">{community.reason}</p>
                      </div>
                    </div>

                    <p className="text-slate-300 text-sm mb-4">{community.description}</p>

                    <div className="flex flex-wrap gap-1 mb-4">
                      {community.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="border-slate-600 text-slate-300 text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    <div className="flex items-center justify-between text-sm text-slate-400 mb-4">
                      <span>{community.memberCount.toLocaleString()} membres</span>
                      <span>{community.contentCount} contenus</span>
                    </div>

                    <Button size="sm" className="w-full bg-green-500 hover:bg-green-600">
                      <Plus className="h-4 w-4 mr-2" />
                      Rejoindre
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Onglet Tendances */}
          <TabsContent value="trending">
            <div className="space-y-4">
              {trendingContent.map((content) => (
                <Card key={content.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0">
                        <div className="h-12 w-12 bg-slate-700 rounded-lg flex items-center justify-center text-slate-300">
                          {getContentTypeIcon(content.type)}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-white">{content.title}</h3>
                          <Badge variant="outline" className="border-slate-600 text-slate-300 text-xs">
                            {content.community}
                          </Badge>
                        </div>
                        <p className="text-slate-400 text-sm mb-3">
                          Par {content.author} • {content.timestamp}
                        </p>
                        <div className="flex items-center gap-4 text-sm text-slate-400">
                          <div className="flex items-center gap-1">
                            <Heart className="h-4 w-4" />
                            {content.likes}
                          </div>
                          <div className="flex items-center gap-1">
                            <MessageCircle className="h-4 w-4" />
                            {content.comments}
                          </div>
                          <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white p-0">
                            <Share2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Feature Flag Notice */}
        <div className="mt-8">
          <Card className="bg-amber-500/10 border-amber-500/20">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-amber-400">
                <Settings className="h-5 w-5" />
                <span className="font-medium">Version Bêta</span>
              </div>
              <p className="text-amber-300 mt-1">
                Les communautés automatiques sont en cours de déploiement. L'adhésion automatique basée sur les tags sera bientôt disponible.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
