import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const filter = searchParams.get('filter') || 'all';
    const search = searchParams.get('search') || '';

    // Construire la requête de base
    let query = supabase
      .from('conversations')
      .select(`
        *,
        conversation_participants!inner(
          user_id,
          last_read_at,
          notification_level,
          is_muted,
          is_pinned
        ),
        messages(
          id,
          content,
          sender_id,
          created_at,
          profiles:sender_id(name)
        )
      `)
      .eq('conversation_participants.user_id', user.id)
      .order('last_message_at', { ascending: false });

    // Appliquer les filtres
    if (filter === 'unread') {
      query = query.gt('conversation_participants.last_read_at', 'last_message_at');
    } else if (filter === 'pinned') {
      query = query.eq('conversation_participants.is_pinned', true);
    }

    // Recherche
    if (search) {
      query = query.ilike('name', `%${search}%`);
    }

    const { data: conversations, error } = await query;

    if (error) {
      console.error('Erreur chargement conversations:', error);
      return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
    }

    // Formater les données pour le frontend
    const formattedConversations = conversations?.map(conv => {
      const lastMessage = conv.messages?.[0];
      const participant = conv.conversation_participants?.[0];
      
      return {
        id: conv.id,
        type: conv.type,
        name: conv.name,
        avatar_url: conv.avatar_url,
        participants: [], // À compléter avec les vrais participants
        last_message: lastMessage ? {
          content: lastMessage.content,
          sender_name: lastMessage.profiles?.name || 'Inconnu',
          created_at: lastMessage.created_at
        } : null,
        unread_count: 0, // À calculer
        is_pinned: participant?.is_pinned || false,
        is_muted: participant?.is_muted || false,
        updated_at: conv.updated_at
      };
    }) || [];

    return NextResponse.json({ conversations: formattedConversations });
  } catch (error) {
    console.error('Erreur API conversations:', error);
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
    }

    const body = await request.json();
    const { type, name, description, participant_ids } = body;

    // Créer la conversation
    const { data: conversation, error: convError } = await supabase
      .from('conversations')
      .insert({
        type,
        name,
        description,
        created_by: user.id
      })
      .select()
      .single();

    if (convError) {
      console.error('Erreur création conversation:', convError);
      return NextResponse.json({ error: 'Erreur création conversation' }, { status: 500 });
    }

    // Ajouter les participants
    const participants = [
      { conversation_id: conversation.id, user_id: user.id, role: 'admin' },
      ...participant_ids.map((id: string) => ({
        conversation_id: conversation.id,
        user_id: id,
        role: 'member'
      }))
    ];

    const { error: participantsError } = await supabase
      .from('conversation_participants')
      .insert(participants);

    if (participantsError) {
      console.error('Erreur ajout participants:', participantsError);
      return NextResponse.json({ error: 'Erreur ajout participants' }, { status: 500 });
    }

    return NextResponse.json({ conversation });
  } catch (error) {
    console.error('Erreur API création conversation:', error);
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
  }
}
