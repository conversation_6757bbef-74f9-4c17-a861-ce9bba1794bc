"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Users,
  UserPlus,
  Search,
  Settings,
  Heart,
  Music,
  MessageCircle,
  MoreHorizontal,
  UserCheck,
  UserX,
  Clock,
  Crown,
  Star,
  Briefcase,
  Mic,
  Guitar,
  Headphones,
  ChevronDown,
  Tag,
  Filter
} from 'lucide-react';

// Types pour le système d'amis avancé
type RelationshipType =
  | 'close_friend' | 'friend' | 'acquaintance'
  | 'collaborator' | 'band_member' | 'producer' | 'manager'
  | 'mentor' | 'student' | 'inspiration'
  | 'fan' | 'supporter' | 'follower'
  | 'sound_engineer' | 'lyricist' | 'composer' | 'instrumentalist';

interface RelationshipOption {
  value: RelationshipType;
  label: string;
  icon: React.ReactNode;
  category: 'personal' | 'professional' | 'artistic' | 'community' | 'specialized';
  color: string;
}

export default function FriendsPage() {
  const [activeTab, setActiveTab] = useState('friends');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterByRelationship, setFilterByRelationship] = useState<RelationshipType | 'all'>('all');

  // Configuration des types de relations
  const relationshipOptions: RelationshipOption[] = [
    // Relations personnelles
    { value: 'close_friend', label: 'Ami proche', icon: <Heart className="h-4 w-4" />, category: 'personal', color: 'bg-pink-500' },
    { value: 'friend', label: 'Ami', icon: <Users className="h-4 w-4" />, category: 'personal', color: 'bg-green-500' },
    { value: 'acquaintance', label: 'Connaissance', icon: <UserCheck className="h-4 w-4" />, category: 'personal', color: 'bg-gray-500' },

    // Relations professionnelles
    { value: 'collaborator', label: 'Collaborateur', icon: <Briefcase className="h-4 w-4" />, category: 'professional', color: 'bg-blue-500' },
    { value: 'band_member', label: 'Membre du groupe', icon: <Users className="h-4 w-4" />, category: 'professional', color: 'bg-purple-500' },
    { value: 'producer', label: 'Producteur', icon: <Crown className="h-4 w-4" />, category: 'professional', color: 'bg-yellow-500' },
    { value: 'manager', label: 'Manager', icon: <Briefcase className="h-4 w-4" />, category: 'professional', color: 'bg-orange-500' },

    // Relations artistiques
    { value: 'mentor', label: 'Mentor', icon: <Star className="h-4 w-4" />, category: 'artistic', color: 'bg-indigo-500' },
    { value: 'student', label: 'Élève', icon: <UserCheck className="h-4 w-4" />, category: 'artistic', color: 'bg-cyan-500' },
    { value: 'inspiration', label: 'Inspiration', icon: <Star className="h-4 w-4" />, category: 'artistic', color: 'bg-rose-500' },

    // Relations communautaires
    { value: 'fan', label: 'Fan', icon: <Heart className="h-4 w-4" />, category: 'community', color: 'bg-red-500' },
    { value: 'supporter', label: 'Supporter', icon: <UserPlus className="h-4 w-4" />, category: 'community', color: 'bg-emerald-500' },
    { value: 'follower', label: 'Abonné', icon: <UserCheck className="h-4 w-4" />, category: 'community', color: 'bg-slate-500' },

    // Relations spécialisées
    { value: 'sound_engineer', label: 'Ingénieur son', icon: <Headphones className="h-4 w-4" />, category: 'specialized', color: 'bg-teal-500' },
    { value: 'lyricist', label: 'Parolier', icon: <Mic className="h-4 w-4" />, category: 'specialized', color: 'bg-violet-500' },
    { value: 'composer', label: 'Compositeur', icon: <Music className="h-4 w-4" />, category: 'specialized', color: 'bg-amber-500' },
    { value: 'instrumentalist', label: 'Instrumentiste', icon: <Guitar className="h-4 w-4" />, category: 'specialized', color: 'bg-lime-500' }
  ];

  // Données de démonstration enrichies
  const friends = [
    {
      id: '1',
      name: 'Alice Martin',
      username: '@alice_music',
      avatar: '/avatars/alice.jpg',
      status: 'online',
      lastSeen: 'En ligne',
      mutualFriends: 5,
      relationshipType: 'close_friend' as RelationshipType,
      commonInterests: ['Jazz', 'Piano', 'Composition'],
      collaborationHistory: 3,
      lastCollaboration: 'Album "Midnight Jazz" (2024)',
      location: 'Paris, France',
      instruments: ['Piano', 'Synthé'],
      experience: 'Avancé'
    },
    {
      id: '2',
      name: 'Bob Wilson',
      username: '@bob_beats',
      avatar: '/avatars/bob.jpg',
      status: 'offline',
      lastSeen: 'Il y a 2h',
      mutualFriends: 3,
      relationshipType: 'collaborator' as RelationshipType,
      commonInterests: ['Electronic', 'Production', 'Synthé'],
      collaborationHistory: 7,
      lastCollaboration: 'EP "Digital Dreams" (2024)',
      location: 'Lyon, France',
      instruments: ['Synthé', 'Drum Machine'],
      experience: 'Expert'
    },
    {
      id: '3',
      name: 'Charlie Dubois',
      username: '@charlie_guitar',
      avatar: '/avatars/charlie.jpg',
      status: 'away',
      lastSeen: 'Il y a 30min',
      mutualFriends: 8,
      relationshipType: 'band_member' as RelationshipType,
      commonInterests: ['Rock', 'Guitare', 'Songwriting'],
      collaborationHistory: 12,
      lastCollaboration: 'Album "Electric Nights" (2024)',
      location: 'Marseille, France',
      instruments: ['Guitare', 'Basse'],
      experience: 'Intermédiaire'
    },
    {
      id: '4',
      name: 'Diana Ross',
      username: '@diana_vocals',
      avatar: '/avatars/diana.jpg',
      status: 'online',
      lastSeen: 'En ligne',
      mutualFriends: 2,
      relationshipType: 'mentor' as RelationshipType,
      commonInterests: ['Soul', 'R&B', 'Chant'],
      collaborationHistory: 1,
      lastCollaboration: 'Single "Harmony" (2023)',
      location: 'Nice, France',
      instruments: ['Voix', 'Piano'],
      experience: 'Expert'
    },
    {
      id: '5',
      name: 'Erik Johnson',
      username: '@erik_drums',
      avatar: '/avatars/erik.jpg',
      status: 'offline',
      lastSeen: 'Il y a 1 jour',
      mutualFriends: 1,
      relationshipType: 'sound_engineer' as RelationshipType,
      commonInterests: ['Rock', 'Batterie', 'Metal'],
      collaborationHistory: 5,
      lastCollaboration: 'Mixage album "Thunder" (2024)',
      location: 'Toulouse, France',
      instruments: ['Batterie', 'Percussion'],
      experience: 'Avancé'
    }
  ];

  const friendRequests = [
    {
      id: '1',
      name: 'Diana Ross',
      username: '@diana_vocals',
      avatar: '/avatars/diana.jpg',
      mutualFriends: 2,
      requestDate: 'Il y a 1 jour',
      commonInterests: ['Soul', 'R&B', 'Chant']
    },
    {
      id: '2',
      name: 'Erik Johnson',
      username: '@erik_drums',
      avatar: '/avatars/erik.jpg',
      mutualFriends: 1,
      requestDate: 'Il y a 3 jours',
      commonInterests: ['Rock', 'Batterie', 'Metal']
    }
  ];

  const suggestions = [
    {
      id: '1',
      name: 'Fiona Apple',
      username: '@fiona_keys',
      avatar: '/avatars/fiona.jpg',
      mutualFriends: 4,
      reason: 'Amis communs et style musical similaire',
      commonInterests: ['Alternative', 'Piano', 'Indie']
    },
    {
      id: '2',
      name: 'George Harrison',
      username: '@george_strings',
      avatar: '/avatars/george.jpg',
      mutualFriends: 6,
      reason: 'Membre de votre communauté Rock',
      commonInterests: ['Rock', 'Guitare', 'Sitar']
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      case 'offline': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  // Fonctions utilitaires
  const getRelationshipOption = (type: RelationshipType) => {
    return relationshipOptions.find(option => option.value === type) || relationshipOptions[1];
  };

  const getRelationshipBadge = (type: RelationshipType) => {
    const option = getRelationshipOption(type);
    return { label: option.label, color: option.color, icon: option.icon };
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'personal': return 'Relations Personnelles';
      case 'professional': return 'Relations Professionnelles';
      case 'artistic': return 'Relations Artistiques';
      case 'community': return 'Relations Communautaires';
      case 'specialized': return 'Relations Spécialisées';
      default: return 'Autres';
    }
  };

  const handleRelationshipChange = (friendId: string, newType: RelationshipType) => {
    // Logique pour changer le type de relation
    console.log(`Changement de relation pour ${friendId} vers ${newType}`);
    // TODO: Appel API pour mettre à jour la relation
  };

  const filteredFriends = friends.filter(friend => {
    const matchesSearch = friend.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         friend.username.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterByRelationship === 'all' || friend.relationshipType === filterByRelationship;
    return matchesSearch && matchesFilter;
  });

  // Composant pour le menu de changement de relation
  const RelationshipDropdown = ({ friend, onRelationshipChange }: {
    friend: any,
    onRelationshipChange: (friendId: string, newType: RelationshipType) => void
  }) => {
    const currentRelationship = getRelationshipOption(friend.relationshipType);
    const groupedOptions = relationshipOptions.reduce((acc, option) => {
      if (!acc[option.category]) acc[option.category] = [];
      acc[option.category].push(option);
      return acc;
    }, {} as Record<string, RelationshipOption[]>);

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700">
            <div className="flex items-center gap-2">
              {currentRelationship.icon}
              <span>{currentRelationship.label}</span>
              <ChevronDown className="h-3 w-3" />
            </div>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64 bg-slate-800 border-slate-700">
          <DropdownMenuLabel className="text-slate-300">Changer la relation</DropdownMenuLabel>
          <DropdownMenuSeparator className="bg-slate-700" />

          {Object.entries(groupedOptions).map(([category, options]) => (
            <div key={category}>
              <DropdownMenuLabel className="text-xs text-slate-400 uppercase tracking-wider px-2 py-1">
                {getCategoryLabel(category)}
              </DropdownMenuLabel>
              {options.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  onClick={() => onRelationshipChange(friend.id, option.value)}
                  className="text-slate-300 hover:bg-slate-700 cursor-pointer"
                >
                  <div className="flex items-center gap-2">
                    {option.icon}
                    <span>{option.label}</span>
                  </div>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator className="bg-slate-700" />
            </div>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            👥 Amis & Collaborateurs
          </h1>
          <p className="text-slate-400">
            Gérez votre réseau musical et découvrez de nouveaux talents
          </p>
        </div>

        {/* Barre de recherche et filtres */}
        <Card className="bg-slate-800/50 border-slate-700 mb-6">
          <CardContent className="p-4">
            <div className="flex gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                <Input
                  placeholder="Rechercher des amis, collaborateurs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-12 bg-slate-700 border-slate-600 text-white text-lg h-12"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="border-slate-600 text-slate-300 h-12 px-4">
                    <Filter className="h-4 w-4 mr-2" />
                    {filterByRelationship === 'all' ? 'Tous les types' : getRelationshipOption(filterByRelationship as RelationshipType).label}
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56 bg-slate-800 border-slate-700">
                  <DropdownMenuLabel className="text-slate-300">Filtrer par relation</DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-slate-700" />
                  <DropdownMenuItem
                    onClick={() => setFilterByRelationship('all')}
                    className="text-slate-300 hover:bg-slate-700 cursor-pointer"
                  >
                    <Users className="h-4 w-4 mr-2" />
                    Tous les types
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="bg-slate-700" />
                  {relationshipOptions.map((option) => (
                    <DropdownMenuItem
                      key={option.value}
                      onClick={() => setFilterByRelationship(option.value)}
                      className="text-slate-300 hover:bg-slate-700 cursor-pointer"
                    >
                      <div className="flex items-center gap-2">
                        {option.icon}
                        <span>{option.label}</span>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>

        {/* Onglets */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="bg-slate-800 border-slate-700">
            <TabsTrigger value="friends" className="data-[state=active]:bg-slate-700">
              <Users className="h-4 w-4 mr-2" />
              Mes Amis ({friends.length})
            </TabsTrigger>
            <TabsTrigger value="requests" className="data-[state=active]:bg-slate-700">
              <Clock className="h-4 w-4 mr-2" />
              Demandes ({friendRequests.length})
            </TabsTrigger>
            <TabsTrigger value="suggestions" className="data-[state=active]:bg-slate-700">
              <UserPlus className="h-4 w-4 mr-2" />
              Suggestions
            </TabsTrigger>
          </TabsList>

          {/* Onglet Mes Amis */}
          <TabsContent value="friends">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredFriends.map((friend) => {
                const relationshipBadge = getRelationshipBadge(friend.relationshipType);
                return (
                  <Card key={friend.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="relative">
                            <Avatar className="h-16 w-16">
                              <AvatarImage src={friend.avatar} />
                              <AvatarFallback className="bg-slate-600 text-white text-lg">
                                {friend.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div className={`absolute -bottom-1 -right-1 h-5 w-5 ${getStatusColor(friend.status)} rounded-full border-2 border-slate-800`}></div>
                          </div>
                          <div>
                            <h3 className="font-semibold text-white text-lg">{friend.name}</h3>
                            <p className="text-slate-400">{friend.username}</p>
                            <p className="text-sm text-slate-500">{friend.lastSeen}</p>
                          </div>
                        </div>
                        <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="space-y-3">
                        {/* Dropdown pour changer la relation */}
                        <RelationshipDropdown
                          friend={friend}
                          onRelationshipChange={handleRelationshipChange}
                        />

                        {/* Informations enrichies */}
                        <div className="bg-slate-700/30 rounded-lg p-3 space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-slate-400">Localisation:</span>
                            <span className="text-slate-300">{friend.location}</span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-slate-400">Expérience:</span>
                            <span className="text-slate-300">{friend.experience}</span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-slate-400">Collaborations:</span>
                            <span className="text-slate-300">{friend.collaborationHistory}</span>
                          </div>
                        </div>

                        {/* Instruments */}
                        <div>
                          <p className="text-xs text-slate-400 mb-1">Instruments:</p>
                          <div className="flex flex-wrap gap-1">
                            {friend.instruments.map((instrument, index) => (
                              <Badge key={index} variant="outline" className="border-slate-600 text-slate-300 text-xs">
                                <Guitar className="h-3 w-3 mr-1" />
                                {instrument}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Genres musicaux */}
                        <div>
                          <p className="text-xs text-slate-400 mb-1">Genres:</p>
                          <div className="flex flex-wrap gap-1">
                            {friend.commonInterests.map((interest, index) => (
                              <Badge key={index} variant="outline" className="border-blue-500/30 text-blue-400 text-xs">
                                <Tag className="h-3 w-3 mr-1" />
                                {interest}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Dernière collaboration */}
                        {friend.lastCollaboration && (
                          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-2">
                            <p className="text-xs text-blue-400 font-medium">Dernière collaboration:</p>
                            <p className="text-xs text-blue-300">{friend.lastCollaboration}</p>
                          </div>
                        )}

                        <p className="text-sm text-slate-400">
                          {friend.mutualFriends} amis en commun
                        </p>

                        <div className="flex gap-2 pt-2">
                          <Button size="sm" className="flex-1 bg-blue-500 hover:bg-blue-600">
                            <MessageCircle className="h-4 w-4 mr-2" />
                            Message
                          </Button>
                          <Button size="sm" variant="outline" className="border-slate-600">
                            <Music className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Message si aucun ami trouvé */}
            {filteredFriends.length === 0 && (
              <Card className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-8 text-center">
                  <Users className="h-16 w-16 mx-auto mb-4 text-slate-500" />
                  <h3 className="text-lg font-medium text-slate-300 mb-2">
                    Aucun ami trouvé
                  </h3>
                  <p className="text-slate-400">
                    {searchQuery || filterByRelationship !== 'all'
                      ? 'Essayez de modifier vos critères de recherche.'
                      : 'Commencez à ajouter des amis pour développer votre réseau musical !'}
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Onglet Demandes d'amitié */}
          <TabsContent value="requests">
            <div className="space-y-4">
              {friendRequests.map((request) => (
                <Card key={request.id} className="bg-slate-800/50 border-slate-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <Avatar className="h-16 w-16">
                          <AvatarImage src={request.avatar} />
                          <AvatarFallback className="bg-slate-600 text-white text-lg">
                            {request.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="font-semibold text-white text-lg">{request.name}</h3>
                          <p className="text-slate-400">{request.username}</p>
                          <p className="text-sm text-slate-500">{request.requestDate}</p>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {request.commonInterests.map((interest, index) => (
                              <Badge key={index} variant="outline" className="border-slate-600 text-slate-300 text-xs">
                                {interest}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" className="bg-green-500 hover:bg-green-600">
                          <UserCheck className="h-4 w-4 mr-2" />
                          Accepter
                        </Button>
                        <Button size="sm" variant="outline" className="border-slate-600 hover:bg-red-500/20">
                          <UserX className="h-4 w-4 mr-2" />
                          Refuser
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Onglet Suggestions */}
          <TabsContent value="suggestions">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {suggestions.map((suggestion) => (
                <Card key={suggestion.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4 mb-4">
                      <Avatar className="h-16 w-16">
                        <AvatarImage src={suggestion.avatar} />
                        <AvatarFallback className="bg-slate-600 text-white text-lg">
                          {suggestion.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <h3 className="font-semibold text-white text-lg">{suggestion.name}</h3>
                        <p className="text-slate-400">{suggestion.username}</p>
                        <p className="text-sm text-blue-400 mt-1">{suggestion.reason}</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex flex-wrap gap-1">
                        {suggestion.commonInterests.map((interest, index) => (
                          <Badge key={index} variant="outline" className="border-slate-600 text-slate-300">
                            {interest}
                          </Badge>
                        ))}
                      </div>

                      <p className="text-sm text-slate-400">
                        {suggestion.mutualFriends} amis en commun
                      </p>

                      <div className="flex gap-2 pt-2">
                        <Button size="sm" className="flex-1 bg-blue-500 hover:bg-blue-600">
                          <UserPlus className="h-4 w-4 mr-2" />
                          Ajouter
                        </Button>
                        <Button size="sm" variant="outline" className="border-slate-600">
                          <MessageCircle className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Feature Flag Notice */}
        <div className="mt-8">
          <Card className="bg-amber-500/10 border-amber-500/20">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-amber-400">
                <Settings className="h-5 w-5" />
                <span className="font-medium">Version Bêta</span>
              </div>
              <p className="text-amber-300 mt-1">
                Le système d'amis avancé est en cours de déploiement. Les cercles d'amis et suggestions IA seront bientôt disponibles.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
