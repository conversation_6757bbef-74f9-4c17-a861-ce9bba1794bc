"use client";

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Users, 
  Heart,
  Music,
  MessageCircle,
  UserCheck,
  Crown,
  Star,
  Briefcase,
  Mic,
  Guitar,
  Calendar,
  Clock
} from 'lucide-react';

// Types
type RelationshipType = 
  | 'friend' | 'close_friend' | 'acquaintance' | 'family'
  | 'collaborator' | 'business_partner' | 'client' | 'mentor' | 'mentee'
  | 'band_member' | 'producer' | 'songwriter' | 'vocalist' | 'instrumentalist'
  | 'fan' | 'community_member' | 'event_organizer'
  | 'label_contact' | 'booking_agent';

interface RelationshipOption {
  value: RelationshipType;
  label: string;
  icon: React.ReactNode;
  category: 'personal' | 'professional' | 'artistic' | 'community' | 'specialized';
  color: string;
}

// Configuration des types de relations
const relationshipOptions: RelationshipOption[] = [
  // Relations Personnelles
  { value: 'friend', label: 'Ami', icon: <Heart className="h-4 w-4" />, category: 'personal', color: 'bg-pink-500' },
  { value: 'close_friend', label: 'Ami proche', icon: <Heart className="h-4 w-4" />, category: 'personal', color: 'bg-red-500' },
  { value: 'acquaintance', label: 'Connaissance', icon: <UserCheck className="h-4 w-4" />, category: 'personal', color: 'bg-blue-400' },
  { value: 'family', label: 'Famille', icon: <Users className="h-4 w-4" />, category: 'personal', color: 'bg-purple-500' },
  
  // Relations Professionnelles
  { value: 'collaborator', label: 'Collaborateur', icon: <Briefcase className="h-4 w-4" />, category: 'professional', color: 'bg-green-500' },
  { value: 'business_partner', label: 'Partenaire commercial', icon: <Briefcase className="h-4 w-4" />, category: 'professional', color: 'bg-emerald-600' },
  { value: 'client', label: 'Client', icon: <Star className="h-4 w-4" />, category: 'professional', color: 'bg-yellow-500' },
  { value: 'mentor', label: 'Mentor', icon: <Crown className="h-4 w-4" />, category: 'professional', color: 'bg-orange-500' },
  
  // Relations Artistiques
  { value: 'band_member', label: 'Membre du groupe', icon: <Users className="h-4 w-4" />, category: 'artistic', color: 'bg-indigo-500' },
  { value: 'producer', label: 'Producteur', icon: <Music className="h-4 w-4" />, category: 'artistic', color: 'bg-violet-500' },
  { value: 'songwriter', label: 'Compositeur', icon: <Music className="h-4 w-4" />, category: 'artistic', color: 'bg-teal-500' },
  { value: 'vocalist', label: 'Chanteur', icon: <Mic className="h-4 w-4" />, category: 'artistic', color: 'bg-rose-500' },
  { value: 'instrumentalist', label: 'Instrumentiste', icon: <Guitar className="h-4 w-4" />, category: 'artistic', color: 'bg-amber-500' },
  
  // Relations Communautaires
  { value: 'fan', label: 'Fan', icon: <Star className="h-4 w-4" />, category: 'community', color: 'bg-pink-400' },
  { value: 'community_member', label: 'Membre communauté', icon: <Users className="h-4 w-4" />, category: 'community', color: 'bg-slate-500' },
  { value: 'event_organizer', label: 'Organisateur événements', icon: <Calendar className="h-4 w-4" />, category: 'community', color: 'bg-lime-500' },
  
  // Relations Spécialisées
  { value: 'label_contact', label: 'Contact label', icon: <Briefcase className="h-4 w-4" />, category: 'specialized', color: 'bg-gray-600' },
  { value: 'booking_agent', label: 'Agent de booking', icon: <Clock className="h-4 w-4" />, category: 'specialized', color: 'bg-stone-500' }
];

export default function FriendsPage() {
  const [activeTab, setActiveTab] = useState('friends');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterByRelationship, setFilterByRelationship] = useState<RelationshipType | 'all'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'carousel' | 'list'>('grid');

  // Données de démonstration
  const friends = [
    {
      id: '1',
      name: 'Alice Martin',
      username: '@alice_music',
      avatar: '/avatars/alice.jpg',
      relationshipType: 'collaborator' as RelationshipType,
      status: 'online',
      location: 'Paris, France',
      instruments: ['Piano', 'Synthé'],
      experience: 'Avancé',
      commonInterests: ['Jazz', 'Blues', 'Fusion'],
      collaborationHistory: 3,
      isOnline: true
    },
    {
      id: '2',
      name: 'Bob Wilson',
      username: '@bob_beats',
      avatar: '/avatars/bob.jpg',
      relationshipType: 'producer' as RelationshipType,
      status: 'away',
      location: 'Lyon, France',
      instruments: ['Synthé', 'Drum Machine'],
      experience: 'Expert',
      commonInterests: ['Electronic', 'Techno', 'House'],
      collaborationHistory: 7,
      isOnline: false
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            👥 Amis & Collaborateurs
          </h1>
          <p className="text-slate-400">
            Gérez votre réseau musical et découvrez de nouveaux talents
          </p>
        </div>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-6">
            <p className="text-slate-300 mb-4">
              🚧 Interface amis ultra-avancée en cours de finalisation...
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {friends.map((friend) => (
                <div key={friend.id} className="bg-slate-700/50 rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={friend.avatar} />
                        <AvatarFallback className="bg-slate-600 text-white">
                          {friend.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className={`absolute -bottom-1 -right-1 h-4 w-4 ${friend.isOnline ? 'bg-green-500' : 'bg-gray-500'} rounded-full border-2 border-slate-800`}></div>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-white">{friend.name}</h3>
                      <p className="text-slate-400 text-sm">{friend.username}</p>
                      <Badge variant="outline" className="text-xs mt-1">
                        {relationshipOptions.find(opt => opt.value === friend.relationshipType)?.label}
                      </Badge>
                    </div>
                    <Button size="sm" variant="outline" className="border-slate-600">
                      <MessageCircle className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="mt-3 text-sm text-slate-400">
                    <p>📍 {friend.location}</p>
                    <p>🎵 {friend.commonInterests.join(', ')}</p>
                    <p>🤝 {friend.collaborationHistory} collaborations</p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-6 p-4 bg-amber-500/10 border border-amber-500/20 rounded-lg">
              <p className="text-amber-300 font-medium">🔄 Développement en cours</p>
              <p className="text-amber-300 mt-1">
                L'interface complète avec modes d'affichage, amis en commun, historique collaborations, et toutes les fonctionnalités avancées sera bientôt disponible.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
