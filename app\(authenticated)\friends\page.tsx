"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  <PERSON>,
  UserPlus,
  Search,
  Settings,
  Heart,
  Music,
  MessageCircle,
  MoreHorizontal,
  UserCheck,
  UserX,
  Clock,
  Crown,
  Star,
  Briefcase,
  Mic,
  Guitar,
  Headphones,
  ChevronDown,
  Tag,
  Filter,
  Grid3X3,
  List,
  Eye,
  Edit3,
  Trash2,
  ExternalLink,
  History,
  Plus,
  ChevronRight,
  Disc3,
  Play,
  Calendar
} from 'lucide-react';

// Types
type RelationshipType = 
  | 'friend' | 'close_friend' | 'acquaintance' | 'family'
  | 'collaborator' | 'business_partner' | 'client' | 'mentor' | 'mentee'
  | 'band_member' | 'producer' | 'songwriter' | 'vocalist' | 'instrumentalist'
  | 'fan' | 'community_member' | 'event_organizer'
  | 'label_contact' | 'booking_agent';

interface RelationshipOption {
  value: RelationshipType;
  label: string;
  icon: React.ReactNode;
  category: 'personal' | 'professional' | 'artistic' | 'community' | 'specialized';
  color: string;
}

// Configuration des types de relations
const relationshipOptions: RelationshipOption[] = [
  // Relations Personnelles
  { value: 'friend', label: 'Ami', icon: <Heart className="h-4 w-4" />, category: 'personal', color: 'bg-pink-500' },
  { value: 'close_friend', label: 'Ami proche', icon: <Heart className="h-4 w-4" />, category: 'personal', color: 'bg-red-500' },
  { value: 'acquaintance', label: 'Connaissance', icon: <UserCheck className="h-4 w-4" />, category: 'personal', color: 'bg-blue-400' },
  { value: 'family', label: 'Famille', icon: <Users className="h-4 w-4" />, category: 'personal', color: 'bg-purple-500' },
  
  // Relations Professionnelles
  { value: 'collaborator', label: 'Collaborateur', icon: <Briefcase className="h-4 w-4" />, category: 'professional', color: 'bg-green-500' },
  { value: 'business_partner', label: 'Partenaire commercial', icon: <Briefcase className="h-4 w-4" />, category: 'professional', color: 'bg-emerald-600' },
  { value: 'client', label: 'Client', icon: <Star className="h-4 w-4" />, category: 'professional', color: 'bg-yellow-500' },
  { value: 'mentor', label: 'Mentor', icon: <Crown className="h-4 w-4" />, category: 'professional', color: 'bg-orange-500' },
  
  // Relations Artistiques
  { value: 'band_member', label: 'Membre du groupe', icon: <Users className="h-4 w-4" />, category: 'artistic', color: 'bg-indigo-500' },
  { value: 'producer', label: 'Producteur', icon: <Music className="h-4 w-4" />, category: 'artistic', color: 'bg-violet-500' },
  { value: 'songwriter', label: 'Compositeur', icon: <Music className="h-4 w-4" />, category: 'artistic', color: 'bg-teal-500' },
  { value: 'vocalist', label: 'Chanteur', icon: <Mic className="h-4 w-4" />, category: 'artistic', color: 'bg-rose-500' },
  { value: 'instrumentalist', label: 'Instrumentiste', icon: <Guitar className="h-4 w-4" />, category: 'artistic', color: 'bg-amber-500' },
  
  // Relations Communautaires
  { value: 'fan', label: 'Fan', icon: <Star className="h-4 w-4" />, category: 'community', color: 'bg-pink-400' },
  { value: 'community_member', label: 'Membre communauté', icon: <Users className="h-4 w-4" />, category: 'community', color: 'bg-slate-500' },
  { value: 'event_organizer', label: 'Organisateur événements', icon: <Calendar className="h-4 w-4" />, category: 'community', color: 'bg-lime-500' },
  
  // Relations Spécialisées
  { value: 'label_contact', label: 'Contact label', icon: <Briefcase className="h-4 w-4" />, category: 'specialized', color: 'bg-gray-600' },
  { value: 'booking_agent', label: 'Agent de booking', icon: <Clock className="h-4 w-4" />, category: 'specialized', color: 'bg-stone-500' }
];

export default function FriendsPage() {
  const [activeTab, setActiveTab] = useState('friends');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterByRelationship, setFilterByRelationship] = useState<RelationshipType | 'all'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'carousel' | 'list'>('grid');
  const [selectedFriend, setSelectedFriend] = useState<any | null>(null);
  const [editingFriend, setEditingFriend] = useState<string | null>(null);
  const [showMutualFriends, setShowMutualFriends] = useState<string | null>(null);

  // Données de démonstration enrichies
  const friends = [
    {
      id: '1',
      name: 'Alice Martin',
      username: '@alice_music',
      avatar: '/avatars/alice.jpg',
      relationshipType: 'collaborator' as RelationshipType,
      status: 'online',
      mutualFriends: [
        { id: '1', name: 'Sophie Chen', avatar: '/avatars/sophie.jpg' },
        { id: '2', name: 'Marc Dubois', avatar: '/avatars/marc.jpg' },
        { id: '3', name: 'Luna Collective', avatar: '/avatars/luna.jpg' },
        { id: '4', name: 'Beat Master', avatar: '/avatars/beat.jpg' },
        { id: '5', name: 'Jazz Lover', avatar: '/avatars/jazz.jpg' }
      ],
      collaborationHistory: 3,
      lastCollaboration: {
        title: 'Album "Midnight Jazz"',
        year: 2024,
        type: 'album',
        id: 'album-123',
        url: '/albums/midnight-jazz'
      },
      collaborationDetails: [
        { title: 'Album "Midnight Jazz"', year: 2024, type: 'album', role: 'Co-compositeur' },
        { title: 'Single "Blue Notes"', year: 2023, type: 'song', role: 'Pianiste' },
        { title: 'EP "Jazz Fusion"', year: 2023, type: 'album', role: 'Arrangeur' }
      ],
      location: 'Paris, France',
      instruments: ['Piano', 'Synthé'],
      experience: 'Avancé',
      commonInterests: ['Jazz', 'Blues', 'Fusion'],
      profileUrl: '/profile/alice-martin',
      isOnline: true,
      lastSeen: new Date(Date.now() - 5 * 60 * 1000) // 5 minutes ago
    },
    {
      id: '2',
      name: 'Bob Wilson',
      username: '@bob_beats',
      avatar: '/avatars/bob.jpg',
      relationshipType: 'producer' as RelationshipType,
      status: 'away',
      mutualFriends: [
        { id: '1', name: 'Cyber Waves', avatar: '/avatars/cyber.jpg' },
        { id: '2', name: 'Neon Beats', avatar: '/avatars/neon.jpg' },
        { id: '3', name: 'Tech Master', avatar: '/avatars/tech.jpg' }
      ],
      collaborationHistory: 7,
      lastCollaboration: {
        title: 'EP "Digital Dreams"',
        year: 2024,
        type: 'album',
        id: 'ep-456',
        url: '/albums/digital-dreams'
      },
      collaborationDetails: [
        { title: 'EP "Digital Dreams"', year: 2024, type: 'album', role: 'Producteur' },
        { title: 'Single "Cyber Night"', year: 2024, type: 'song', role: 'Beatmaker' },
        { title: 'Remix "Electric Soul"', year: 2023, type: 'song', role: 'Remixeur' }
      ],
      location: 'Lyon, France',
      instruments: ['Synthé', 'Drum Machine'],
      experience: 'Expert',
      commonInterests: ['Electronic', 'Techno', 'House'],
      profileUrl: '/profile/bob-wilson',
      isOnline: false,
      lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
    }
  ];

  // Fonctions utilitaires
  const getRelationshipOption = (type: RelationshipType) => {
    return relationshipOptions.find(option => option.value === type) || relationshipOptions[1];
  };

  const getRelationshipBadge = (type: RelationshipType) => {
    const option = getRelationshipOption(type);
    return { label: option.label, color: option.color, icon: option.icon };
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'personal': return 'Relations Personnelles';
      case 'professional': return 'Relations Professionnelles';
      case 'artistic': return 'Relations Artistiques';
      case 'community': return 'Relations Communautaires';
      case 'specialized': return 'Relations Spécialisées';
      default: return 'Autres';
    }
  };

  const handleRelationshipChange = (friendId: string, newType: RelationshipType) => {
    console.log(`Changement de relation pour ${friendId} vers ${newType}`);
  };

  const handleRemoveFriend = (friendId: string) => {
    console.log(`Suppression de l'ami ${friendId}`);
  };

  const handleSendMessage = (friendId: string) => {
    console.log(`Envoi message à ${friendId}`);
  };

  const handleEditCollaboration = (friendId: string, collabId: string) => {
    console.log(`Édition collaboration ${collabId} pour ${friendId}`);
    setEditingFriend(friendId);
  };

  const getOnlineStatus = (friend: any) => {
    if (friend.isOnline) return { status: 'online', color: 'bg-green-500', text: 'En ligne' };

    const timeDiff = Date.now() - friend.lastSeen.getTime();
    const minutes = Math.floor(timeDiff / (1000 * 60));
    const hours = Math.floor(timeDiff / (1000 * 60 * 60));
    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));

    if (minutes < 5) return { status: 'away', color: 'bg-yellow-500', text: 'À l\'instant' };
    if (minutes < 30) return { status: 'away', color: 'bg-yellow-500', text: `Il y a ${minutes}min` };
    if (hours < 24) return { status: 'offline', color: 'bg-gray-500', text: `Il y a ${hours}h` };
    return { status: 'offline', color: 'bg-gray-500', text: `Il y a ${days}j` };
  };

  const filteredFriends = friends.filter(friend => {
    const matchesSearch = friend.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         friend.username.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterByRelationship === 'all' || friend.relationshipType === filterByRelationship;
    return matchesSearch && matchesFilter;
  });

  // Composant pour afficher les amis en commun
  const MutualFriendsPopover = ({ friend }: { friend: any }) => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white p-1">
          <div className="flex -space-x-2">
            {friend.mutualFriends.slice(0, 3).map((mutualFriend: any, index: number) => (
              <Avatar key={index} className="h-6 w-6 border-2 border-slate-800">
                <AvatarImage src={mutualFriend.avatar} />
                <AvatarFallback className="bg-slate-600 text-white text-xs">
                  {mutualFriend.name.split(' ').map((n: string) => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
            ))}
          </div>
          <span className="text-xs ml-2">+{friend.mutualFriends.length}</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 bg-slate-800 border-slate-700">
        <div className="space-y-3">
          <h4 className="font-medium text-white">Amis en commun ({friend.mutualFriends.length})</h4>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {friend.mutualFriends.map((mutualFriend: any) => (
              <div key={mutualFriend.id} className="flex items-center gap-3 p-2 hover:bg-slate-700 rounded-lg cursor-pointer">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={mutualFriend.avatar} />
                  <AvatarFallback className="bg-slate-600 text-white text-xs">
                    {mutualFriend.name.split(' ').map((n: string) => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <span className="text-slate-300 text-sm">{mutualFriend.name}</span>
                <Button size="sm" variant="ghost" className="ml-auto p-1">
                  <ExternalLink className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );

  // Composant pour l'historique des collaborations
  const CollaborationHistory = ({ friend }: { friend: any }) => (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="text-blue-400 hover:text-blue-300 p-1">
          <History className="h-4 w-4 mr-1" />
          Historique ({friend.collaborationHistory})
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-slate-800 border-slate-700 max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-white">Historique des collaborations avec {friend.name}</DialogTitle>
          <DialogDescription className="text-slate-400">
            Toutes vos collaborations musicales ensemble
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {friend.collaborationDetails.map((collab: any, index: number) => (
            <div key={index} className="flex items-center gap-4 p-4 bg-slate-700/50 rounded-lg hover:bg-slate-700/70 transition-colors">
              <div className="h-12 w-12 bg-slate-600 rounded-lg flex items-center justify-center">
                {collab.type === 'album' ? <Disc3 className="h-6 w-6 text-slate-300" /> : <Music className="h-6 w-6 text-slate-300" />}
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-white">{collab.title}</h4>
                <p className="text-slate-400 text-sm">{collab.role} • {collab.year}</p>
              </div>
              <div className="flex gap-2">
                <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white">
                  <Play className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white">
                  <Edit3 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
          <Button className="w-full bg-blue-500 hover:bg-blue-600">
            <Plus className="h-4 w-4 mr-2" />
            Ajouter une collaboration
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            👥 Amis & Collaborateurs
          </h1>
          <p className="text-slate-400">
            Gérez votre réseau musical et découvrez de nouveaux talents
          </p>
        </div>

        {/* Barre de recherche et filtres */}
        <Card className="bg-slate-800/50 border-slate-700 mb-6">
          <CardContent className="p-4">
            <div className="flex gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                <Input
                  placeholder="Rechercher des amis, collaborateurs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-12 bg-slate-700 border-slate-600 text-white text-lg h-12"
                />
              </div>

              {/* Sélecteur de mode d'affichage */}
              <div className="flex bg-slate-700 rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className={viewMode === 'grid' ? 'bg-slate-600' : ''}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'carousel' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('carousel')}
                  className={viewMode === 'carousel' ? 'bg-slate-600' : ''}
                >
                  <Users className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className={viewMode === 'list' ? 'bg-slate-600' : ''}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="border-slate-600 text-slate-300 h-12 px-4">
                    <Filter className="h-4 w-4 mr-2" />
                    {filterByRelationship === 'all' ? 'Tous les types' : getRelationshipOption(filterByRelationship as RelationshipType).label}
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56 bg-slate-800 border-slate-700">
                  <DropdownMenuLabel className="text-slate-300">Filtrer par relation</DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-slate-700" />
                  <DropdownMenuItem
                    onClick={() => setFilterByRelationship('all')}
                    className="text-slate-300 hover:bg-slate-700 cursor-pointer"
                  >
                    <Users className="h-4 w-4 mr-2" />
                    Tous les types
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="bg-slate-700" />
                  {relationshipOptions.map((option) => (
                    <DropdownMenuItem
                      key={option.value}
                      onClick={() => setFilterByRelationship(option.value)}
                      className="text-slate-300 hover:bg-slate-700 cursor-pointer"
                    >
                      <div className="flex items-center gap-2">
                        {option.icon}
                        <span>{option.label}</span>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>

        {/* Contenu principal avec modes d'affichage */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="bg-slate-800 border-slate-700">
            <TabsTrigger value="friends" className="data-[state=active]:bg-slate-700">
              <Users className="h-4 w-4 mr-2" />
              Amis ({filteredFriends.length})
            </TabsTrigger>
            <TabsTrigger value="requests" className="data-[state=active]:bg-slate-700">
              <UserPlus className="h-4 w-4 mr-2" />
              Demandes
            </TabsTrigger>
            <TabsTrigger value="suggestions" className="data-[state=active]:bg-slate-700">
              <Star className="h-4 w-4 mr-2" />
              Suggestions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="friends">
            {/* Mode Carrousel */}
            {viewMode === 'carousel' && (
              <div className="mb-6">
                <div className="flex gap-4 overflow-x-auto pb-4 scrollbar-hide">
                  {filteredFriends.map((friend) => {
                    const onlineStatus = getOnlineStatus(friend);
                    return (
                      <div key={friend.id} className="flex-shrink-0 w-32">
                        <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors cursor-pointer">
                          <CardContent className="p-4 text-center">
                            <div className="relative mb-3">
                              <Avatar className="h-16 w-16 mx-auto">
                                <AvatarImage src={friend.avatar} />
                                <AvatarFallback className="bg-slate-600 text-white">
                                  {friend.name.split(' ').map(n => n[0]).join('')}
                                </AvatarFallback>
                              </Avatar>
                              <div className={`absolute -bottom-1 -right-1 h-4 w-4 ${onlineStatus.color} rounded-full border-2 border-slate-800`}></div>
                            </div>
                            <h4 className="font-medium text-white text-sm truncate">{friend.name}</h4>
                            <p className="text-xs text-slate-400 truncate">{onlineStatus.text}</p>
                            <div className="flex gap-1 mt-2">
                              <Button size="sm" variant="ghost" className="p-1 h-6 w-6" onClick={() => handleSendMessage(friend.id)}>
                                <MessageCircle className="h-3 w-3" />
                              </Button>
                              <Button size="sm" variant="ghost" className="p-1 h-6 w-6">
                                <Eye className="h-3 w-3" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Mode Grille (par défaut) */}
            {viewMode === 'grid' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredFriends.map((friend) => {
                  const relationshipBadge = getRelationshipBadge(friend.relationshipType);
                  const onlineStatus = getOnlineStatus(friend);
                  return (
                    <Card key={friend.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <div className="relative">
                              <Avatar className="h-16 w-16">
                                <AvatarImage src={friend.avatar} />
                                <AvatarFallback className="bg-slate-600 text-white text-lg">
                                  {friend.name.split(' ').map(n => n[0]).join('')}
                                </AvatarFallback>
                              </Avatar>
                              <div className={`absolute -bottom-1 -right-1 h-5 w-5 ${onlineStatus.color} rounded-full border-2 border-slate-800`}></div>
                            </div>
                            <div>
                              <Button
                                variant="link"
                                className="p-0 h-auto font-semibold text-white text-lg hover:text-blue-400"
                                onClick={() => window.open(friend.profileUrl, '_blank')}
                              >
                                {friend.name}
                                <ExternalLink className="h-4 w-4 ml-1" />
                              </Button>
                              <p className="text-slate-400">{friend.username}</p>
                              <p className="text-sm text-slate-500">{onlineStatus.text}</p>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="bg-slate-800 border-slate-700">
                              <DropdownMenuItem onClick={() => window.open(friend.profileUrl, '_blank')} className="text-slate-300 hover:bg-slate-700">
                                <Eye className="h-4 w-4 mr-2" />
                                Voir le profil
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleSendMessage(friend.id)} className="text-slate-300 hover:bg-slate-700">
                                <MessageCircle className="h-4 w-4 mr-2" />
                                Envoyer un message
                              </DropdownMenuItem>
                              <DropdownMenuSeparator className="bg-slate-700" />
                              <DropdownMenuItem onClick={() => handleRemoveFriend(friend.id)} className="text-red-400 hover:bg-slate-700">
                                <Trash2 className="h-4 w-4 mr-2" />
                                Supprimer l'ami
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        <div className="space-y-3">
                          {/* Badge de relation */}
                          <Badge className={`${relationshipBadge.color} text-white text-xs`}>
                            {relationshipBadge.icon}
                            <span className="ml-1">{relationshipBadge.label}</span>
                          </Badge>

                          {/* Informations enrichies */}
                          <div className="bg-slate-700/30 rounded-lg p-3 space-y-2">
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-slate-400">Localisation:</span>
                              <span className="text-slate-300">{friend.location}</span>
                            </div>
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-slate-400">Expérience:</span>
                              <span className="text-slate-300">{friend.experience}</span>
                            </div>
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-slate-400">Collaborations:</span>
                              <CollaborationHistory friend={friend} />
                            </div>
                          </div>

                          {/* Instruments */}
                          <div>
                            <p className="text-xs text-slate-400 mb-1">Instruments:</p>
                            <div className="flex flex-wrap gap-1">
                              {friend.instruments.map((instrument, index) => (
                                <Badge key={index} variant="outline" className="border-slate-600 text-slate-300 text-xs">
                                  <Guitar className="h-3 w-3 mr-1" />
                                  {instrument}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          {/* Genres musicaux avec liens */}
                          <div>
                            <p className="text-xs text-slate-400 mb-1">Genres:</p>
                            <div className="flex flex-wrap gap-1">
                              {friend.commonInterests.map((interest, index) => (
                                <Button
                                  key={index}
                                  variant="outline"
                                  size="sm"
                                  className="border-blue-500/30 text-blue-400 text-xs h-6 px-2 hover:bg-blue-500/10"
                                  onClick={() => window.open(`/genres/${interest.toLowerCase()}`, '_blank')}
                                >
                                  <Tag className="h-3 w-3 mr-1" />
                                  {interest}
                                </Button>
                              ))}
                            </div>
                          </div>

                          {/* Dernière collaboration cliquable */}
                          {friend.lastCollaboration && (
                            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-2">
                              <p className="text-xs text-blue-400 font-medium">Dernière collaboration:</p>
                              <Button
                                variant="link"
                                className="p-0 h-auto text-xs text-blue-300 hover:text-blue-200"
                                onClick={() => window.open(friend.lastCollaboration.url, '_blank')}
                              >
                                {friend.lastCollaboration.title} ({friend.lastCollaboration.year})
                                <ExternalLink className="h-3 w-3 ml-1" />
                              </Button>
                            </div>
                          )}

                          {/* Amis en commun */}
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-slate-400">Amis en commun:</span>
                            <MutualFriendsPopover friend={friend} />
                          </div>

                          <div className="flex gap-2 pt-2">
                            <Button
                              size="sm"
                              className="flex-1 bg-blue-500 hover:bg-blue-600"
                              onClick={() => handleSendMessage(friend.id)}
                            >
                              <MessageCircle className="h-4 w-4 mr-2" />
                              Message
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-slate-600"
                              onClick={() => window.open(friend.profileUrl, '_blank')}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}

            {/* Mode Liste */}
            {viewMode === 'list' && (
              <div className="space-y-3">
                {filteredFriends.map((friend) => {
                  const relationshipBadge = getRelationshipBadge(friend.relationshipType);
                  const onlineStatus = getOnlineStatus(friend);
                  return (
                    <Card key={friend.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-4">
                          <div className="relative">
                            <Avatar className="h-12 w-12">
                              <AvatarImage src={friend.avatar} />
                              <AvatarFallback className="bg-slate-600 text-white">
                                {friend.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div className={`absolute -bottom-1 -right-1 h-4 w-4 ${onlineStatus.color} rounded-full border-2 border-slate-800`}></div>
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <Button
                                variant="link"
                                className="p-0 h-auto font-semibold text-white hover:text-blue-400"
                                onClick={() => window.open(friend.profileUrl, '_blank')}
                              >
                                {friend.name}
                                <ExternalLink className="h-3 w-3 ml-1" />
                              </Button>
                              <Badge className={`${relationshipBadge.color} text-white text-xs`}>
                                {relationshipBadge.label}
                              </Badge>
                            </div>
                            <p className="text-slate-400 text-sm">{friend.username} • {friend.location}</p>
                            <div className="flex items-center gap-4 mt-1">
                              <span className="text-xs text-slate-500">{onlineStatus.text}</span>
                              <span className="text-xs text-slate-500">{friend.collaborationHistory} collaborations</span>
                              <MutualFriendsPopover friend={friend} />
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-slate-600"
                              onClick={() => handleSendMessage(friend.id)}
                            >
                              <MessageCircle className="h-4 w-4" />
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent className="bg-slate-800 border-slate-700">
                                <DropdownMenuItem onClick={() => window.open(friend.profileUrl, '_blank')} className="text-slate-300 hover:bg-slate-700">
                                  <Eye className="h-4 w-4 mr-2" />
                                  Voir le profil
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleSendMessage(friend.id)} className="text-slate-300 hover:bg-slate-700">
                                  <MessageCircle className="h-4 w-4 mr-2" />
                                  Envoyer un message
                                </DropdownMenuItem>
                                <DropdownMenuSeparator className="bg-slate-700" />
                                <DropdownMenuItem onClick={() => handleRemoveFriend(friend.id)} className="text-red-400 hover:bg-slate-700">
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Supprimer l'ami
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}

            {/* Message si aucun ami trouvé */}
            {filteredFriends.length === 0 && (
              <Card className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-8 text-center">
                  <Users className="h-16 w-16 mx-auto mb-4 text-slate-500" />
                  <h3 className="text-lg font-medium text-slate-300 mb-2">
                    Aucun ami trouvé
                  </h3>
                  <p className="text-slate-400">
                    {searchQuery || filterByRelationship !== 'all'
                      ? 'Essayez de modifier vos critères de recherche.'
                      : 'Commencez à ajouter des amis pour développer votre réseau musical !'}
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="requests">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-8 text-center">
                <UserPlus className="h-16 w-16 mx-auto mb-4 text-slate-500" />
                <h3 className="text-lg font-medium text-slate-300 mb-2">
                  Aucune demande d'amitié
                </h3>
                <p className="text-slate-400">
                  Les nouvelles demandes d'amitié apparaîtront ici.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="suggestions">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-8 text-center">
                <Star className="h-16 w-16 mx-auto mb-4 text-slate-500" />
                <h3 className="text-lg font-medium text-slate-300 mb-2">
                  Suggestions à venir
                </h3>
                <p className="text-slate-400">
                  L'IA analysera vos goûts musicaux pour vous suggérer de nouveaux amis et collaborateurs.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
