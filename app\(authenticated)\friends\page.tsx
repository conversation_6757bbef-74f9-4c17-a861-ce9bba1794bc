"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  UserPlus, 
  Search, 
  Settings,
  Heart,
  Music,
  MessageCircle,
  MoreHorizontal,
  UserCheck,
  UserX,
  Clock
} from 'lucide-react';

export default function FriendsPage() {
  const [activeTab, setActiveTab] = useState('friends');
  const [searchQuery, setSearchQuery] = useState('');

  // Données de démonstration
  const friends = [
    {
      id: '1',
      name: '<PERSON>',
      username: '@alice_music',
      avatar: '/avatars/alice.jpg',
      status: 'online',
      lastSeen: 'En ligne',
      mutualFriends: 5,
      relationshipType: 'close_friend',
      commonInterests: ['Jazz', 'Piano', 'Composition']
    },
    {
      id: '2',
      name: 'Bob <PERSON>',
      username: '@bob_beats',
      avatar: '/avatars/bob.jpg',
      status: 'offline',
      lastSeen: 'Il y a 2h',
      mutualFriends: 3,
      relationshipType: 'collaborator',
      commonInterests: ['Electronic', 'Production', 'Synthé']
    },
    {
      id: '3',
      name: 'Charlie Dubois',
      username: '@charlie_guitar',
      avatar: '/avatars/charlie.jpg',
      status: 'away',
      lastSeen: 'Il y a 30min',
      mutualFriends: 8,
      relationshipType: 'friend',
      commonInterests: ['Rock', 'Guitare', 'Songwriting']
    }
  ];

  const friendRequests = [
    {
      id: '1',
      name: 'Diana Ross',
      username: '@diana_vocals',
      avatar: '/avatars/diana.jpg',
      mutualFriends: 2,
      requestDate: 'Il y a 1 jour',
      commonInterests: ['Soul', 'R&B', 'Chant']
    },
    {
      id: '2',
      name: 'Erik Johnson',
      username: '@erik_drums',
      avatar: '/avatars/erik.jpg',
      mutualFriends: 1,
      requestDate: 'Il y a 3 jours',
      commonInterests: ['Rock', 'Batterie', 'Metal']
    }
  ];

  const suggestions = [
    {
      id: '1',
      name: 'Fiona Apple',
      username: '@fiona_keys',
      avatar: '/avatars/fiona.jpg',
      mutualFriends: 4,
      reason: 'Amis communs et style musical similaire',
      commonInterests: ['Alternative', 'Piano', 'Indie']
    },
    {
      id: '2',
      name: 'George Harrison',
      username: '@george_strings',
      avatar: '/avatars/george.jpg',
      mutualFriends: 6,
      reason: 'Membre de votre communauté Rock',
      commonInterests: ['Rock', 'Guitare', 'Sitar']
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      case 'offline': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getRelationshipBadge = (type: string) => {
    switch (type) {
      case 'close_friend': return { label: 'Ami proche', color: 'bg-pink-500' };
      case 'collaborator': return { label: 'Collaborateur', color: 'bg-blue-500' };
      case 'family': return { label: 'Famille', color: 'bg-purple-500' };
      default: return { label: 'Ami', color: 'bg-green-500' };
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            👥 Amis & Collaborateurs
          </h1>
          <p className="text-slate-400">
            Gérez votre réseau musical et découvrez de nouveaux talents
          </p>
        </div>

        {/* Barre de recherche */}
        <Card className="bg-slate-800/50 border-slate-700 mb-6">
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
              <Input 
                placeholder="Rechercher des amis, collaborateurs..." 
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 bg-slate-700 border-slate-600 text-white text-lg h-12"
              />
            </div>
          </CardContent>
        </Card>

        {/* Onglets */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="bg-slate-800 border-slate-700">
            <TabsTrigger value="friends" className="data-[state=active]:bg-slate-700">
              <Users className="h-4 w-4 mr-2" />
              Mes Amis ({friends.length})
            </TabsTrigger>
            <TabsTrigger value="requests" className="data-[state=active]:bg-slate-700">
              <Clock className="h-4 w-4 mr-2" />
              Demandes ({friendRequests.length})
            </TabsTrigger>
            <TabsTrigger value="suggestions" className="data-[state=active]:bg-slate-700">
              <UserPlus className="h-4 w-4 mr-2" />
              Suggestions
            </TabsTrigger>
          </TabsList>

          {/* Onglet Mes Amis */}
          <TabsContent value="friends">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {friends.map((friend) => {
                const relationshipBadge = getRelationshipBadge(friend.relationshipType);
                return (
                  <Card key={friend.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="relative">
                            <Avatar className="h-16 w-16">
                              <AvatarImage src={friend.avatar} />
                              <AvatarFallback className="bg-slate-600 text-white text-lg">
                                {friend.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div className={`absolute -bottom-1 -right-1 h-5 w-5 ${getStatusColor(friend.status)} rounded-full border-2 border-slate-800`}></div>
                          </div>
                          <div>
                            <h3 className="font-semibold text-white text-lg">{friend.name}</h3>
                            <p className="text-slate-400">{friend.username}</p>
                            <p className="text-sm text-slate-500">{friend.lastSeen}</p>
                          </div>
                        </div>
                        <Button size="sm" variant="ghost" className="text-slate-400 hover:text-white">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="space-y-3">
                        <Badge className={`${relationshipBadge.color} text-white`}>
                          {relationshipBadge.label}
                        </Badge>

                        <div className="flex flex-wrap gap-1">
                          {friend.commonInterests.map((interest, index) => (
                            <Badge key={index} variant="outline" className="border-slate-600 text-slate-300">
                              {interest}
                            </Badge>
                          ))}
                        </div>

                        <p className="text-sm text-slate-400">
                          {friend.mutualFriends} amis en commun
                        </p>

                        <div className="flex gap-2 pt-2">
                          <Button size="sm" className="flex-1 bg-blue-500 hover:bg-blue-600">
                            <MessageCircle className="h-4 w-4 mr-2" />
                            Message
                          </Button>
                          <Button size="sm" variant="outline" className="border-slate-600">
                            <Music className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          {/* Onglet Demandes d'amitié */}
          <TabsContent value="requests">
            <div className="space-y-4">
              {friendRequests.map((request) => (
                <Card key={request.id} className="bg-slate-800/50 border-slate-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <Avatar className="h-16 w-16">
                          <AvatarImage src={request.avatar} />
                          <AvatarFallback className="bg-slate-600 text-white text-lg">
                            {request.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="font-semibold text-white text-lg">{request.name}</h3>
                          <p className="text-slate-400">{request.username}</p>
                          <p className="text-sm text-slate-500">{request.requestDate}</p>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {request.commonInterests.map((interest, index) => (
                              <Badge key={index} variant="outline" className="border-slate-600 text-slate-300 text-xs">
                                {interest}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" className="bg-green-500 hover:bg-green-600">
                          <UserCheck className="h-4 w-4 mr-2" />
                          Accepter
                        </Button>
                        <Button size="sm" variant="outline" className="border-slate-600 hover:bg-red-500/20">
                          <UserX className="h-4 w-4 mr-2" />
                          Refuser
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Onglet Suggestions */}
          <TabsContent value="suggestions">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {suggestions.map((suggestion) => (
                <Card key={suggestion.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4 mb-4">
                      <Avatar className="h-16 w-16">
                        <AvatarImage src={suggestion.avatar} />
                        <AvatarFallback className="bg-slate-600 text-white text-lg">
                          {suggestion.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <h3 className="font-semibold text-white text-lg">{suggestion.name}</h3>
                        <p className="text-slate-400">{suggestion.username}</p>
                        <p className="text-sm text-blue-400 mt-1">{suggestion.reason}</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex flex-wrap gap-1">
                        {suggestion.commonInterests.map((interest, index) => (
                          <Badge key={index} variant="outline" className="border-slate-600 text-slate-300">
                            {interest}
                          </Badge>
                        ))}
                      </div>

                      <p className="text-sm text-slate-400">
                        {suggestion.mutualFriends} amis en commun
                      </p>

                      <div className="flex gap-2 pt-2">
                        <Button size="sm" className="flex-1 bg-blue-500 hover:bg-blue-600">
                          <UserPlus className="h-4 w-4 mr-2" />
                          Ajouter
                        </Button>
                        <Button size="sm" variant="outline" className="border-slate-600">
                          <MessageCircle className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Feature Flag Notice */}
        <div className="mt-8">
          <Card className="bg-amber-500/10 border-amber-500/20">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-amber-400">
                <Settings className="h-5 w-5" />
                <span className="font-medium">Version Bêta</span>
              </div>
              <p className="text-amber-300 mt-1">
                Le système d'amis avancé est en cours de déploiement. Les cercles d'amis et suggestions IA seront bientôt disponibles.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
