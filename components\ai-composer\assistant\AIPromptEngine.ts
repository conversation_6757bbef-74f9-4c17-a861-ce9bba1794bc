/**
 * Mo<PERSON>ur de prompts pour l'assistant IA
 * Génère des prompts contextuels et intelligents
 */

export interface PromptContext {
  lyricsContent?: string;
  currentSection?: string;
  chords?: any[];
  genre?: string;
  mood?: string;
  tempo?: number;
  key?: string;
  timeSignature?: string;
}

export interface PromptTemplate {
  id: string;
  name: string;
  category: 'lyrics' | 'chords' | 'structure' | 'analysis';
  template: string;
  variables: string[];
}

export class AIPromptEngine {
  private static templates: PromptTemplate[] = [
    {
      id: 'continue-lyrics',
      name: 'Continuer les paroles',
      category: 'lyrics',
      template: `Continue les paroles suivantes dans le style {{genre}} avec une humeur {{mood}} :

Paroles actuelles :
{{lyricsContent}}

Section actuelle : {{currentSection}}
Tonalité : {{key}}
Tempo : {{tempo}} BPM

Instructions :
- Garde la cohérence thématique
- Respecte le style musical
- Assure-toi que les paroles s'adaptent au rythme`,
      variables: ['lyricsContent', 'currentSection', 'genre', 'mood', 'key', 'tempo']
    },
    {
      id: 'suggest-chords',
      name: 'Suggérer des accords',
      category: 'chords',
      template: `Suggère une progression d'accords pour ces paroles dans le style {{genre}} :

Paroles :
{{lyricsContent}}

Contexte musical :
- Tonalité : {{key}}
- Tempo : {{tempo}} BPM
- Signature rythmique : {{timeSignature}}
- Humeur : {{mood}}

Format de réponse : [Accord] à la position X, [Accord] à la position Y
Exemple : [Am] à 0, [F] à 15, [C] à 30, [G] à 45`,
      variables: ['lyricsContent', 'genre', 'key', 'tempo', 'timeSignature', 'mood']
    },
    {
      id: 'analyze-lyrics',
      name: 'Analyser les paroles',
      category: 'analysis',
      template: `Analyse ces paroles de chanson {{genre}} :

{{lyricsContent}}

Fournis une analyse détaillée incluant :
1. Structure narrative et thématique
2. Techniques poétiques utilisées (rimes, métaphores, etc.)
3. Émotions véhiculées
4. Cohérence et flow
5. Suggestions d'amélioration
6. Adaptation au genre musical`,
      variables: ['lyricsContent', 'genre']
    },
    {
      id: 'improve-lyrics',
      name: 'Améliorer les paroles',
      category: 'lyrics',
      template: `Améliore ces paroles en gardant le sens et le style {{genre}} :

Paroles originales :
{{lyricsContent}}

Objectifs d'amélioration :
- Améliorer le flow et la métrique
- Renforcer les rimes
- Clarifier le message
- Adapter au style {{genre}}
- Maintenir l'émotion {{mood}}`,
      variables: ['lyricsContent', 'genre', 'mood']
    }
  ];

  /**
   * Génère un prompt contextuel basé sur un template
   */
  static generatePrompt(templateId: string, context: PromptContext): string {
    const template = this.templates.find(t => t.id === templateId);
    if (!template) {
      throw new Error(`Template ${templateId} not found`);
    }

    let prompt = template.template;

    // Remplace les variables dans le template
    template.variables.forEach(variable => {
      const value = context[variable as keyof PromptContext] || 'Non spécifié';
      const regex = new RegExp(`{{${variable}}}`, 'g');
      prompt = prompt.replace(regex, String(value));
    });

    return prompt;
  }

  /**
   * Génère un prompt personnalisé avec contexte
   */
  static generateCustomPrompt(userPrompt: string, context: PromptContext): string {
    const contextInfo = this.buildContextString(context);
    
    return `${userPrompt}

Contexte musical :
${contextInfo}`;
  }

  /**
   * Construit une chaîne de contexte musical
   */
  private static buildContextString(context: PromptContext): string {
    const parts: string[] = [];

    if (context.genre) parts.push(`Genre: ${context.genre}`);
    if (context.mood) parts.push(`Humeur: ${context.mood}`);
    if (context.key) parts.push(`Tonalité: ${context.key}`);
    if (context.tempo) parts.push(`Tempo: ${context.tempo} BPM`);
    if (context.timeSignature) parts.push(`Signature: ${context.timeSignature}`);
    if (context.currentSection) parts.push(`Section actuelle: ${context.currentSection}`);

    if (context.lyricsContent && context.lyricsContent.trim()) {
      parts.push(`\nParoles actuelles:\n${context.lyricsContent.trim()}`);
    }

    if (context.chords && context.chords.length > 0) {
      const chordNames = context.chords.map(c => c.name || c.chord).join(', ');
      parts.push(`Accords utilisés: ${chordNames}`);
    }

    return parts.join('\n');
  }

  /**
   * Obtient tous les templates disponibles
   */
  static getTemplates(): PromptTemplate[] {
    return [...this.templates];
  }

  /**
   * Obtient les templates par catégorie
   */
  static getTemplatesByCategory(category: PromptTemplate['category']): PromptTemplate[] {
    return this.templates.filter(t => t.category === category);
  }

  /**
   * Ajoute un template personnalisé
   */
  static addCustomTemplate(template: PromptTemplate): void {
    this.templates.push(template);
  }

  /**
   * Valide qu'un contexte contient les variables requises pour un template
   */
  static validateContext(templateId: string, context: PromptContext): boolean {
    const template = this.templates.find(t => t.id === templateId);
    if (!template) return false;

    const requiredVars = template.variables.filter(v => v !== 'mood' && v !== 'tempo'); // Variables optionnelles
    return requiredVars.every(variable => 
      context[variable as keyof PromptContext] !== undefined &&
      context[variable as keyof PromptContext] !== ''
    );
  }
}
