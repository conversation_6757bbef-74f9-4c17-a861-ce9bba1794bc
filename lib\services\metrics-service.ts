/**
 * 📊 SERVICE DE MÉTRIQUES UNIFIÉ - MOUVIK
 * 
 * Service complet pour le tracking et l'analyse de toutes les métriques
 * Version: 1.0 - Juillet 2024
 */

import { createBrowserClient } from '@/lib/supabase/client';

// Types pour les métriques
export type ResourceType = 'song' | 'album' | 'playlist' | 'profile' | 'band' | 'community' | 'comment' | 'message';

export type MetricType = 
  | 'play' | 'view' | 'like' | 'share' | 'download' | 'comment' | 'follow' | 'unfollow'
  | 'add_to_playlist' | 'remove_from_playlist' | 'collaboration_request' | 'message_sent';

export type DeviceType = 'mobile' | 'desktop' | 'tablet' | 'smart_speaker';

export interface MetricEvent {
  resourceType: ResourceType;
  resourceId: string;
  metricType: MetricType;
  userId?: string;
  sessionId?: string;
  duration?: number; // Pour les plays (en secondes)
  quality?: 'low' | 'medium' | 'high'; // Pour les plays
  completionRate?: number; // Pourcentage 0-100
  deviceType?: DeviceType;
  platform?: string;
  metadata?: Record<string, any>;
}

export interface ResourceStats {
  resourceType: ResourceType;
  resourceId: string;
  totalPlays: number;
  totalViews: number;
  totalLikes: number;
  totalShares: number;
  totalDownloads: number;
  totalComments: number;
  uniqueUsers: number;
  avgPlayDuration: number;
  avgCompletionRate: number;
  lastActivity: string;
  trendingScore?: number;
}

export interface GenreStats {
  genreName: string;
  displayName: string;
  songCount: number;
  artistCount: number;
  totalPlays: number;
  totalLikes: number;
  avgCompletionRate: number;
  weeklyTrendScore: number;
  popularityScore: number;
  characteristics: string[];
  typicalInstruments: string[];
  relatedGenres: string[];
}

export interface UserEngagementStats {
  userId: string;
  totalPlays: number;
  totalLikes: number;
  totalShares: number;
  totalComments: number;
  avgSessionDuration: number;
  favoriteGenres: string[];
  topArtists: string[];
  engagementScore: number;
}

export class MetricsService {
  private supabase = createBrowserClient();
  private sessionId: string;

  constructor() {
    // Générer un ID de session unique
    this.sessionId = this.generateSessionId();
  }

  /**
   * Enregistrer un événement métrique
   */
  async trackEvent(event: MetricEvent): Promise<void> {
    try {
      const deviceInfo = this.getDeviceInfo();
      const locationInfo = await this.getLocationInfo();

      const metricData = {
        resource_type: event.resourceType,
        resource_id: event.resourceId,
        metric_type: event.metricType,
        user_id: event.userId,
        session_id: event.sessionId || this.sessionId,
        duration: event.duration,
        quality: event.quality,
        completion_rate: event.completionRate,
        device_type: event.deviceType || deviceInfo.deviceType,
        platform: event.platform || deviceInfo.platform,
        user_agent: navigator.userAgent,
        referrer: document.referrer,
        location_country: locationInfo.country,
        location_city: locationInfo.city,
        metadata: event.metadata || {},
        ip_address: locationInfo.ip
      };

      const { error } = await this.supabase
        .from('unified_metrics')
        .insert(metricData);

      if (error) {
        console.error('Erreur lors de l\'enregistrement de la métrique:', error);
        // En production, envoyer à un service de logging
      }
    } catch (error) {
      console.error('Erreur dans trackEvent:', error);
    }
  }

  /**
   * Obtenir les statistiques d'une ressource
   */
  async getResourceStats(resourceType: ResourceType, resourceId: string): Promise<ResourceStats | null> {
    try {
      const { data, error } = await this.supabase
        .from('resource_stats_realtime')
        .select('*')
        .eq('resource_type', resourceType)
        .eq('resource_id', resourceId)
        .single();

      if (error) {
        console.error('Erreur lors de la récupération des stats:', error);
        return null;
      }

      return {
        resourceType: data.resource_type,
        resourceId: data.resource_id,
        totalPlays: data.total_plays || 0,
        totalViews: data.total_views || 0,
        totalLikes: data.total_likes || 0,
        totalShares: data.total_shares || 0,
        totalDownloads: data.total_downloads || 0,
        totalComments: data.total_comments || 0,
        uniqueUsers: data.unique_users || 0,
        avgPlayDuration: data.avg_play_duration || 0,
        avgCompletionRate: data.avg_completion_rate || 0,
        lastActivity: data.last_activity
      };
    } catch (error) {
      console.error('Erreur dans getResourceStats:', error);
      return null;
    }
  }

  /**
   * Obtenir les statistiques par genre
   */
  async getGenreStats(genreName?: string): Promise<GenreStats[]> {
    try {
      let query = this.supabase
        .from('genre_trends')
        .select('*')
        .order('weekly_trend_score', { ascending: false });

      if (genreName) {
        query = query.eq('genre_name', genreName);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Erreur lors de la récupération des stats de genre:', error);
        return [];
      }

      return data.map(item => ({
        genreName: item.genre_name,
        displayName: item.display_name,
        songCount: item.song_count || 0,
        artistCount: item.artist_count || 0,
        totalPlays: item.total_plays || 0,
        totalLikes: item.total_likes || 0,
        avgCompletionRate: item.avg_completion_rate || 0,
        weeklyTrendScore: item.weekly_trend_score || 0,
        popularityScore: 0, // À récupérer depuis genre_metadata
        characteristics: [],
        typicalInstruments: [],
        relatedGenres: []
      }));
    } catch (error) {
      console.error('Erreur dans getGenreStats:', error);
      return [];
    }
  }

  /**
   * Obtenir les métriques temps réel
   */
  async getRealTimeMetrics(resourceType: ResourceType, resourceId: string): Promise<any> {
    try {
      // Métriques des dernières 24h
      const { data, error } = await this.supabase
        .from('unified_metrics')
        .select('metric_type, created_at, duration, completion_rate')
        .eq('resource_type', resourceType)
        .eq('resource_id', resourceId)
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Erreur lors de la récupération des métriques temps réel:', error);
        return null;
      }

      // Traitement des données pour créer des métriques temps réel
      const hourlyStats = this.processHourlyStats(data);
      const currentTrend = this.calculateCurrentTrend(data);

      return {
        hourlyStats,
        currentTrend,
        totalEvents: data.length,
        lastUpdate: new Date().toISOString()
      };
    } catch (error) {
      console.error('Erreur dans getRealTimeMetrics:', error);
      return null;
    }
  }

  /**
   * Calculer le score de tendance
   */
  async calculateTrendingScore(resourceType: ResourceType, resourceId: string, daysBack: number = 7): Promise<number> {
    try {
      const { data, error } = await this.supabase
        .rpc('calculate_trending_score', {
          resource_type_param: resourceType,
          resource_id_param: resourceId,
          days_back: daysBack
        });

      if (error) {
        console.error('Erreur lors du calcul du score de tendance:', error);
        return 0;
      }

      return data || 0;
    } catch (error) {
      console.error('Erreur dans calculateTrendingScore:', error);
      return 0;
    }
  }

  /**
   * Obtenir les statistiques d'engagement utilisateur
   */
  async getUserEngagementStats(userId: string): Promise<UserEngagementStats | null> {
    try {
      const { data, error } = await this.supabase
        .from('unified_metrics')
        .select('metric_type, duration, resource_type, created_at')
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

      if (error) {
        console.error('Erreur lors de la récupération des stats d\'engagement:', error);
        return null;
      }

      return this.processUserEngagementData(data, userId);
    } catch (error) {
      console.error('Erreur dans getUserEngagementStats:', error);
      return null;
    }
  }

  /**
   * Rafraîchir les vues matérialisées
   */
  async refreshMetricsViews(): Promise<void> {
    try {
      const { error } = await this.supabase.rpc('refresh_metrics_views');
      
      if (error) {
        console.error('Erreur lors du rafraîchissement des vues:', error);
      }
    } catch (error) {
      console.error('Erreur dans refreshMetricsViews:', error);
    }
  }

  // Méthodes utilitaires privées

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getDeviceInfo(): { deviceType: DeviceType; platform: string } {
    const userAgent = navigator.userAgent.toLowerCase();
    
    let deviceType: DeviceType = 'desktop';
    if (/mobile|android|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
      deviceType = 'mobile';
    } else if (/tablet|ipad/i.test(userAgent)) {
      deviceType = 'tablet';
    }

    let platform = 'web';
    if (userAgent.includes('android')) platform = 'android';
    else if (userAgent.includes('iphone') || userAgent.includes('ipad')) platform = 'ios';
    else if (userAgent.includes('windows')) platform = 'windows';
    else if (userAgent.includes('mac')) platform = 'mac';
    else if (userAgent.includes('linux')) platform = 'linux';

    return { deviceType, platform };
  }

  private async getLocationInfo(): Promise<{ country?: string; city?: string; ip?: string }> {
    try {
      // En production, utiliser un service de géolocalisation IP
      // Pour le développement, retourner des valeurs par défaut
      return {
        country: 'FR',
        city: 'Paris',
        ip: '127.0.0.1'
      };
    } catch (error) {
      return {};
    }
  }

  private processHourlyStats(data: any[]): any[] {
    const hourlyMap = new Map();
    
    data.forEach(item => {
      const hour = new Date(item.created_at).getHours();
      if (!hourlyMap.has(hour)) {
        hourlyMap.set(hour, { hour, plays: 0, views: 0, likes: 0 });
      }
      
      const stats = hourlyMap.get(hour);
      if (item.metric_type === 'play') stats.plays++;
      else if (item.metric_type === 'view') stats.views++;
      else if (item.metric_type === 'like') stats.likes++;
    });

    return Array.from(hourlyMap.values()).sort((a, b) => a.hour - b.hour);
  }

  private calculateCurrentTrend(data: any[]): number {
    if (data.length < 2) return 0;

    const now = Date.now();
    const recentData = data.filter(item => 
      now - new Date(item.created_at).getTime() < 2 * 60 * 60 * 1000 // 2 dernières heures
    );
    const previousData = data.filter(item => {
      const time = now - new Date(item.created_at).getTime();
      return time >= 2 * 60 * 60 * 1000 && time < 4 * 60 * 60 * 1000; // 2-4h avant
    });

    if (previousData.length === 0) return recentData.length > 0 ? 100 : 0;
    
    const recentCount = recentData.length;
    const previousCount = previousData.length;
    
    return Math.min(((recentCount / previousCount) - 1) * 100, 100);
  }

  private processUserEngagementData(data: any[], userId: string): UserEngagementStats {
    const totalPlays = data.filter(d => d.metric_type === 'play').length;
    const totalLikes = data.filter(d => d.metric_type === 'like').length;
    const totalShares = data.filter(d => d.metric_type === 'share').length;
    const totalComments = data.filter(d => d.metric_type === 'comment').length;

    const playDurations = data
      .filter(d => d.metric_type === 'play' && d.duration)
      .map(d => d.duration);
    
    const avgSessionDuration = playDurations.length > 0 
      ? playDurations.reduce((a, b) => a + b, 0) / playDurations.length 
      : 0;

    // Calcul simple du score d'engagement
    const engagementScore = Math.min(
      (totalPlays * 1 + totalLikes * 2 + totalShares * 3 + totalComments * 2) / 10,
      100
    );

    return {
      userId,
      totalPlays,
      totalLikes,
      totalShares,
      totalComments,
      avgSessionDuration,
      favoriteGenres: [], // À implémenter
      topArtists: [], // À implémenter
      engagementScore
    };
  }
}

// Instance singleton
export const metricsService = new MetricsService();

// Hooks React pour faciliter l'utilisation
export const useMetrics = () => {
  return {
    trackEvent: (event: MetricEvent) => metricsService.trackEvent(event),
    getResourceStats: (resourceType: ResourceType, resourceId: string) => 
      metricsService.getResourceStats(resourceType, resourceId),
    getRealTimeMetrics: (resourceType: ResourceType, resourceId: string) =>
      metricsService.getRealTimeMetrics(resourceType, resourceId),
    calculateTrendingScore: (resourceType: ResourceType, resourceId: string, daysBack?: number) =>
      metricsService.calculateTrendingScore(resourceType, resourceId, daysBack)
  };
};
