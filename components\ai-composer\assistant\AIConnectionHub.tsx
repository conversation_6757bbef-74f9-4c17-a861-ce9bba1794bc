'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Brain, Settings, Zap, Target, TrendingUp, Sparkles, 
  Cpu, Globe, Key, TestTube, CheckCircle, AlertCircle,
  Play, Pause, RotateCcw, Save, Download, Upload
} from 'lucide-react';

// Import des composants existants
import { useAIComposerConfig } from '@/hooks/useAIComposerConfig';

interface AIProvider {
  id: string;
  name: string;
  type: 'local' | 'cloud';
  status: 'connected' | 'disconnected' | 'error';
  models: string[];
  capabilities: string[];
  icon: React.ComponentType<any>;
}

interface AIInsight {
  id: string;
  type: 'suggestion' | 'analysis' | 'improvement' | 'warning' | 'success';
  title: string;
  description: string;
  confidence: number;
  category: 'harmony' | 'melody' | 'rhythm' | 'structure' | 'lyrics' | 'production';
  actionable?: boolean;
  action?: () => void;
}

interface AIConnectionHubProps {
  onConfigChange?: (config: any) => void;
  onInsightApply?: (insight: AIInsight) => void;
  currentLyrics?: string;
  currentChords?: any[];
  songMetadata?: any;
  className?: string;
}

/**
 * Hub de connexion IA mega pro
 * Intègre configuration, insights, et actions IA dans une interface unifiée
 */
export const AIConnectionHub: React.FC<AIConnectionHubProps> = ({
  onConfigChange,
  onInsightApply,
  currentLyrics = '',
  currentChords = [],
  songMetadata = {},
  className = ''
}) => {
  // Configuration IA avec hook existant
  const { config, updateConfig, testConnection, isConfigured } = useAIComposerConfig();
  
  // États pour les providers
  const [providers] = useState<AIProvider[]>([
    {
      id: 'ollama',
      name: 'Ollama (Local)',
      type: 'local',
      status: 'connected',
      models: ['llama3.2', 'mistral', 'codellama'],
      capabilities: ['text-generation', 'code-completion', 'analysis'],
      icon: Cpu
    },
    {
      id: 'openai',
      name: 'OpenAI',
      type: 'cloud',
      status: 'disconnected',
      models: ['gpt-4', 'gpt-3.5-turbo', 'gpt-4-turbo'],
      capabilities: ['text-generation', 'analysis', 'creative-writing'],
      icon: Globe
    },
    {
      id: 'anthropic',
      name: 'Anthropic Claude',
      type: 'cloud',
      status: 'disconnected',
      models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
      capabilities: ['text-generation', 'analysis', 'reasoning'],
      icon: Brain
    }
  ]);

  // États pour les insights
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // États pour la configuration avancée
  const [advancedSettings, setAdvancedSettings] = useState({
    temperature: 0.7,
    maxTokens: 2000,
    topP: 0.9,
    frequencyPenalty: 0,
    presencePenalty: 0,
    systemPrompt: 'Tu es un assistant musical expert en composition et arrangement.',
    customKeywords: '',
    analysisDepth: 'standard' as 'basic' | 'standard' | 'deep'
  });

  // Génération d'insights automatique
  const generateInsights = useCallback(async () => {
    if (!isConfigured || !currentLyrics) return;

    setIsAnalyzing(true);
    try {
      const response = await fetch('/api/ai/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lyrics: currentLyrics,
          chords: currentChords,
          metadata: songMetadata,
          config: config,
          settings: advancedSettings
        }),
      });

      const data = await response.json();
      setInsights(data.insights || []);
    } catch (error) {
      console.error('Erreur génération insights:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [isConfigured, currentLyrics, currentChords, songMetadata, config, advancedSettings]);

  // Test de connexion avancé
  const handleTestConnection = useCallback(async (providerId: string) => {
    try {
      const result = await testConnection();
      // Mettre à jour le statut du provider
      console.log(`Test connexion ${providerId}:`, result);
    } catch (error) {
      console.error(`Erreur test ${providerId}:`, error);
    }
  }, [testConnection]);

  // Effet pour génération automatique d'insights
  useEffect(() => {
    if (currentLyrics && isConfigured) {
      const timer = setTimeout(generateInsights, 2000);
      return () => clearTimeout(timer);
    }
  }, [currentLyrics, generateInsights, isConfigured]);

  const filteredInsights = insights.filter(insight => 
    selectedCategory === 'all' || insight.category === selectedCategory
  );

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            Hub de Connexion IA Mega Pro
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={isConfigured ? 'default' : 'secondary'}>
              {isConfigured ? 'Configuré' : 'Non configuré'}
            </Badge>
            {isAnalyzing && (
              <Badge variant="outline" className="animate-pulse">
                Analyse en cours...
              </Badge>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="providers" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="providers">Providers</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
            <TabsTrigger value="advanced">Avancé</TabsTrigger>
            <TabsTrigger value="actions">Actions</TabsTrigger>
          </TabsList>
          
          {/* Onglet Providers */}
          <TabsContent value="providers" className="space-y-4">
            <div className="grid gap-4">
              {providers.map((provider) => (
                <Card key={provider.id} className="relative">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <provider.icon className="w-8 h-8" />
                        <div>
                          <h3 className="font-medium">{provider.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {provider.models.length} modèles • {provider.type}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge 
                          variant={provider.status === 'connected' ? 'default' : 'secondary'}
                          className="flex items-center gap-1"
                        >
                          {provider.status === 'connected' ? (
                            <CheckCircle className="w-3 h-3" />
                          ) : (
                            <AlertCircle className="w-3 h-3" />
                          )}
                          {provider.status}
                        </Badge>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleTestConnection(provider.id)}
                        >
                          <TestTube className="w-4 h-4 mr-2" />
                          Tester
                        </Button>
                      </div>
                    </div>
                    
                    {/* Configuration spécifique au provider */}
                    {provider.id === config.provider && (
                      <div className="mt-4 space-y-3 border-t pt-3">
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <Label className="text-xs">Modèle</Label>
                            <Select value={config.model} onValueChange={(value) => updateConfig({ model: value })}>
                              <SelectTrigger className="h-8">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {provider.models.map((model) => (
                                  <SelectItem key={model} value={model}>
                                    {model}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          
                          {provider.type === 'cloud' && (
                            <div>
                              <Label className="text-xs">Clé API</Label>
                              <Input
                                type="password"
                                placeholder="sk-..."
                                value={config.apiKey || ''}
                                onChange={(e) => updateConfig({ apiKey: e.target.value })}
                                className="h-8"
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          {/* Onglet Insights */}
          <TabsContent value="insights" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Label>Catégorie:</Label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes</SelectItem>
                    <SelectItem value="harmony">Harmonie</SelectItem>
                    <SelectItem value="melody">Mélodie</SelectItem>
                    <SelectItem value="rhythm">Rythme</SelectItem>
                    <SelectItem value="structure">Structure</SelectItem>
                    <SelectItem value="lyrics">Paroles</SelectItem>
                    <SelectItem value="production">Production</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <Button
                onClick={generateInsights}
                disabled={!isConfigured || isAnalyzing}
                size="sm"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                {isAnalyzing ? 'Analyse...' : 'Analyser'}
              </Button>
            </div>
            
            <ScrollArea className="h-[400px]">
              <div className="space-y-3">
                {filteredInsights.map((insight) => (
                  <Card key={insight.id} className="relative">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline" className="text-xs">
                              {insight.category}
                            </Badge>
                            <Badge 
                              variant={insight.type === 'warning' ? 'destructive' : 'default'}
                              className="text-xs"
                            >
                              {insight.confidence}% confiance
                            </Badge>
                          </div>
                          <h4 className="font-medium mb-1">{insight.title}</h4>
                          <p className="text-sm text-muted-foreground">
                            {insight.description}
                          </p>
                        </div>
                        
                        {insight.actionable && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onInsightApply?.(insight)}
                          >
                            <Target className="w-4 h-4 mr-2" />
                            Appliquer
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
                
                {filteredInsights.length === 0 && !isAnalyzing && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Brain className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>Aucun insight disponible</p>
                    <p className="text-sm">Ajoutez du contenu et lancez une analyse</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>
          
          {/* Onglet Configuration Avancée */}
          <TabsContent value="advanced" className="space-y-4">
            <div className="grid gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Température: {advancedSettings.temperature}</Label>
                  <Slider
                    value={[advancedSettings.temperature]}
                    onValueChange={([value]) => setAdvancedSettings(prev => ({ ...prev, temperature: value }))}
                    max={2}
                    min={0}
                    step={0.1}
                    className="mt-2"
                  />
                </div>
                
                <div>
                  <Label>Max Tokens: {advancedSettings.maxTokens}</Label>
                  <Slider
                    value={[advancedSettings.maxTokens]}
                    onValueChange={([value]) => setAdvancedSettings(prev => ({ ...prev, maxTokens: value }))}
                    max={4000}
                    min={100}
                    step={100}
                    className="mt-2"
                  />
                </div>
              </div>
              
              <div>
                <Label>Prompt Système</Label>
                <Textarea
                  value={advancedSettings.systemPrompt}
                  onChange={(e) => setAdvancedSettings(prev => ({ ...prev, systemPrompt: e.target.value }))}
                  rows={3}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label>Mots-clés d'analyse (séparés par des virgules)</Label>
                <Input
                  value={advancedSettings.customKeywords}
                  onChange={(e) => setAdvancedSettings(prev => ({ ...prev, customKeywords: e.target.value }))}
                  placeholder="jazz, blues, rock, mélancolique..."
                  className="mt-1"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label>Profondeur d'analyse</Label>
                <Select 
                  value={advancedSettings.analysisDepth} 
                  onValueChange={(value: any) => setAdvancedSettings(prev => ({ ...prev, analysisDepth: value }))}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="basic">Basique</SelectItem>
                    <SelectItem value="standard">Standard</SelectItem>
                    <SelectItem value="deep">Approfondie</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>
          
          {/* Onglet Actions Rapides */}
          <TabsContent value="actions" className="space-y-4">
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <Zap className="w-6 h-6" />
                <span className="text-sm">Suggestions Rapides</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <TrendingUp className="w-6 h-6" />
                <span className="text-sm">Analyse Structure</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <Target className="w-6 h-6" />
                <span className="text-sm">Optimiser Harmonie</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <Sparkles className="w-6 h-6" />
                <span className="text-sm">Idées Créatives</span>
              </Button>
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="flex-1">
                <Save className="w-4 h-4 mr-2" />
                Sauvegarder Config
              </Button>
              <Button variant="outline" size="sm" className="flex-1">
                <Download className="w-4 h-4 mr-2" />
                Exporter
              </Button>
              <Button variant="outline" size="sm" className="flex-1">
                <Upload className="w-4 h-4 mr-2" />
                Importer
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
