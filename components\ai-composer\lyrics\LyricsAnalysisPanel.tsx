'use client';

import React, { useMemo } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
// import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, Target, Heart, Zap, 
  Hash, Clock, Music2, TrendingUp 
} from 'lucide-react';

interface LyricsAnalysisPanelProps {
  lyricsContent?: string;
  className?: string;
}

interface AnalysisMetrics {
  wordCount: number;
  lineCount: number;
  syllableCount: number;
  rhymeScore: number;
  emotionScore: number;
  complexityScore: number;
  readabilityScore: number;
  structureScore: number;
}

/**
 * Panneau d'analyse des paroles
 * Fournit des métriques et analyses en temps réel
 */
export const LyricsAnalysisPanel: React.FC<LyricsAnalysisPanelProps> = ({
  lyricsContent = '',
  className = ''
}) => {
  
  const analysis = useMemo((): AnalysisMetrics => {
    if (!lyricsContent.trim()) {
      return {
        wordCount: 0,
        lineCount: 0,
        syllableCount: 0,
        rhymeScore: 0,
        emotionScore: 0,
        complexityScore: 0,
        readabilityScore: 0,
        structureScore: 0
      };
    }

    const lines = lyricsContent.split('\n').filter(line => line.trim());
    const words = lyricsContent.trim().split(/\s+/).filter(Boolean);
    
    // Comptage de base
    const wordCount = words.length;
    const lineCount = lines.length;
    
    // Estimation des syllabes (approximative)
    const syllableCount = words.reduce((total, word) => {
      return total + estimateSyllables(word);
    }, 0);
    
    // Score de rimes (basique)
    const rhymeScore = calculateRhymeScore(lines);
    
    // Score d'émotion (basé sur des mots-clés)
    const emotionScore = calculateEmotionScore(words);
    
    // Score de complexité (longueur des mots, variété)
    const complexityScore = calculateComplexityScore(words);
    
    // Score de lisibilité
    const readabilityScore = calculateReadabilityScore(words, syllableCount);
    
    // Score de structure
    const structureScore = calculateStructureScore(lines);

    return {
      wordCount,
      lineCount,
      syllableCount,
      rhymeScore,
      emotionScore,
      complexityScore,
      readabilityScore,
      structureScore
    };
  }, [lyricsContent]);

  const metrics = [
    {
      label: 'Mots',
      value: analysis.wordCount,
      icon: Hash,
      color: 'text-blue-600'
    },
    {
      label: 'Lignes',
      value: analysis.lineCount,
      icon: BarChart3,
      color: 'text-green-600'
    },
    {
      label: 'Syllabes',
      value: analysis.syllableCount,
      icon: Clock,
      color: 'text-purple-600'
    }
  ];

  const scores = [
    {
      label: 'Rimes',
      value: analysis.rhymeScore,
      icon: Music2,
      color: 'text-pink-600'
    },
    {
      label: 'Émotion',
      value: analysis.emotionScore,
      icon: Heart,
      color: 'text-red-600'
    },
    {
      label: 'Complexité',
      value: analysis.complexityScore,
      icon: Zap,
      color: 'text-yellow-600'
    },
    {
      label: 'Lisibilité',
      value: analysis.readabilityScore,
      icon: Target,
      color: 'text-indigo-600'
    },
    {
      label: 'Structure',
      value: analysis.structureScore,
      icon: TrendingUp,
      color: 'text-emerald-600'
    }
  ];

  if (!lyricsContent.trim()) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Analyse des Paroles
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <BarChart3 className="w-12 h-12 mx-auto mb-4" />
            <p>Commencez à écrire pour voir l'analyse</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="w-5 h-5" />
          Analyse des Paroles
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Métriques de base */}
        <div className="grid grid-cols-3 gap-4">
          {metrics.map((metric) => (
            <div key={metric.label} className="text-center">
              <metric.icon className={`w-6 h-6 mx-auto mb-2 ${metric.color}`} />
              <div className="text-2xl font-bold">{metric.value}</div>
              <div className="text-xs text-muted-foreground">{metric.label}</div>
            </div>
          ))}
        </div>
        
        {/* Scores d'analyse */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">Scores d'analyse</h4>
          {scores.map((score) => (
            <div key={score.label} className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <score.icon className={`w-4 h-4 ${score.color}`} />
                  <span>{score.label}</span>
                </div>
                <Badge variant="outline">{score.value}%</Badge>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{ width: `${score.value}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Suggestions d'amélioration */}
        {analysis.wordCount > 10 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Suggestions</h4>
            <div className="space-y-1 text-xs text-muted-foreground">
              {analysis.rhymeScore < 50 && (
                <p>• Essayez d'améliorer les rimes pour plus de musicalité</p>
              )}
              {analysis.emotionScore < 40 && (
                <p>• Ajoutez plus d'émotion avec des mots expressifs</p>
              )}
              {analysis.structureScore < 60 && (
                <p>• Travaillez la structure avec des sections distinctes</p>
              )}
              {analysis.complexityScore > 80 && (
                <p>• Simplifiez certaines phrases pour plus de clarté</p>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Fonctions utilitaires d'analyse
function estimateSyllables(word: string): number {
  word = word.toLowerCase();
  if (word.length <= 3) return 1;
  word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '');
  word = word.replace(/^y/, '');
  const matches = word.match(/[aeiouy]{1,2}/g);
  return matches ? matches.length : 1;
}

function calculateRhymeScore(lines: string[]): number {
  if (lines.length < 2) return 0;
  
  let rhymes = 0;
  for (let i = 0; i < lines.length - 1; i++) {
    const word1 = getLastWord(lines[i]);
    const word2 = getLastWord(lines[i + 1]);
    if (word1 && word2 && doWordsRhyme(word1, word2)) {
      rhymes++;
    }
  }
  
  return Math.min(100, (rhymes / (lines.length - 1)) * 100);
}

function calculateEmotionScore(words: string[]): number {
  const emotionalWords = [
    'amour', 'cœur', 'âme', 'rêve', 'espoir', 'joie', 'bonheur',
    'tristesse', 'douleur', 'larmes', 'peur', 'colère', 'passion'
  ];
  
  const emotionalCount = words.filter(word => 
    emotionalWords.some(emo => word.toLowerCase().includes(emo))
  ).length;
  
  return Math.min(100, (emotionalCount / words.length) * 100 * 10);
}

function calculateComplexityScore(words: string[]): number {
  const avgLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
  const uniqueWords = new Set(words.map(w => w.toLowerCase())).size;
  const variety = uniqueWords / words.length;
  
  return Math.min(100, (avgLength * 10) + (variety * 50));
}

function calculateReadabilityScore(words: string[], syllables: number): number {
  if (words.length === 0) return 0;
  const avgSyllablesPerWord = syllables / words.length;
  return Math.max(0, 100 - (avgSyllablesPerWord - 1.5) * 30);
}

function calculateStructureScore(lines: string[]): number {
  const emptyLines = lines.filter(line => !line.trim()).length;
  const hasStructure = emptyLines > 0; // Présence de séparations
  const regularLength = lines.filter(line => line.trim()).length > 4;
  
  let score = 0;
  if (hasStructure) score += 40;
  if (regularLength) score += 40;
  if (lines.length > 8) score += 20;
  
  return Math.min(100, score);
}

function getLastWord(line: string): string {
  const words = line.trim().split(/\s+/);
  return words[words.length - 1]?.replace(/[^\w]/g, '') || '';
}

function doWordsRhyme(word1: string, word2: string): boolean {
  if (word1.length < 2 || word2.length < 2) return false;
  const ending1 = word1.slice(-2).toLowerCase();
  const ending2 = word2.slice(-2).toLowerCase();
  return ending1 === ending2;
}
