'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Song } from '@/components/songs/song-schema';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { updateSongLyrics } from '@/lib/actions/song.actions';
import { ArrowLeft } from 'lucide-react';

// Import de notre nouvelle architecture AI Composer
import { AIComposerWorkspace } from '@/components/ai-composer/core/AIComposerWorkspace';

interface WorkspaceClientProps {
  song: Song;
}

export function WorkspaceClient({ song }: WorkspaceClientProps) {
  const router = useRouter();

  // Données initiales pour l'AI Composer
  const initialData = {
    lyrics: song.lyrics || '',
    chords: (song as any).ai_composer_data?.chords || [],
    sections: (song as any).ai_composer_data?.sections || [],
    genre: song.genre || '',
    mood: song.moods?.[0] || '',
    key: song.key || '',
    tempo: (song as any).tempo || 120
  };

  // Handler pour sauvegarder les données de l'AI Composer
  const handleSave = useCallback(async (data: any) => {
    try {
      // Vérifier que l'ID de la chanson existe
      if (!song.id) {
        throw new Error('ID de chanson manquant');
      }

      // Sauvegarder les paroles
      if (data.lyrics !== song.lyrics) {
        const result = await updateSongLyrics({
          songId: song.id,
          lyrics: data.lyrics
        });

        if (result.error) {
          throw new Error(result.error);
        }
      }

      // TODO: Sauvegarder les données AI Composer (accords, sections, etc.)
      // Ici on pourrait ajouter une fonction pour sauvegarder ai_composer_data

      toast.success('Chanson sauvegardée avec succès !');
    } catch (error: any) {
      console.error('Erreur de sauvegarde:', error);
      toast.error('Erreur lors de la sauvegarde: ' + error.message);
    }
  }, [song.id, song.lyrics]);

  return (
    <div className="h-screen w-full flex flex-col bg-background">
      {/* Header avec bouton retour */}
      <div className="flex items-center gap-4 p-4 border-b">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push('/ai-composer')}
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div className="flex-grow">
          <h1 className="text-xl font-semibold">{song.title}</h1>
          <p className="text-sm text-muted-foreground">
            {song.artist} • AI Composer Workspace
          </p>
        </div>
      </div>

      {/* AI Composer Workspace */}
      <div className="flex-1">
        <AIComposerWorkspace
          songId={song.id}
          initialData={initialData}
          onSave={handleSave}
        />
      </div>
    </div>
  );
}


