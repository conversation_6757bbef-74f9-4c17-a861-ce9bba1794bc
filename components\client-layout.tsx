"use client";

import React, { useState, useEffect, useMemo, useRef } from "react";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import { SidebarProvider } from "@/components/ui/sidebar-logic";
import { AudioProvider } from "@/contexts/audio-context";
import GlobalAudioPlayer from "@/components/audio/global-audio-player";
import { useAudioPlayerStore, PlayerMode } from "@/lib/stores/audioPlayerStore";
import { UserProvider } from "@/contexts/user-context";
import type { UserProfileForSidebar } from "@/components/ui/app-sidebar";
import { AIProvider } from "@/components/providers/AIProviderContext";
import { createBrowserClient } from "@/lib/supabase/client";
import { getUserProfileForSidebar } from "@/lib/supabase/queries/user";
import type { User } from '@supabase/supabase-js';

interface ClientLayoutProps {
  children: React.ReactNode;
  initialUser?: UserProfileForSidebar | null;
}

const PLAYER_HEIGHTS: Record<PlayerMode, number> = {
  mini: 64,
  normal: 96,
  mega: 0, // Plein écran, pas de padding nécessaire
};

export function ClientLayout({ children, initialUser }: ClientLayoutProps) {
  const [currentUser, setCurrentUser] = useState<UserProfileForSidebar | null>(initialUser || null);
  const [isLoading, setIsLoading] = useState<boolean>(!initialUser);
  const [error, setError] = useState<string | null>(null);
  const supabase = useMemo(() => createBrowserClient(), []);

  const { playerMode, currentSong } = useAudioPlayerStore();
  const bottomPadding = currentSong ? PLAYER_HEIGHTS[playerMode] : 0;

  const currentUserRef = useRef(currentUser);
  currentUserRef.current = currentUser;

  useEffect(() => {
    let isMounted = true;

    const updateUserProfileInternal = async (authUser: User | null) => {
      if (!isMounted) return;

      try {
        if (authUser) {
          const newProfile = await getUserProfileForSidebar(supabase, authUser.id);
          if (isMounted) {
            if (JSON.stringify(newProfile) !== JSON.stringify(currentUserRef.current)) {
              setCurrentUser(newProfile || null);
            }
          }
        } else {
          if (isMounted && currentUserRef.current !== null) {
            setCurrentUser(null);
          }
        }
      } catch (err) {
        console.error('ClientLayout: Error updating user profile:', err);
        if (isMounted) {
          setError('Failed to load user profile.');
          setCurrentUser(null);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      if (isMounted) {
        updateUserProfileInternal(session?.user ?? null);
      }
    });

    // Initial check to avoid race conditions
    supabase.auth.getUser().then(({ data: { user } }) => {
      if (isMounted) {
        updateUserProfileInternal(user);
      }
    });

    return () => {
      isMounted = false;
      subscription?.unsubscribe();
    };
  }, [supabase]);

  const userContextValue = useMemo(() => ({
    user: currentUser,
    isLoading,
    error
  }), [currentUser, isLoading, error]);

  return (
    <UserProvider value={userContextValue}>
      <ThemeProvider
        attribute="class"
        defaultTheme="dark"
        enableSystem
        disableTransitionOnChange
      >
        <AudioProvider>
          <SidebarProvider>
            <div className="flex flex-col h-screen">
            <main
              className="flex-1 overflow-y-auto transition-all duration-300 ease-in-out"
              style={{ paddingBottom: `${bottomPadding}px` }}
            >
              <AIProvider>
                {children}
              </AIProvider>
            </main>
            {currentUser && <GlobalAudioPlayer />}
            </div>
          </SidebarProvider>
        </AudioProvider>
        <Toaster />
      </ThemeProvider>
    </UserProvider>
  );
}
