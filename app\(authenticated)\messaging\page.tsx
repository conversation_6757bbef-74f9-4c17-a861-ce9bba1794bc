import { Metadata } from 'next';
import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import { UnifiedMessagingHub } from '@/components/messaging/UnifiedMessagingHub';

export const metadata: Metadata = {
  title: 'Messagerie - MOUVIK',
  description: 'Hub de communication unifié - Chat, amis, communautés et notifications',
};

export default async function MessagingPage() {
  const supabase = createClient();
  
  // Vérifier l'authentification
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error || !user) {
    redirect('/auth/login');
  }

  return (
    <div className="container mx-auto p-6 h-screen">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Hub de Communication</h1>
        <p className="text-muted-foreground">
          G<PERSON>rez vos conversations, amis, communautés et notifications en un seul endroit.
        </p>
      </div>
      
      <UnifiedMessagingHub 
        currentUserId={user.id}
        className="h-[calc(100vh-200px)]"
      />
    </div>
  );
}
