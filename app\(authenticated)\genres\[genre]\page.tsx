"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Music, 
  Users, 
  Disc3, 
  Play, 
  Heart, 
  Share2, 
  Plus,
  TrendingUp,
  Star,
  Headphones,
  Radio,
  Mic,
  Guitar,
  Calendar,
  MapPin,
  ExternalLink,
  Upload,
  PlusCircle
} from 'lucide-react';

interface GenrePageProps {
  params: {
    genre: string;
  };
}

export default function GenrePage({ params }: GenrePageProps) {
  const [activeTab, setActiveTab] = useState('overview');
  
  // Décoder le genre depuis l'URL
  const genreName = decodeURIComponent(params.genre)
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  // Données de démonstration pour le genre
  const genreData = {
    name: genreName,
    description: `Découvrez l'univers du ${genreName}, ses artistes, ses morceaux emblématiques et sa communauté passionnée.`,
    stats: {
      songs: 1247,
      albums: 89,
      artists: 156,
      playlists: 234,
      followers: 12450
    },
    topArtists: [
      { id: '1', name: 'Alice Martin', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face', followers: 2340, profileUrl: '/profile/alice-martin' },
      { id: '2', name: 'Bob Wilson', avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face', followers: 1890, profileUrl: '/profile/bob-wilson' },
      { id: '3', name: 'Luna Collective', avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face', followers: 3456, profileUrl: '/profile/luna-collective' }
    ],
    recentSongs: [
      { id: '1', title: 'Midnight Dreams', artist: 'Alice Martin', cover: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop', plays: 15420, url: '/songs/midnight-dreams' },
      { id: '2', title: 'Electric Soul', artist: 'Bob Wilson', cover: 'https://images.unsplash.com/photo-1571974599782-87624638275c?w=300&h=300&fit=crop', plays: 12340, url: '/songs/electric-soul' },
      { id: '3', title: 'Cosmic Vibes', artist: 'Luna Collective', cover: 'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?w=300&h=300&fit=crop', plays: 18920, url: '/songs/cosmic-vibes' }
    ],
    recentAlbums: [
      { id: '1', title: 'Night Sessions', artist: 'Alice Martin', cover: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop', tracks: 12, url: '/albums/night-sessions' },
      { id: '2', title: 'Digital Dreams', artist: 'Bob Wilson', cover: 'https://images.unsplash.com/photo-1571974599782-87624638275c?w=300&h=300&fit=crop', tracks: 8, url: '/albums/digital-dreams' }
    ],
    topPlaylists: [
      { id: '1', title: `Best of ${genreName}`, creator: 'MOUVIK', cover: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop', tracks: 45, url: '/playlists/best-of-genre' },
      { id: '2', title: `${genreName} Essentials`, creator: 'Community', cover: 'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?w=300&h=300&fit=crop', tracks: 32, url: '/playlists/genre-essentials' }
    ]
  };

  const getGenreColor = (genre: string) => {
    const colors = {
      'jazz': 'from-amber-500 to-orange-600',
      'electronic': 'from-blue-500 to-purple-600',
      'rock': 'from-red-500 to-pink-600',
      'blues': 'from-indigo-500 to-blue-600',
      'fusion': 'from-green-500 to-teal-600',
      'ambient': 'from-purple-500 to-indigo-600',
      'techno': 'from-cyan-500 to-blue-600',
      'house': 'from-pink-500 to-rose-600'
    };
    return colors[genre.toLowerCase() as keyof typeof colors] || 'from-slate-500 to-slate-600';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header avec gradient du genre */}
        <div className={`relative mb-8 rounded-2xl bg-gradient-to-r ${getGenreColor(genreName)} p-8 text-white overflow-hidden`}>
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-5xl font-bold mb-2">{genreName}</h1>
                <p className="text-xl opacity-90 mb-4">{genreData.description}</p>
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <Music className="h-5 w-5" />
                    <span>{genreData.stats.songs} morceaux</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Disc3 className="h-5 w-5" />
                    <span>{genreData.stats.albums} albums</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    <span>{genreData.stats.artists} artistes</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Heart className="h-5 w-5" />
                    <span>{genreData.stats.followers.toLocaleString()} fans</span>
                  </div>
                </div>
              </div>
              <div className="flex gap-3">
                <Button size="lg" className="bg-white/20 hover:bg-white/30 backdrop-blur-sm">
                  <Heart className="h-5 w-5 mr-2" />
                  Suivre
                </Button>
                <Button size="lg" className="bg-white/20 hover:bg-white/30 backdrop-blur-sm">
                  <Share2 className="h-5 w-5 mr-2" />
                  Partager
                </Button>
              </div>
            </div>
          </div>
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
        </div>

        {/* Onglets de contenu */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="bg-slate-800 border-slate-700 grid grid-cols-6 w-full">
            <TabsTrigger value="overview" className="data-[state=active]:bg-slate-700">
              <TrendingUp className="h-4 w-4 mr-2" />
              Vue d'ensemble
            </TabsTrigger>
            <TabsTrigger value="songs" className="data-[state=active]:bg-slate-700">
              <Music className="h-4 w-4 mr-2" />
              Morceaux
            </TabsTrigger>
            <TabsTrigger value="albums" className="data-[state=active]:bg-slate-700">
              <Disc3 className="h-4 w-4 mr-2" />
              Albums
            </TabsTrigger>
            <TabsTrigger value="artists" className="data-[state=active]:bg-slate-700">
              <Mic className="h-4 w-4 mr-2" />
              Artistes
            </TabsTrigger>
            <TabsTrigger value="playlists" className="data-[state=active]:bg-slate-700">
              <Radio className="h-4 w-4 mr-2" />
              Playlists
            </TabsTrigger>
            <TabsTrigger value="community" className="data-[state=active]:bg-slate-700">
              <Users className="h-4 w-4 mr-2" />
              Communauté
            </TabsTrigger>
          </TabsList>

          {/* Vue d'ensemble */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Top Artistes */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Star className="h-5 w-5 text-yellow-400" />
                    Top Artistes
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {genreData.topArtists.map((artist, index) => (
                    <div key={artist.id} className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-colors cursor-pointer">
                      <div className="flex items-center gap-3 flex-1">
                        <span className="text-slate-400 font-bold text-lg w-6">#{index + 1}</span>
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={artist.avatar} />
                          <AvatarFallback className="bg-slate-600 text-white">
                            {artist.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <Button
                            variant="link"
                            className="p-0 h-auto font-medium text-white hover:text-blue-400"
                            onClick={() => window.open(artist.profileUrl, '_blank')}
                          >
                            {artist.name}
                            <ExternalLink className="h-3 w-3 ml-1" />
                          </Button>
                          <p className="text-slate-400 text-sm">{artist.followers.toLocaleString()} abonnés</p>
                        </div>
                      </div>
                    </div>
                  ))}
                  <Button className="w-full bg-blue-500 hover:bg-blue-600">
                    <Users className="h-4 w-4 mr-2" />
                    Voir tous les artistes
                  </Button>
                </CardContent>
              </Card>

              {/* Morceaux récents */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Music className="h-5 w-5 text-green-400" />
                    Morceaux Récents
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {genreData.recentSongs.map((song) => (
                    <div key={song.id} className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-colors">
                      <div className="w-12 h-12 bg-slate-600 rounded-lg flex items-center justify-center">
                        <Music className="h-6 w-6 text-slate-300" />
                      </div>
                      <div className="flex-1">
                        <Button
                          variant="link"
                          className="p-0 h-auto font-medium text-white hover:text-blue-400"
                          onClick={() => window.open(song.url, '_blank')}
                        >
                          {song.title}
                          <ExternalLink className="h-3 w-3 ml-1" />
                        </Button>
                        <p className="text-slate-400 text-sm">{song.artist}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Play className="h-3 w-3 text-slate-500" />
                          <span className="text-slate-500 text-xs">{song.plays.toLocaleString()} écoutes</span>
                        </div>
                      </div>
                    </div>
                  ))}
                  <Button className="w-full bg-green-500 hover:bg-green-600">
                    <Music className="h-4 w-4 mr-2" />
                    Découvrir plus de morceaux
                  </Button>
                </CardContent>
              </Card>

              {/* Albums et Playlists */}
              <div className="space-y-6">
                {/* Albums récents */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Disc3 className="h-5 w-5 text-purple-400" />
                      Albums Récents
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {genreData.recentAlbums.map((album) => (
                      <div key={album.id} className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-colors">
                        <div className="w-12 h-12 bg-slate-600 rounded-lg flex items-center justify-center">
                          <Disc3 className="h-6 w-6 text-slate-300" />
                        </div>
                        <div className="flex-1">
                          <Button
                            variant="link"
                            className="p-0 h-auto font-medium text-white hover:text-blue-400"
                            onClick={() => window.open(album.url, '_blank')}
                          >
                            {album.title}
                            <ExternalLink className="h-3 w-3 ml-1" />
                          </Button>
                          <p className="text-slate-400 text-sm">{album.artist}</p>
                          <p className="text-slate-500 text-xs">{album.tracks} pistes</p>
                        </div>
                      </div>
                    ))}
                    <Button className="w-full bg-purple-500 hover:bg-purple-600">
                      <Disc3 className="h-4 w-4 mr-2" />
                      Explorer les albums
                    </Button>
                  </CardContent>
                </Card>

                {/* Top Playlists */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Radio className="h-5 w-5 text-pink-400" />
                      Playlists Populaires
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {genreData.topPlaylists.map((playlist) => (
                      <div key={playlist.id} className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-colors">
                        <div className="w-12 h-12 bg-slate-600 rounded-lg flex items-center justify-center">
                          <Radio className="h-6 w-6 text-slate-300" />
                        </div>
                        <div className="flex-1">
                          <Button
                            variant="link"
                            className="p-0 h-auto font-medium text-white hover:text-blue-400"
                            onClick={() => window.open(playlist.url, '_blank')}
                          >
                            {playlist.title}
                            <ExternalLink className="h-3 w-3 ml-1" />
                          </Button>
                          <p className="text-slate-400 text-sm">Par {playlist.creator}</p>
                          <p className="text-slate-500 text-xs">{playlist.tracks} morceaux</p>
                        </div>
                      </div>
                    ))}
                    <Button className="w-full bg-pink-500 hover:bg-pink-600">
                      <Radio className="h-4 w-4 mr-2" />
                      Voir toutes les playlists
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Autres onglets avec invitations à contribuer */}
          <TabsContent value="songs">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-12 text-center">
                <Music className="h-24 w-24 mx-auto mb-6 text-slate-500" />
                <h3 className="text-2xl font-bold text-white mb-4">Aucun morceau {genreName} pour le moment</h3>
                <p className="text-slate-400 mb-6 max-w-md mx-auto">
                  Soyez le premier à partager un morceau {genreName} et aidez à construire cette communauté musicale !
                </p>
                <div className="flex gap-4 justify-center">
                  <Button size="lg" className="bg-blue-500 hover:bg-blue-600">
                    <Upload className="h-5 w-5 mr-2" />
                    Uploader un morceau
                  </Button>
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300">
                    <PlusCircle className="h-5 w-5 mr-2" />
                    Créer une collaboration
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="albums">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-12 text-center">
                <Disc3 className="h-24 w-24 mx-auto mb-6 text-slate-500" />
                <h3 className="text-2xl font-bold text-white mb-4">Aucun album {genreName} disponible</h3>
                <p className="text-slate-400 mb-6 max-w-md mx-auto">
                  Créez le premier album {genreName} de MOUVIK et inspirez d'autres artistes !
                </p>
                <div className="flex gap-4 justify-center">
                  <Button size="lg" className="bg-purple-500 hover:bg-purple-600">
                    <Disc3 className="h-5 w-5 mr-2" />
                    Créer un album
                  </Button>
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300">
                    <Users className="h-5 w-5 mr-2" />
                    Rejoindre un projet
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="artists">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-12 text-center">
                <Mic className="h-24 w-24 mx-auto mb-6 text-slate-500" />
                <h3 className="text-2xl font-bold text-white mb-4">Communauté {genreName} en construction</h3>
                <p className="text-slate-400 mb-6 max-w-md mx-auto">
                  Rejoignez la communauté {genreName} et connectez-vous avec d'autres passionnés !
                </p>
                <div className="flex gap-4 justify-center">
                  <Button size="lg" className="bg-green-500 hover:bg-green-600">
                    <Star className="h-5 w-5 mr-2" />
                    Devenir artiste {genreName}
                  </Button>
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300">
                    <Users className="h-5 w-5 mr-2" />
                    Découvrir des artistes
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="playlists">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-12 text-center">
                <Radio className="h-24 w-24 mx-auto mb-6 text-slate-500" />
                <h3 className="text-2xl font-bold text-white mb-4">Aucune playlist {genreName} créée</h3>
                <p className="text-slate-400 mb-6 max-w-md mx-auto">
                  Créez la première playlist {genreName} et partagez vos morceaux favoris avec la communauté !
                </p>
                <div className="flex gap-4 justify-center">
                  <Button size="lg" className="bg-pink-500 hover:bg-pink-600">
                    <Plus className="h-5 w-5 mr-2" />
                    Créer une playlist
                  </Button>
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300">
                    <Heart className="h-5 w-5 mr-2" />
                    Explorer les genres similaires
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="community">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-12 text-center">
                <Users className="h-24 w-24 mx-auto mb-6 text-slate-500" />
                <h3 className="text-2xl font-bold text-white mb-4">Communauté {genreName} à venir</h3>
                <p className="text-slate-400 mb-6 max-w-md mx-auto">
                  Soyez parmi les premiers membres de la communauté {genreName} et aidez à la faire grandir !
                </p>
                <div className="flex gap-4 justify-center">
                  <Button size="lg" className="bg-blue-500 hover:bg-blue-600">
                    <Heart className="h-5 w-5 mr-2" />
                    Rejoindre la communauté
                  </Button>
                  <Button size="lg" variant="outline" className="border-slate-600 text-slate-300">
                    <Share2 className="h-5 w-5 mr-2" />
                    Inviter des amis
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
