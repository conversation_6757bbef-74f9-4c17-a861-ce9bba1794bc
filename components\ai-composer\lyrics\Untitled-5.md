# Guide d'Intégration : <PERSON>Form ↔ AI Composer

Ce document décrit l'interface technique (API de props) entre le composant conteneur `SongForm` et le module `AIComposerWorkspace`.

## 1. Vue d'ensemble

Le `SongForm` agit comme un "smart container" qui gère l'état global de la chanson (via `react-hook-form`) et les interactions avec Supabase. L'`AIComposerWorkspace` est un composant spécialisé qui reçoit les données créatives, offre une interface d'édition riche, et notifie le `SongForm` des changements via des callbacks.

```mermaid
graph TD
    A[SongForm State (react-hook-form)] -->|Props| B(AIComposerWorkspace);
    B -->|Callbacks (onDataChange)| A;
    A -->|onSubmit| C[Supabase];
```

## 2. Interface de Props (`AIComposerWorkspaceProps`)

Le composant `AIComposerWorkspace` attendra les props suivantes de `SongForm`:

```typescript
// types/ai-composer.ts

export interface AIComposerData {
  // Structure riche pour la persistance JSONB
  version: string;
  structure: any[]; // Remplacer par des types concrets : SongSection[]
  lyrics: any[]; // Remplacer par des types concrets : RichLyricsBlock[]
  chords: any[]; // Remplacer par des types concrets : ChordProgression[]
  history: any[]; // Remplacer par des types concrets : AIInteraction[]
}

export interface AIComposerWorkspaceProps {
  // Données initiales fournies par le SongForm
  initialLyrics: string | null;
  initialChords: string | null;
  initialAiData: AIComposerData | null;
  
  // Callback pour remonter les données modifiées
  onDataChange: (data: {
    lyrics: string; // Version texte simple pour compatibilité
    chords: string; // Version texte simple pour compatibilité
    aiData: AIComposerData; // Objet JSONB complet
  }) => void;

  // Informations contextuelles pour l'IA
  songTitle: string;
  songGenre?: string[];
}
```

## 3. Exemple d'Implémentation dans `SongForm.tsx`

```tsx
// Dans un onglet du SongForm.tsx

const { watch, setValue } = useFormContext<SongFormValues>();

const handleComposerDataChange = useCallback((data) => {
  setValue('lyrics', data.lyrics, { shouldDirty: true });
  setValue('chords', data.chords, { shouldDirty: true });
  setValue('ai_composer_data', data.aiData, { shouldDirty: true });
}, [setValue]);

<AIComposerWorkspace
  initialLyrics={watch('lyrics')}
  initialChords={watch('chords')}
  initialAiData={watch('ai_composer_data')}
  onDataChange={handleComposerDataChange}
  songTitle={watch('title')}
  songGenre={watch('genres')}
/>
```