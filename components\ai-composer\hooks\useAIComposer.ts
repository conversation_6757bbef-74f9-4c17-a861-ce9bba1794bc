import { useState, useCallback, useEffect } from 'react';

interface AIComposerState {
  lyrics: string;
  chords: any[];
  sections: any[];
  currentTime: number;
  isPlaying: boolean;
  isDirty: boolean;
}

interface AIComposerConfig {
  provider: string;
  model: string;
  apiKey: string;
  isConfigured: boolean;
}

/**
 * Hook principal pour l'état de l'AI Composer
 * Centralise la logique métier et l'état
 */
export const useAIComposer = (initialData?: Partial<AIComposerState>) => {
  const [state, setState] = useState<AIComposerState>({
    lyrics: initialData?.lyrics || '',
    chords: initialData?.chords || [],
    sections: initialData?.sections || [],
    currentTime: 0,
    isPlaying: false,
    isDirty: false
  });

  const [config, setConfig] = useState<AIComposerConfig>({
    provider: 'openai',
    model: 'gpt-4',
    apiKey: '',
    isConfigured: false
  });

  // Actions
  const updateLyrics = useCallback((lyrics: string) => {
    setState(prev => ({ ...prev, lyrics, isDirty: true }));
  }, []);

  const updateChords = useCallback((chords: any[]) => {
    setState(prev => ({ ...prev, chords, isDirty: true }));
  }, []);

  const updateSections = useCallback((sections: any[]) => {
    setState(prev => ({ ...prev, sections, isDirty: true }));
  }, []);

  const setCurrentTime = useCallback((currentTime: number) => {
    setState(prev => ({ ...prev, currentTime }));
  }, []);

  const togglePlayback = useCallback(() => {
    setState(prev => ({ ...prev, isPlaying: !prev.isPlaying }));
  }, []);

  const markSaved = useCallback(() => {
    setState(prev => ({ ...prev, isDirty: false }));
  }, []);

  const reset = useCallback(() => {
    setState({
      lyrics: '',
      chords: [],
      sections: [],
      currentTime: 0,
      isPlaying: false,
      isDirty: false
    });
  }, []);

  // Auto-save (optionnel)
  useEffect(() => {
    if (state.isDirty) {
      const timer = setTimeout(() => {
        // Auto-save logic here
        console.log('Auto-saving...', state);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [state.isDirty, state]);

  return {
    // État
    ...state,
    config,
    
    // Actions
    updateLyrics,
    updateChords,
    updateSections,
    setCurrentTime,
    togglePlayback,
    markSaved,
    reset,
    setConfig,
    
    // Computed
    totalDuration: state.sections.reduce((sum, section) => sum + (section.duration || 0), 0),
    hasContent: state.lyrics.trim().length > 0 || state.chords.length > 0,
    canSave: state.isDirty && (state.lyrics.trim().length > 0 || state.chords.length > 0)
  };
};
