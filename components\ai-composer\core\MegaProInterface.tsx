'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Music, Keyboard, Command, Search, Settings, Maximize2, Minimize2,
  Save, Undo, Redo, Copy, Paste, Cut, ZoomIn, ZoomOut, RotateCcw,
  Play, Pause, Square, SkipBack, SkipForward, Volume2, Mic,
  Layers, Grid3X3, Eye, EyeOff, Lock, Unlock, Star, Heart,
  FileText, Download, Upload, Share2, HelpCircle, Lightbulb
} from 'lucide-react';

interface MegaProInterfaceProps {
  children: React.ReactNode;
  title?: string;
  onSave?: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
  isPlaying?: boolean;
  onPlayPause?: () => void;
  className?: string;
}

interface KeyboardShortcut {
  key: string;
  description: string;
  action: () => void;
}

/**
 * Interface Mega Pro pour AI Composer
 * Ergonomie professionnelle avec raccourcis clavier, thèmes, et outils avancés
 */
export const MegaProInterface: React.FC<MegaProInterfaceProps> = ({
  children,
  title = 'AI Composer Mega Pro',
  onSave,
  onUndo,
  onRedo,
  canUndo = false,
  canRedo = false,
  isPlaying = false,
  onPlayPause,
  className = ''
}) => {
  // États de l'interface
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [theme, setTheme] = useState<'light' | 'dark' | 'auto'>('auto');
  const [zoom, setZoom] = useState(100);
  const [showShortcuts, setShowShortcuts] = useState(false);
  const [commandPalette, setCommandPalette] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Références
  const interfaceRef = useRef<HTMLDivElement>(null);

  // Raccourcis clavier
  const shortcuts: KeyboardShortcut[] = [
    { key: 'Ctrl+S', description: 'Sauvegarder', action: () => onSave?.() },
    { key: 'Ctrl+Z', description: 'Annuler', action: () => onUndo?.() },
    { key: 'Ctrl+Y', description: 'Refaire', action: () => onRedo?.() },
    { key: 'Space', description: 'Lecture/Pause', action: () => onPlayPause?.() },
    { key: 'Ctrl+K', description: 'Palette de commandes', action: () => setCommandPalette(true) },
    { key: 'F11', description: 'Plein écran', action: () => toggleFullscreen() },
    { key: 'Ctrl+Plus', description: 'Zoom avant', action: () => setZoom(prev => Math.min(200, prev + 25)) },
    { key: 'Ctrl+Minus', description: 'Zoom arrière', action: () => setZoom(prev => Math.max(50, prev - 25)) },
    { key: 'Ctrl+0', description: 'Zoom 100%', action: () => setZoom(100) },
    { key: '?', description: 'Aide raccourcis', action: () => setShowShortcuts(true) },
  ];

  // Gestion du plein écran
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      interfaceRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // Gestion des raccourcis clavier
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Palette de commandes
      if (e.ctrlKey && e.key === 'k') {
        e.preventDefault();
        setCommandPalette(true);
        return;
      }

      // Aide
      if (e.key === '?' && !e.ctrlKey && !e.altKey && !e.shiftKey) {
        e.preventDefault();
        setShowShortcuts(true);
        return;
      }

      // Autres raccourcis
      shortcuts.forEach(shortcut => {
        const keys = shortcut.key.split('+');
        const isCtrl = keys.includes('Ctrl') && e.ctrlKey;
        const isShift = keys.includes('Shift') && e.shiftKey;
        const isAlt = keys.includes('Alt') && e.altKey;
        const mainKey = keys[keys.length - 1];

        if (
          (keys.includes('Ctrl') ? isCtrl : !e.ctrlKey) &&
          (keys.includes('Shift') ? isShift : !e.shiftKey) &&
          (keys.includes('Alt') ? isAlt : !e.altKey) &&
          (e.key === mainKey || e.code === mainKey)
        ) {
          e.preventDefault();
          shortcut.action();
        }
      });
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts]);

  // Gestion du changement de plein écran
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  return (
    <div 
      ref={interfaceRef}
      className={`mega-pro-interface h-screen flex flex-col bg-background ${className}`}
      style={{ zoom: `${zoom}%` }}
    >
      {/* Barre de titre professionnelle */}
      <div className="flex items-center justify-between px-4 py-2 border-b bg-muted/30">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Music className="w-6 h-6 text-primary" />
            <h1 className="text-lg font-semibold">{title}</h1>
          </div>
          
          <div className="flex items-center gap-1">
            <Badge variant="outline" className="text-xs">
              v2.0 Pro
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {zoom}%
            </Badge>
          </div>
        </div>

        {/* Barre d'outils principale */}
        <div className="flex items-center gap-2">
          {/* Contrôles de fichier */}
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm" onClick={onSave} title="Sauvegarder (Ctrl+S)">
              <Save className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onUndo} disabled={!canUndo} title="Annuler (Ctrl+Z)">
              <Undo className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onRedo} disabled={!canRedo} title="Refaire (Ctrl+Y)">
              <Redo className="w-4 h-4" />
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Contrôles de lecture */}
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm" title="Retour">
              <SkipBack className="w-4 h-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onPlayPause}
              title="Lecture/Pause (Space)"
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            <Button variant="ghost" size="sm" title="Arrêt">
              <Square className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" title="Suivant">
              <SkipForward className="w-4 h-4" />
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Contrôles d'affichage */}
          <div className="flex items-center gap-1">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setZoom(prev => Math.max(50, prev - 25))}
              title="Zoom arrière (Ctrl+-)"
            >
              <ZoomOut className="w-4 h-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setZoom(100)}
              title="Zoom 100% (Ctrl+0)"
            >
              {zoom}%
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setZoom(prev => Math.min(200, prev + 25))}
              title="Zoom avant (Ctrl++)"
            >
              <ZoomIn className="w-4 h-4" />
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Outils avancés */}
          <div className="flex items-center gap-1">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setCommandPalette(true)}
              title="Palette de commandes (Ctrl+K)"
            >
              <Command className="w-4 h-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setShowShortcuts(true)}
              title="Aide raccourcis (?)"
            >
              <HelpCircle className="w-4 h-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={toggleFullscreen}
              title="Plein écran (F11)"
            >
              {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Zone de contenu principal */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar gauche (optionnelle) */}
        {!sidebarCollapsed && (
          <div className="w-64 border-r bg-muted/20 flex flex-col">
            <div className="p-3 border-b">
              <div className="flex items-center gap-2">
                <Layers className="w-4 h-4" />
                <span className="text-sm font-medium">Outils</span>
              </div>
            </div>
            
            <ScrollArea className="flex-1 p-3">
              <div className="space-y-2">
                <Button variant="ghost" size="sm" className="w-full justify-start">
                  <FileText className="w-4 h-4 mr-2" />
                  Paroles
                </Button>
                <Button variant="ghost" size="sm" className="w-full justify-start">
                  <Music className="w-4 h-4 mr-2" />
                  Accords
                </Button>
                <Button variant="ghost" size="sm" className="w-full justify-start">
                  <Grid3X3 className="w-4 h-4 mr-2" />
                  Structure
                </Button>
                <Button variant="ghost" size="sm" className="w-full justify-start">
                  <Lightbulb className="w-4 h-4 mr-2" />
                  IA Assistant
                </Button>
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Zone de contenu */}
        <div className="flex-1 flex flex-col">
          {children}
        </div>
      </div>

      {/* Barre de statut */}
      <div className="flex items-center justify-between px-4 py-1 border-t bg-muted/30 text-xs text-muted-foreground">
        <div className="flex items-center gap-4">
          <span>Prêt</span>
          <span>•</span>
          <span>Dernière sauvegarde: il y a 2 min</span>
        </div>
        
        <div className="flex items-center gap-4">
          <span>120 BPM</span>
          <span>•</span>
          <span>Tonalité: C</span>
          <span>•</span>
          <span>4/4</span>
        </div>
      </div>

      {/* Palette de commandes */}
      {commandPalette && (
        <div className="fixed inset-0 bg-black/50 flex items-start justify-center pt-20 z-50">
          <Card className="w-96">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <Command className="w-4 h-4" />
                <span className="font-medium">Palette de Commandes</span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Input
                  placeholder="Tapez une commande..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  autoFocus
                />
                
                <ScrollArea className="h-48">
                  <div className="space-y-1">
                    {shortcuts
                      .filter(s => s.description.toLowerCase().includes(searchTerm.toLowerCase()))
                      .map((shortcut, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 rounded hover:bg-muted cursor-pointer"
                          onClick={() => {
                            shortcut.action();
                            setCommandPalette(false);
                            setSearchTerm('');
                          }}
                        >
                          <span className="text-sm">{shortcut.description}</span>
                          <Badge variant="outline" className="text-xs">
                            {shortcut.key}
                          </Badge>
                        </div>
                      ))}
                  </div>
                </ScrollArea>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Aide raccourcis */}
      {showShortcuts && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-96 max-h-[80vh]">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Keyboard className="w-5 h-5" />
                Raccourcis Clavier
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {shortcuts.map((shortcut, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm">{shortcut.description}</span>
                      <Badge variant="outline" className="text-xs">
                        {shortcut.key}
                      </Badge>
                    </div>
                  ))}
                </div>
              </ScrollArea>
              
              <div className="mt-4 flex justify-end">
                <Button onClick={() => setShowShortcuts(false)}>
                  Fermer
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
