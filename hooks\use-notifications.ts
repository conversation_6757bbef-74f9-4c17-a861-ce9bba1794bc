/**
 * 🔔 HOOK NOTIFICATIONS TEMPS RÉEL - MOUVIK
 * 
 * Hook pour gérer les notifications en temps réel dans la sidebar
 * Version: 1.0 - Juillet 2024
 */

import { useState, useEffect } from 'react';
import { createBrowserClient } from '@/lib/supabase/client';

export interface NotificationCount {
  messages: number;
  friendRequests: number;
  likes: number;
  comments: number;
  collaborations: number;
  total: number;
}

export interface NotificationSettings {
  showInSidebar: boolean;
  enableSound: boolean;
  enablePush: boolean;
  enableEmail: boolean;
  quietHours: {
    enabled: boolean;
    start: string; // "22:00"
    end: string;   // "08:00"
  };
}

export function useNotifications(userId?: string) {
  const [counts, setCounts] = useState<NotificationCount>({
    messages: 0,
    friendRequests: 0,
    likes: 0,
    comments: 0,
    collaborations: 0,
    total: 0
  });

  const [settings, setSettings] = useState<NotificationSettings>({
    showInSidebar: true,
    enableSound: true,
    enablePush: false,
    enableEmail: false,
    quietHours: {
      enabled: false,
      start: "22:00",
      end: "08:00"
    }
  });

  const [isLoading, setIsLoading] = useState(true);
  const supabase = createBrowserClient();

  // Charger les compteurs de notifications
  const loadNotificationCounts = async () => {
    if (!userId) return;

    try {
      setIsLoading(true);

      // Compter les messages non lus
      const { data: unreadMessages, error: messagesError } = await supabase
        .from('messages')
        .select('id')
        .eq('conversation_id', userId) // Simplification pour la démo
        .eq('is_read', false);

      // Compter les demandes d'amitié en attente
      const { data: friendRequests, error: friendsError } = await supabase
        .from('friendships')
        .select('id')
        .eq('addressee_id', userId)
        .eq('status', 'pending');

      // Compter les notifications non lues
      const { data: unreadNotifications, error: notificationsError } = await supabase
        .from('notifications')
        .select('type')
        .eq('user_id', userId)
        .eq('is_read', false);

      if (!messagesError && !friendsError && !notificationsError) {
        const messageCount = unreadMessages?.length || 0;
        const friendRequestCount = friendRequests?.length || 0;
        
        // Compter par type de notification
        const likesCount = unreadNotifications?.filter(n => n.type === 'like').length || 0;
        const commentsCount = unreadNotifications?.filter(n => n.type === 'comment').length || 0;
        const collaborationsCount = unreadNotifications?.filter(n => n.type === 'collaboration_request').length || 0;

        const newCounts = {
          messages: messageCount,
          friendRequests: friendRequestCount,
          likes: likesCount,
          comments: commentsCount,
          collaborations: collaborationsCount,
          total: messageCount + friendRequestCount + likesCount + commentsCount + collaborationsCount
        };

        setCounts(newCounts);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Charger les paramètres de notification
  const loadNotificationSettings = async () => {
    if (!userId) return;

    try {
      // En production, récupérer depuis la base de données
      // Pour la démo, utiliser localStorage
      const savedSettings = localStorage.getItem(`notification_settings_${userId}`);
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Erreur lors du chargement des paramètres:', error);
    }
  };

  // Sauvegarder les paramètres
  const updateSettings = async (newSettings: Partial<NotificationSettings>) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    
    try {
      // Sauvegarder en localStorage pour la démo
      localStorage.setItem(`notification_settings_${userId}`, JSON.stringify(updatedSettings));
      
      // En production, sauvegarder en base de données
      // await supabase.from('user_notification_settings').upsert({
      //   user_id: userId,
      //   settings: updatedSettings
      // });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des paramètres:', error);
    }
  };

  // Marquer les notifications comme lues
  const markAsRead = async (type?: 'messages' | 'friendRequests' | 'likes' | 'comments' | 'collaborations') => {
    if (!userId) return;

    try {
      if (type === 'messages') {
        // Marquer les messages comme lus
        await supabase
          .from('messages')
          .update({ is_read: true })
          .eq('conversation_id', userId);
        
        setCounts(prev => ({ ...prev, messages: 0, total: prev.total - prev.messages }));
      } else if (type === 'friendRequests') {
        // Les demandes d'amitié sont marquées comme lues quand on les accepte/refuse
        setCounts(prev => ({ ...prev, friendRequests: 0, total: prev.total - prev.friendRequests }));
      } else {
        // Marquer toutes les notifications comme lues
        await supabase
          .from('notifications')
          .update({ is_read: true })
          .eq('user_id', userId);
        
        setCounts({
          messages: 0,
          friendRequests: 0,
          likes: 0,
          comments: 0,
          collaborations: 0,
          total: 0
        });
      }
    } catch (error) {
      console.error('Erreur lors du marquage comme lu:', error);
    }
  };

  // Vérifier si on est en heures silencieuses
  const isQuietHours = () => {
    if (!settings.quietHours.enabled) return false;

    const now = new Date();
    const currentTime = now.getHours() * 100 + now.getMinutes();
    const startTime = parseInt(settings.quietHours.start.replace(':', ''));
    const endTime = parseInt(settings.quietHours.end.replace(':', ''));

    if (startTime > endTime) {
      // Période qui traverse minuit (ex: 22:00 à 08:00)
      return currentTime >= startTime || currentTime <= endTime;
    } else {
      // Période normale (ex: 08:00 à 22:00)
      return currentTime >= startTime && currentTime <= endTime;
    }
  };

  // Jouer un son de notification
  const playNotificationSound = () => {
    if (!settings.enableSound || isQuietHours()) return;

    try {
      // Créer un son simple avec Web Audio API
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
      
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.2);
    } catch (error) {
      console.warn('Impossible de jouer le son de notification:', error);
    }
  };

  // Écouter les nouvelles notifications en temps réel
  useEffect(() => {
    if (!userId) return;

    loadNotificationCounts();
    loadNotificationSettings();

    // Écouter les changements en temps réel avec Supabase
    const messagesSubscription = supabase
      .channel('messages_notifications')
      .on('postgres_changes', 
        { event: 'INSERT', schema: 'public', table: 'messages' },
        (payload) => {
          if (payload.new && settings.showInSidebar) {
            setCounts(prev => ({ 
              ...prev, 
              messages: prev.messages + 1, 
              total: prev.total + 1 
            }));
            playNotificationSound();
          }
        }
      )
      .subscribe();

    const friendsSubscription = supabase
      .channel('friends_notifications')
      .on('postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'friendships' },
        (payload) => {
          if (payload.new && payload.new.addressee_id === userId && settings.showInSidebar) {
            setCounts(prev => ({ 
              ...prev, 
              friendRequests: prev.friendRequests + 1, 
              total: prev.total + 1 
            }));
            playNotificationSound();
          }
        }
      )
      .subscribe();

    // Rafraîchir les compteurs toutes les 30 secondes
    const interval = setInterval(loadNotificationCounts, 30000);

    return () => {
      messagesSubscription.unsubscribe();
      friendsSubscription.unsubscribe();
      clearInterval(interval);
    };
  }, [userId, settings.showInSidebar]);

  return {
    counts,
    settings,
    isLoading,
    updateSettings,
    markAsRead,
    loadNotificationCounts,
    isQuietHours: isQuietHours()
  };
}
