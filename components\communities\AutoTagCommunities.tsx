'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { 
  Users, Music, Album, TrendingUp, Calendar, Search, 
  Hash, Star, Play, Heart, MessageCircle, Share2,
  UserPlus, Bell, BellOff, Settings, Filter
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';

interface TagCommunity {
  id: string;
  tag_id: number;
  name: string;
  description: string;
  avatar_url?: string;
  banner_url?: string;
  member_count: number;
  content_count: number;
  is_auto_generated: boolean;
  settings: {
    auto_join_on_tag_use: boolean;
    min_content_for_visibility: number;
    featured_content_limit: number;
  };
  tag: {
    id: number;
    name: string;
    color?: string;
  };
  is_member?: boolean;
  notification_level?: 'all' | 'important' | 'none';
}

interface CommunityContent {
  id: string;
  type: 'song' | 'album' | 'playlist' | 'artist';
  title: string;
  artist_name: string;
  thumbnail_url?: string;
  created_at: string;
  stats: {
    plays: number;
    likes: number;
    comments: number;
  };
  tags: string[];
}

interface CommunityActivity {
  id: string;
  type: 'new_content' | 'new_member' | 'trending' | 'discussion';
  title: string;
  description: string;
  user?: {
    id: string;
    name: string;
    avatar_url?: string;
  };
  content?: CommunityContent;
  created_at: string;
}

interface AutoTagCommunitiesProps {
  selectedTagId?: number;
  showJoinedOnly?: boolean;
  className?: string;
}

/**
 * Système de communautés automatiques basées sur les tags
 * Génère des pages communautés avec activité, contenu et membres
 */
export const AutoTagCommunities: React.FC<AutoTagCommunitiesProps> = ({
  selectedTagId,
  showJoinedOnly = false,
  className = ''
}) => {
  // États principaux
  const [communities, setCommunities] = useState<TagCommunity[]>([]);
  const [selectedCommunity, setSelectedCommunity] = useState<TagCommunity | null>(null);
  const [communityContent, setCommunityContent] = useState<CommunityContent[]>([]);
  const [communityActivity, setCommunityActivity] = useState<CommunityActivity[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Charger les communautés
  const loadCommunities = useCallback(async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (selectedTagId) params.append('tag_id', selectedTagId.toString());
      if (showJoinedOnly) params.append('joined_only', 'true');
      if (searchTerm) params.append('search', searchTerm);

      const response = await fetch(`/api/communities/tags?${params}`);
      const data = await response.json();
      setCommunities(data.communities || []);
    } catch (error) {
      console.error('Erreur chargement communautés:', error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedTagId, showJoinedOnly, searchTerm]);

  // Charger le contenu d'une communauté
  const loadCommunityContent = useCallback(async (communityId: string) => {
    try {
      const response = await fetch(`/api/communities/${communityId}/content`);
      const data = await response.json();
      setCommunityContent(data.content || []);
    } catch (error) {
      console.error('Erreur chargement contenu communauté:', error);
    }
  }, []);

  // Charger l'activité d'une communauté
  const loadCommunityActivity = useCallback(async (communityId: string) => {
    try {
      const response = await fetch(`/api/communities/${communityId}/activity`);
      const data = await response.json();
      setCommunityActivity(data.activity || []);
    } catch (error) {
      console.error('Erreur chargement activité communauté:', error);
    }
  }, []);

  // Rejoindre/quitter une communauté
  const handleJoinCommunity = useCallback(async (communityId: string, join: boolean) => {
    try {
      const response = await fetch(`/api/communities/${communityId}/members`, {
        method: join ? 'POST' : 'DELETE',
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.ok) {
        setCommunities(prev => prev.map(community => 
          community.id === communityId 
            ? { 
                ...community, 
                is_member: join,
                member_count: community.member_count + (join ? 1 : -1)
              }
            : community
        ));

        if (selectedCommunity?.id === communityId) {
          setSelectedCommunity(prev => prev ? {
            ...prev,
            is_member: join,
            member_count: prev.member_count + (join ? 1 : -1)
          } : null);
        }
      }
    } catch (error) {
      console.error('Erreur rejoindre/quitter communauté:', error);
    }
  }, [selectedCommunity]);

  // Sélectionner une communauté
  const handleSelectCommunity = useCallback((community: TagCommunity) => {
    setSelectedCommunity(community);
    loadCommunityContent(community.id);
    loadCommunityActivity(community.id);
    setActiveTab('overview');
  }, [loadCommunityContent, loadCommunityActivity]);

  // Charger les communautés au montage
  useEffect(() => {
    loadCommunities();
  }, [loadCommunities]);

  // Composant CommunityCard
  const CommunityCard: React.FC<{ community: TagCommunity }> = ({ community }) => (
    <Card 
      className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
        selectedCommunity?.id === community.id ? 'ring-2 ring-primary' : ''
      }`}
      onClick={() => handleSelectCommunity(community)}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Avatar communauté */}
          <Avatar className="h-12 w-12 flex-shrink-0">
            <AvatarImage src={community.avatar_url} />
            <AvatarFallback style={{ backgroundColor: community.tag.color || '#6366f1' }}>
              <Hash className="w-6 h-6 text-white" />
            </AvatarFallback>
          </Avatar>

          {/* Informations */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-medium text-sm truncate">{community.name}</h3>
              {community.is_auto_generated && (
                <Badge variant="outline" className="text-xs">Auto</Badge>
              )}
            </div>
            
            <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
              {community.description}
            </p>
            
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Users className="w-3 h-3" />
                {community.member_count}
              </div>
              <div className="flex items-center gap-1">
                <Music className="w-3 h-3" />
                {community.content_count}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col gap-1">
            <Button
              variant={community.is_member ? "default" : "outline"}
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleJoinCommunity(community.id, !community.is_member);
              }}
              className="text-xs"
            >
              {community.is_member ? (
                <>
                  <Users className="w-3 h-3 mr-1" />
                  Membre
                </>
              ) : (
                <>
                  <UserPlus className="w-3 h-3 mr-1" />
                  Rejoindre
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Composant ContentItem
  const ContentItem: React.FC<{ content: CommunityContent }> = ({ content }) => (
    <Card className="hover:shadow-sm transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Thumbnail */}
          <div className="w-16 h-16 bg-muted rounded flex-shrink-0 flex items-center justify-center">
            {content.thumbnail_url ? (
              <img 
                src={content.thumbnail_url} 
                alt={content.title}
                className="w-full h-full object-cover rounded"
              />
            ) : (
              <Music className="w-6 h-6 text-muted-foreground" />
            )}
          </div>

          {/* Informations */}
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm mb-1 truncate">{content.title}</h4>
            <p className="text-xs text-muted-foreground mb-2">{content.artist_name}</p>
            
            {/* Tags */}
            <div className="flex gap-1 mb-2 flex-wrap">
              {content.tags.slice(0, 3).map(tag => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  #{tag}
                </Badge>
              ))}
            </div>
            
            {/* Stats */}
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Play className="w-3 h-3" />
                {content.stats.plays}
              </div>
              <div className="flex items-center gap-1">
                <Heart className="w-3 h-3" />
                {content.stats.likes}
              </div>
              <div className="flex items-center gap-1">
                <MessageCircle className="w-3 h-3" />
                {content.stats.comments}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm">
              <Play className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Heart className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Composant ActivityItem
  const ActivityItem: React.FC<{ activity: CommunityActivity }> = ({ activity }) => (
    <div className="flex items-start gap-3 p-3 hover:bg-muted/50 rounded-lg transition-colors">
      {/* Avatar utilisateur */}
      {activity.user && (
        <Avatar className="h-8 w-8 flex-shrink-0">
          <AvatarImage src={activity.user.avatar_url} />
          <AvatarFallback>
            {activity.user.name.charAt(0).toUpperCase()}
          </AvatarFallback>
        </Avatar>
      )}

      {/* Contenu */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <span className="text-sm font-medium">{activity.title}</span>
          <span className="text-xs text-muted-foreground">
            {formatDistanceToNow(new Date(activity.created_at), { 
              addSuffix: true, 
              locale: fr 
            })}
          </span>
        </div>
        
        <p className="text-sm text-muted-foreground mb-2">
          {activity.description}
        </p>
        
        {/* Contenu associé */}
        {activity.content && (
          <div className="bg-muted/30 p-2 rounded text-xs">
            <div className="flex items-center gap-2">
              <Music className="w-3 h-3" />
              <span className="font-medium">{activity.content.title}</span>
              <span className="text-muted-foreground">par {activity.content.artist_name}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className={`grid grid-cols-1 lg:grid-cols-3 gap-6 ${className}`}>
      {/* Liste des communautés */}
      <div className="lg:col-span-1 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Hash className="w-5 h-5" />
              Communautés par Tags
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Recherche */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Rechercher une communauté..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filtres */}
            <div className="flex gap-2">
              <Button
                variant={showJoinedOnly ? "default" : "outline"}
                size="sm"
                onClick={() => loadCommunities()}
              >
                Mes communautés
              </Button>
            </div>

            {/* Liste */}
            <ScrollArea className="h-[500px]">
              <div className="space-y-3">
                {isLoading ? (
                  <div className="text-center py-8 text-muted-foreground">
                    Chargement...
                  </div>
                ) : communities.length > 0 ? (
                  communities.map(community => (
                    <CommunityCard key={community.id} community={community} />
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Hash className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>Aucune communauté trouvée</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {/* Détail de la communauté sélectionnée */}
      <div className="lg:col-span-2">
        {selectedCommunity ? (
          <Card>
            <CardHeader>
              <div className="flex items-start gap-4">
                {/* Avatar et bannière */}
                <div className="relative">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={selectedCommunity.avatar_url} />
                    <AvatarFallback style={{ backgroundColor: selectedCommunity.tag.color || '#6366f1' }}>
                      <Hash className="w-8 h-8 text-white" />
                    </AvatarFallback>
                  </Avatar>
                </div>

                {/* Informations */}
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h2 className="text-xl font-bold">{selectedCommunity.name}</h2>
                    <Badge variant="outline">#{selectedCommunity.tag.name}</Badge>
                  </div>
                  
                  <p className="text-muted-foreground mb-3">
                    {selectedCommunity.description}
                  </p>
                  
                  <div className="flex items-center gap-6 text-sm">
                    <div className="flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      <span>{selectedCommunity.member_count} membres</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Music className="w-4 h-4" />
                      <span>{selectedCommunity.content_count} contenus</span>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2">
                  <Button
                    variant={selectedCommunity.is_member ? "default" : "outline"}
                    onClick={() => handleJoinCommunity(selectedCommunity.id, !selectedCommunity.is_member)}
                  >
                    {selectedCommunity.is_member ? (
                      <>
                        <Users className="w-4 h-4 mr-2" />
                        Membre
                      </>
                    ) : (
                      <>
                        <UserPlus className="w-4 h-4 mr-2" />
                        Rejoindre
                      </>
                    )}
                  </Button>
                  
                  {selectedCommunity.is_member && (
                    <Button variant="outline" size="sm">
                      <Bell className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="overview">Aperçu</TabsTrigger>
                  <TabsTrigger value="content">Contenu</TabsTrigger>
                  <TabsTrigger value="activity">Activité</TabsTrigger>
                </TabsList>
                
                <TabsContent value="overview" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Contenu récent */}
                    <div>
                      <h3 className="font-medium mb-3">Contenu récent</h3>
                      <div className="space-y-2">
                        {communityContent.slice(0, 3).map(content => (
                          <ContentItem key={content.id} content={content} />
                        ))}
                      </div>
                    </div>
                    
                    {/* Activité récente */}
                    <div>
                      <h3 className="font-medium mb-3">Activité récente</h3>
                      <div className="space-y-2">
                        {communityActivity.slice(0, 5).map(activity => (
                          <ActivityItem key={activity.id} activity={activity} />
                        ))}
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="content" className="space-y-4">
                  <div className="grid grid-cols-1 gap-3">
                    {communityContent.map(content => (
                      <ContentItem key={content.id} content={content} />
                    ))}
                  </div>
                </TabsContent>
                
                <TabsContent value="activity" className="space-y-2">
                  {communityActivity.map(activity => (
                    <ActivityItem key={activity.id} activity={activity} />
                  ))}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="flex items-center justify-center h-64">
              <div className="text-center text-muted-foreground">
                <Hash className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">Sélectionnez une communauté</p>
                <p className="text-sm">
                  Choisissez une communauté dans la liste pour voir son contenu et son activité.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};
