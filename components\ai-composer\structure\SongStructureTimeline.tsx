'use client';

import React, { useState, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, Plus, Trash2, Edit3, Play, 
  MoreVertical, GripVertical 
} from 'lucide-react';

interface SongSection {
  id: string;
  name: string;
  type: 'intro' | 'verse' | 'chorus' | 'bridge' | 'outro' | 'instrumental';
  duration: number; // en secondes
  order: number;
  lyrics?: string;
  chords?: string[];
}

interface SongStructureTimelineProps {
  sections?: SongSection[];
  onSectionsChange?: (sections: SongSection[]) => void;
  currentTime?: number;
  onTimeChange?: (time: number) => void;
  isPlaying?: boolean;
  onPlayPause?: () => void;
  className?: string;
}

/**
 * Timeline de structure de chanson
 * Remplace UnifiedSongStructureTimeline avec un nom plus clair
 */
export const SongStructureTimeline: React.FC<SongStructureTimelineProps> = ({
  sections = [],
  onSectionsChange,
  currentTime = 0,
  onTimeChange,
  isPlaying = false,
  onPlayPause,
  className = ''
}) => {
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [newSectionName, setNewSectionName] = useState('');

  const defaultSections: SongSection[] = [
    { id: '1', name: 'Intro', type: 'intro', duration: 8, order: 1 },
    { id: '2', name: 'Verse 1', type: 'verse', duration: 16, order: 2 },
    { id: '3', name: 'Chorus', type: 'chorus', duration: 16, order: 3 },
    { id: '4', name: 'Verse 2', type: 'verse', duration: 16, order: 4 },
    { id: '5', name: 'Chorus', type: 'chorus', duration: 16, order: 5 },
    { id: '6', name: 'Bridge', type: 'bridge', duration: 8, order: 6 },
    { id: '7', name: 'Chorus', type: 'chorus', duration: 16, order: 7 },
    { id: '8', name: 'Outro', type: 'outro', duration: 8, order: 8 },
  ];

  const activeSections = sections.length > 0 ? sections : defaultSections;
  const totalDuration = activeSections.reduce((sum, section) => sum + section.duration, 0);

  const addSection = useCallback(() => {
    const newSection: SongSection = {
      id: Date.now().toString(),
      name: newSectionName || 'Nouvelle section',
      type: 'verse',
      duration: 16,
      order: activeSections.length + 1
    };
    
    onSectionsChange?.([...activeSections, newSection]);
    setNewSectionName('');
  }, [activeSections, newSectionName, onSectionsChange]);

  const removeSection = useCallback((sectionId: string) => {
    const updatedSections = activeSections.filter(s => s.id !== sectionId);
    onSectionsChange?.(updatedSections);
  }, [activeSections, onSectionsChange]);

  const updateSection = useCallback((sectionId: string, updates: Partial<SongSection>) => {
    const updatedSections = activeSections.map(section =>
      section.id === sectionId ? { ...section, ...updates } : section
    );
    onSectionsChange?.(updatedSections);
    setEditingSection(null);
  }, [activeSections, onSectionsChange]);

  const getSectionTypeColor = (type: SongSection['type']) => {
    const colors = {
      intro: 'bg-blue-100 text-blue-800',
      verse: 'bg-green-100 text-green-800',
      chorus: 'bg-purple-100 text-purple-800',
      bridge: 'bg-orange-100 text-orange-800',
      outro: 'bg-gray-100 text-gray-800',
      instrumental: 'bg-yellow-100 text-yellow-800'
    };
    return colors[type] || colors.verse;
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getCurrentSection = () => {
    let accumulatedTime = 0;
    for (const section of activeSections) {
      if (currentTime >= accumulatedTime && currentTime < accumulatedTime + section.duration) {
        return section;
      }
      accumulatedTime += section.duration;
    }
    return null;
  };

  const currentSection = getCurrentSection();

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Structure de la Chanson
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {formatTime(totalDuration)} total
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={onPlayPause}
            >
              <Play className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Timeline visuelle */}
        <div className="relative">
          <div className="flex h-12 bg-muted rounded-md overflow-hidden">
            {activeSections.map((section, index) => {
              const widthPercent = (section.duration / totalDuration) * 100;
              const isActive = currentSection?.id === section.id;
              
              return (
                <div
                  key={section.id}
                  className={`relative flex items-center justify-center text-xs font-medium transition-all cursor-pointer ${
                    isActive 
                      ? 'bg-primary text-primary-foreground' 
                      : getSectionTypeColor(section.type)
                  }`}
                  style={{ width: `${widthPercent}%` }}
                  onClick={() => {
                    const startTime = activeSections
                      .slice(0, index)
                      .reduce((sum, s) => sum + s.duration, 0);
                    onTimeChange?.(startTime);
                  }}
                >
                  <span className="truncate px-1">{section.name}</span>
                </div>
              );
            })}
          </div>
          
          {/* Curseur de lecture */}
          {totalDuration > 0 && (
            <div
              className="absolute top-0 bottom-0 w-0.5 bg-red-500 pointer-events-none"
              style={{ left: `${(currentTime / totalDuration) * 100}%` }}
            />
          )}
        </div>
        
        {/* Liste des sections */}
        <div className="space-y-2">
          {activeSections.map((section, index) => (
            <div
              key={section.id}
              className={`flex items-center gap-3 p-3 rounded-md border transition-all ${
                currentSection?.id === section.id 
                  ? 'border-primary bg-primary/5' 
                  : 'border-border'
              }`}
            >
              <GripVertical className="w-4 h-4 text-muted-foreground cursor-move" />
              
              <Badge variant="outline" className={getSectionTypeColor(section.type)}>
                {section.type}
              </Badge>
              
              {editingSection === section.id ? (
                <Input
                  value={section.name}
                  onChange={(e) => updateSection(section.id, { name: e.target.value })}
                  onBlur={() => setEditingSection(null)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') setEditingSection(null);
                  }}
                  className="flex-1 h-8"
                  autoFocus
                />
              ) : (
                <span 
                  className="flex-1 cursor-pointer"
                  onClick={() => setEditingSection(section.id)}
                >
                  {section.name}
                </span>
              )}
              
              <span className="text-sm text-muted-foreground min-w-[60px]">
                {formatTime(section.duration)}
              </span>
              
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setEditingSection(section.id)}
                >
                  <Edit3 className="w-3 h-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeSection(section.id)}
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        {/* Ajouter une section */}
        <div className="flex items-center gap-2">
          <Input
            placeholder="Nom de la nouvelle section"
            value={newSectionName}
            onChange={(e) => setNewSectionName(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') addSection();
            }}
            className="flex-1"
          />
          <Button onClick={addSection} size="sm">
            <Plus className="w-4 h-4 mr-1" />
            Ajouter
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
