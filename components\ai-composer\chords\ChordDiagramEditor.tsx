'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { 
  Music, Guitar, Piano, Edit, Eye, Save, Download, Upload, 
  Play, Pause, RotateCcw, Copy, Trash2, Plus, Minus,
  Grid3X3, Palette, Settings, Zap, Target
} from 'lucide-react';

// Import des composants existants
import { ChordDiagramViewer } from '@/components/chord-system/components/ChordDiagramViewer';
import { MidiChordPlayer } from '@/lib/chords/midi-chord-player';

interface ChordPosition {
  frets: number[];
  fingers: number[];
  midi: number[];
  difficulty: 'easy' | 'medium' | 'hard';
  baseFret: number;
  barres?: Array<{ fret: number; fromString: number; toString: number; }>;
}

interface ChordDiagram {
  id: string;
  name: string;
  instrument: 'guitar' | 'ukulele' | 'bass' | 'mandolin' | 'piano';
  positions: ChordPosition[];
  tags: string[];
  createdAt: string;
  isCustom: boolean;
}

interface ChordDiagramEditorProps {
  initialChord?: ChordDiagram;
  onSave?: (chord: ChordDiagram) => void;
  onCancel?: () => void;
  mode?: 'create' | 'edit' | 'view';
  className?: string;
}

/**
 * Éditeur/Créateur de diagrammes d'accords mega pro
 * Combine visualisation, édition, et création d'accords personnalisés
 */
export const ChordDiagramEditor: React.FC<ChordDiagramEditorProps> = ({
  initialChord,
  onSave,
  onCancel,
  mode = 'create',
  className = ''
}) => {
  // États principaux
  const [chord, setChord] = useState<ChordDiagram>(
    initialChord || {
      id: crypto.randomUUID(),
      name: 'Nouvel Accord',
      instrument: 'guitar',
      positions: [{
        frets: [0, 0, 0, 0, 0, 0],
        fingers: [0, 0, 0, 0, 0, 0],
        midi: [],
        difficulty: 'medium',
        baseFret: 0,
        barres: []
      }],
      tags: [],
      createdAt: new Date().toISOString(),
      isCustom: true
    }
  );

  const [activePositionIndex, setActivePositionIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [editMode, setEditMode] = useState<'frets' | 'fingers' | 'barres'>('frets');
  const [selectedString, setSelectedString] = useState<number | null>(null);
  const [zoom, setZoom] = useState(100);

  // Player MIDI
  const [midiPlayer] = useState(() => new MidiChordPlayer());

  // Configuration des instruments
  const instrumentConfigs = {
    guitar: { strings: 6, tuning: ['E', 'A', 'D', 'G', 'B', 'E'], maxFret: 24 },
    ukulele: { strings: 4, tuning: ['G', 'C', 'E', 'A'], maxFret: 15 },
    bass: { strings: 4, tuning: ['E', 'A', 'D', 'G'], maxFret: 24 },
    mandolin: { strings: 4, tuning: ['G', 'D', 'A', 'E'], maxFret: 15 },
    piano: { strings: 0, tuning: [], maxFret: 0 }
  };

  const currentConfig = instrumentConfigs[chord.instrument];
  const activePosition = chord.positions[activePositionIndex];

  // Gestion de la lecture MIDI
  const playChord = useCallback(async () => {
    if (!activePosition.midi || activePosition.midi.length === 0) return;

    try {
      setIsPlaying(true);
      await midiPlayer.playChord(activePosition, 2000);
      setTimeout(() => setIsPlaying(false), 2000);
    } catch (error) {
      console.error('Erreur lecture MIDI:', error);
      setIsPlaying(false);
    }
  }, [activePosition, midiPlayer]);

  // Mise à jour d'une frette
  const updateFret = useCallback((stringIndex: number, fret: number) => {
    const newPositions = [...chord.positions];
    const newFrets = [...newPositions[activePositionIndex].frets];
    newFrets[stringIndex] = fret;
    newPositions[activePositionIndex] = {
      ...newPositions[activePositionIndex],
      frets: newFrets
    };
    
    setChord(prev => ({ ...prev, positions: newPositions }));
  }, [chord.positions, activePositionIndex]);

  // Mise à jour d'un doigté
  const updateFinger = useCallback((stringIndex: number, finger: number) => {
    const newPositions = [...chord.positions];
    const newFingers = [...newPositions[activePositionIndex].fingers];
    newFingers[stringIndex] = finger;
    newPositions[activePositionIndex] = {
      ...newPositions[activePositionIndex],
      fingers: newFingers
    };
    
    setChord(prev => ({ ...prev, positions: newPositions }));
  }, [chord.positions, activePositionIndex]);

  // Ajout d'une nouvelle position
  const addPosition = useCallback(() => {
    const newPosition: ChordPosition = {
      frets: new Array(currentConfig.strings).fill(0),
      fingers: new Array(currentConfig.strings).fill(0),
      midi: [],
      difficulty: 'medium',
      baseFret: 0,
      barres: []
    };
    
    setChord(prev => ({
      ...prev,
      positions: [...prev.positions, newPosition]
    }));
    setActivePositionIndex(chord.positions.length);
  }, [currentConfig.strings, chord.positions.length]);

  // Suppression d'une position
  const removePosition = useCallback((index: number) => {
    if (chord.positions.length <= 1) return;
    
    const newPositions = chord.positions.filter((_, i) => i !== index);
    setChord(prev => ({ ...prev, positions: newPositions }));
    
    if (activePositionIndex >= newPositions.length) {
      setActivePositionIndex(newPositions.length - 1);
    }
  }, [chord.positions, activePositionIndex]);

  // Calcul automatique des notes MIDI
  const calculateMidiNotes = useCallback(() => {
    if (chord.instrument === 'piano') return;

    const baseMidiNotes = {
      guitar: [40, 45, 50, 55, 59, 64], // E2, A2, D3, G3, B3, E4
      ukulele: [67, 60, 64, 69], // G4, C4, E4, A4
      bass: [28, 33, 38, 43], // E1, A1, D2, G2
      mandolin: [55, 62, 69, 76] // G3, D4, A4, E5
    };

    const baseMidi = baseMidiNotes[chord.instrument] || [];
    const newMidi = activePosition.frets.map((fret, index) => {
      if (fret === -1) return 0; // Corde muette
      return baseMidi[index] + fret + activePosition.baseFret;
    }).filter(note => note > 0);

    const newPositions = [...chord.positions];
    newPositions[activePositionIndex] = {
      ...newPositions[activePositionIndex],
      midi: newMidi
    };
    
    setChord(prev => ({ ...prev, positions: newPositions }));
  }, [chord.instrument, activePosition, activePositionIndex, chord.positions]);

  // Effet pour recalculer les notes MIDI
  useEffect(() => {
    calculateMidiNotes();
  }, [activePosition.frets, activePosition.baseFret]);

  // Sauvegarde
  const handleSave = useCallback(() => {
    onSave?.(chord);
  }, [chord, onSave]);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Music className="w-5 h-5" />
            Éditeur de Diagrammes d'Accords Mega Pro
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {mode === 'create' ? 'Création' : mode === 'edit' ? 'Édition' : 'Visualisation'}
            </Badge>
            {chord.isCustom && (
              <Badge variant="secondary">Personnalisé</Badge>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="editor" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="editor">Éditeur</TabsTrigger>
            <TabsTrigger value="positions">Positions</TabsTrigger>
            <TabsTrigger value="properties">Propriétés</TabsTrigger>
            <TabsTrigger value="export">Export</TabsTrigger>
          </TabsList>
          
          {/* Onglet Éditeur */}
          <TabsContent value="editor" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Panneau de visualisation */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Visualisation</h3>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={playChord}
                      disabled={isPlaying || activePosition.midi.length === 0}
                    >
                      {isPlaying ? (
                        <Pause className="w-4 h-4" />
                      ) : (
                        <Play className="w-4 h-4" />
                      )}
                    </Button>
                    
                    <div className="flex items-center gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setZoom(Math.max(50, zoom - 25))}
                      >
                        <Minus className="w-4 h-4" />
                      </Button>
                      <span className="text-sm w-12 text-center">{zoom}%</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setZoom(Math.min(200, zoom + 25))}
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
                
                {/* Diagramme interactif */}
                <div 
                  className="border rounded-lg p-4 bg-muted/30"
                  style={{ transform: `scale(${zoom / 100})`, transformOrigin: 'top left' }}
                >
                  {chord.instrument !== 'piano' ? (
                    <div className="space-y-4">
                      {/* Grille de frettes interactive */}
                      <div className="grid gap-2" style={{ gridTemplateColumns: `repeat(${currentConfig.strings}, 1fr)` }}>
                        {/* En-têtes des cordes */}
                        {currentConfig.tuning.map((note, index) => (
                          <div key={index} className="text-center text-sm font-medium">
                            {note}
                          </div>
                        ))}
                        
                        {/* Cases à vide */}
                        {activePosition.frets.map((fret, stringIndex) => (
                          <div
                            key={`open-${stringIndex}`}
                            className={`h-8 border rounded cursor-pointer flex items-center justify-center text-sm font-medium ${
                              fret === 0 ? 'bg-green-100 text-green-800' : 
                              fret === -1 ? 'bg-red-100 text-red-800' : 'bg-muted'
                            } ${selectedString === stringIndex ? 'ring-2 ring-primary' : ''}`}
                            onClick={() => {
                              setSelectedString(stringIndex);
                              if (mode !== 'view') {
                                updateFret(stringIndex, fret === 0 ? -1 : 0);
                              }
                            }}
                          >
                            {fret === 0 ? '○' : fret === -1 ? '×' : ''}
                          </div>
                        ))}
                        
                        {/* Grille de frettes */}
                        {Array.from({ length: 5 }, (_, fretIndex) => (
                          activePosition.frets.map((currentFret, stringIndex) => (
                            <div
                              key={`fret-${fretIndex + 1}-${stringIndex}`}
                              className={`h-8 border rounded cursor-pointer flex items-center justify-center text-sm font-medium ${
                                currentFret === fretIndex + 1 + activePosition.baseFret ? 'bg-blue-100 text-blue-800' : 'bg-background hover:bg-muted'
                              } ${selectedString === stringIndex ? 'ring-2 ring-primary' : ''}`}
                              onClick={() => {
                                setSelectedString(stringIndex);
                                if (mode !== 'view') {
                                  const newFret = currentFret === fretIndex + 1 + activePosition.baseFret ? 0 : fretIndex + 1 + activePosition.baseFret;
                                  updateFret(stringIndex, newFret);
                                }
                              }}
                            >
                              {currentFret === fretIndex + 1 + activePosition.baseFret && (
                                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs">
                                  {activePosition.fingers[stringIndex] || ''}
                                </div>
                              )}
                            </div>
                          ))
                        ))}
                      </div>
                      
                      {/* Numéros de frettes */}
                      <div className="flex justify-center gap-8 text-sm text-muted-foreground">
                        {Array.from({ length: 5 }, (_, i) => (
                          <span key={i}>{i + 1 + activePosition.baseFret}</span>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Piano className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
                      <p className="text-muted-foreground">Éditeur piano en développement</p>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Panneau de contrôles */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Contrôles d'Édition</h3>
                
                {/* Informations de base */}
                <div className="space-y-3">
                  <div>
                    <Label>Nom de l'accord</Label>
                    <Input
                      value={chord.name}
                      onChange={(e) => setChord(prev => ({ ...prev, name: e.target.value }))}
                      disabled={mode === 'view'}
                    />
                  </div>
                  
                  <div>
                    <Label>Instrument</Label>
                    <Select 
                      value={chord.instrument} 
                      onValueChange={(value: any) => setChord(prev => ({ ...prev, instrument: value }))}
                      disabled={mode === 'view'}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="guitar">Guitare</SelectItem>
                        <SelectItem value="ukulele">Ukulélé</SelectItem>
                        <SelectItem value="bass">Basse</SelectItem>
                        <SelectItem value="mandolin">Mandoline</SelectItem>
                        <SelectItem value="piano">Piano</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label>Frette de base: {activePosition.baseFret}</Label>
                    <Slider
                      value={[activePosition.baseFret]}
                      onValueChange={([value]) => {
                        const newPositions = [...chord.positions];
                        newPositions[activePositionIndex] = {
                          ...newPositions[activePositionIndex],
                          baseFret: value
                        };
                        setChord(prev => ({ ...prev, positions: newPositions }));
                      }}
                      max={12}
                      min={0}
                      step={1}
                      disabled={mode === 'view'}
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <Label>Difficulté</Label>
                    <Select 
                      value={activePosition.difficulty} 
                      onValueChange={(value: any) => {
                        const newPositions = [...chord.positions];
                        newPositions[activePositionIndex] = {
                          ...newPositions[activePositionIndex],
                          difficulty: value
                        };
                        setChord(prev => ({ ...prev, positions: newPositions }));
                      }}
                      disabled={mode === 'view'}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="easy">Facile</SelectItem>
                        <SelectItem value="medium">Moyen</SelectItem>
                        <SelectItem value="hard">Difficile</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                {/* Mode d'édition */}
                {mode !== 'view' && (
                  <div className="space-y-3">
                    <Label>Mode d'édition</Label>
                    <div className="flex gap-2">
                      <Button
                        variant={editMode === 'frets' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setEditMode('frets')}
                      >
                        Frettes
                      </Button>
                      <Button
                        variant={editMode === 'fingers' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setEditMode('fingers')}
                      >
                        Doigtés
                      </Button>
                      <Button
                        variant={editMode === 'barres' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setEditMode('barres')}
                      >
                        Barrés
                      </Button>
                    </div>
                  </div>
                )}
                
                {/* Actions */}
                <div className="flex gap-2">
                  {mode !== 'view' && (
                    <>
                      <Button onClick={handleSave} className="flex-1">
                        <Save className="w-4 h-4 mr-2" />
                        Sauvegarder
                      </Button>
                      <Button variant="outline" onClick={onCancel}>
                        Annuler
                      </Button>
                    </>
                  )}
                  
                  <Button variant="outline" size="sm">
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
          
          {/* Autres onglets à implémenter */}
          <TabsContent value="positions">
            <div className="text-center py-8 text-muted-foreground">
              <Grid3X3 className="w-12 h-12 mx-auto mb-4" />
              <p>Gestion des positions multiples</p>
            </div>
          </TabsContent>
          
          <TabsContent value="properties">
            <div className="text-center py-8 text-muted-foreground">
              <Settings className="w-12 h-12 mx-auto mb-4" />
              <p>Propriétés avancées</p>
            </div>
          </TabsContent>
          
          <TabsContent value="export">
            <div className="text-center py-8 text-muted-foreground">
              <Download className="w-12 h-12 mx-auto mb-4" />
              <p>Options d'export</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
