import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'À Propos de Mouvik',
  description: 'Découvrez la vision de Mouvik, nos offres d\'abonnement et comment nous contacter.',
};

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-12 text-white">
      <header className="text-center mb-12">
        <h1 className="text-5xl font-bold tracking-tighter text-transparent bg-clip-text bg-gradient-to-r from-[#3dd6f5] to-purple-400 drop-shadow-sm">
          Bienvenue sur Mouvik
        </h1>
        <p className="mt-4 text-lg text-slate-300 max-w-3xl mx-auto">
          Votre plateforme de création musicale collaborative, propulsée par l'intelligence artificielle.
        </p>
      </header>

      <section id="presentation" className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-6 text-blue-400">Notre Vision</h2>
        <p className="text-slate-400 leading-relaxed max-w-4xl mx-auto text-center">
          Mouvik a été créé par des musiciens, pour des musiciens. Notre mission est de briser les barrières de la création musicale en offrant des outils intuitifs et puissants qui permettent aux artistes de collaborer, d'expérimenter et de donner vie à leurs idées, peu importe où ils se trouvent. Avec l'aide de notre IA avancée, nous vous aidons à trouver l'inspiration, à structurer vos morceaux et à repousser les limites de votre créativité.
        </p>
      </section>

      <section id="abonnements" className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-8 text-purple-400">Nos Offres</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {/* Free Tier */}
          <div className="bg-[#0a1118] p-6 rounded-lg border border-slate-800 text-center">
            <h3 className="text-2xl font-bold text-slate-300">Free</h3>
            <p className="text-4xl font-extrabold my-4">0€<span className="text-base font-normal text-slate-400">/mois</span></p>
            <ul className="text-slate-400 space-y-2">
              <li>3 projets</li>
              <li>Stockage limité</li>
              <li>Accès de base à l'IA</li>
            </ul>
          </div>
          {/* Pro Tier */}
          <div className="bg-[#0a1118] p-6 rounded-lg border border-blue-700 text-center ring-2 ring-blue-500 shadow-lg shadow-blue-500/20">
            <h3 className="text-2xl font-bold text-blue-400">Pro</h3>
            <p className="text-4xl font-extrabold my-4">15€<span className="text-base font-normal text-slate-400">/mois</span></p>
            <ul className="text-slate-300 space-y-2">
              <li>Projets illimités</li>
              <li>Stockage étendu</li>
              <li>Accès complet à l'IA</li>
              <li>Support prioritaire</li>
            </ul>
          </div>
          {/* Studio Tier */}
          <div className="bg-[#0a1118] p-6 rounded-lg border border-slate-800 text-center">
            <h3 className="text-2xl font-bold text-purple-400">Studio</h3>
            <p className="text-4xl font-extrabold my-4">45€<span className="text-base font-normal text-slate-400">/mois</span></p>
            <ul className="text-slate-400 space-y-2">
              <li>Tous les avantages Pro</li>
              <li>Outils de mastering IA</li>
              <li>Collaboration en temps réel</li>
              <li>Accès anticipé aux nouveautés</li>
            </ul>
          </div>
        </div>
      </section>

      <section id="contact" className="text-center">
        <h2 className="text-3xl font-bold mb-4 text-teal-400">Contactez-nous</h2>
        <p className="text-slate-400 mb-6">
          Une question, une suggestion ou besoin d'aide ? Notre équipe est là pour vous.
        </p>
        <a href="mailto:<EMAIL>" className="inline-block bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold py-3 px-8 rounded-lg hover:scale-105 transition-transform duration-300">
          Envoyer un email
        </a>
      </section>
    </div>
  );
}
