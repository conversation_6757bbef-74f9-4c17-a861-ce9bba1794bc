"use client"

import React, { ReactNode, useCallback, useEffect, useState } from "react"
import { useIsMobile } from "@/hooks/use-mobile"
import { useHydrated } from "@/hooks/useHydrated"

const SIDEBAR_COOKIE_NAME = "sidebar:state"
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7
const SIDEBAR_KEYBOARD_SHORTCUT = "b"

type SidebarContextType = {
  state: "expanded" | "collapsed"
  toggleSidebar: () => void
  isMobile: boolean
  openMobile: boolean
  setOpenMobile: (open: boolean) => void
}

const SidebarContext = React.createContext<SidebarContextType | null>(null)

export function useSidebar() {
  const context = React.useContext(SidebarContext)
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider.")
  }
  return context
}

export function SidebarProvider({ children, collapsed: defaultCollapsed = false }: { children: ReactNode, collapsed?: boolean }) {
  const isMobile = useIsMobile()
  const hydrated = useHydrated()
  const [openMobile, setOpenMobile] = useState(false)

  const [state, setState] = useState<"expanded" | "collapsed">("expanded")

  useEffect(() => {
    if (!hydrated) return;
    const cookieValue = document.cookie
      .split("; ")
      .find((row) => row.startsWith(`${SIDEBAR_COOKIE_NAME}=`))
      ?.split("=")[1]

    if (cookieValue === 'collapsed' || cookieValue === 'expanded') {
      setState(cookieValue)
    } else {
      setState(defaultCollapsed ? 'collapsed' : 'expanded')
    }
  }, [hydrated, defaultCollapsed])


  const toggleSidebar = useCallback(() => {
    setState((prevState) => {
      const newState = prevState === "expanded" ? "collapsed" : "expanded"
      document.cookie = `${SIDEBAR_COOKIE_NAME}=${newState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`
      return newState
    })
  }, [])

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {
        event.preventDefault()
        toggleSidebar()
      }
    }
    document.addEventListener("keydown", handleKeyDown)
    return () => {
      document.removeEventListener("keydown", handleKeyDown)
    }
  }, [toggleSidebar])

  const value: SidebarContextType = {
    state: hydrated ? state : (defaultCollapsed ? "collapsed" : "expanded"),
    toggleSidebar,
    isMobile,
    openMobile,
    setOpenMobile,
  }

  return (
    <SidebarContext.Provider value={value}>
      {children}
    </SidebarContext.Provider>
  )
}
