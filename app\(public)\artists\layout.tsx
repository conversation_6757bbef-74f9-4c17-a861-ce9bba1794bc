import React from "react";
import { AppSidebar } from "@/components/sidebar";
import { createSupabaseServerClient } from "@/lib/supabase/server";
import { UserProfileForSidebar } from "@/components/sidebar";

export default async function ArtistPublicLayout({ children }: { children: React.ReactNode }) {
  const supabase = createSupabaseServerClient();
  const { data: { user: authUser } } = await supabase.auth.getUser();

  // Pour les utilisateurs non authentifiés uniquement, nous préparons les données de profil pour la sidebar
  // Les utilisateurs authentifiés utiliseront déjà la sidebar du layout authentifié
  let userProfileForSidebar: UserProfileForSidebar | null = null;
  let isAuthenticated = !!authUser;

  if (!isAuthenticated) {
    // Pas besoin de préparer un profil pour la sidebar si l'utilisateur est déjà authentifié
    // car la sidebar sera déjà affichée par le layout authentifié
  }

  return (
    <div className="flex min-h-screen bg-black">
      {/* N'afficher la sidebar que pour les utilisateurs non authentifiés */}
      {!isAuthenticated && <AppSidebar user={userProfileForSidebar} />}
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
}
