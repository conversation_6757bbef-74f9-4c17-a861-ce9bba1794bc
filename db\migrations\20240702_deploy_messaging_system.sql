-- 🚀 DÉPLOIEMENT SYSTÈME DE MESSAGERIE UNIFIÉ - MOUVIK
-- Version: 1.0 - Juillet 2024
-- Description: Déploiement sécurisé des nouvelles tables de messagerie
-- Compatible avec l'existant, aucun conflit

-- ============================================================================
-- VÉRIFICATIONS PRÉALABLES
-- ============================================================================

-- Vérifier que les tables existantes sont bien présentes
DO $$
BEGIN
  -- Vérifier tables critiques existantes
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'songs') THEN
    RAISE EXCEPTION 'Table songs manquante - Arrêt du déploiement';
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'comments') THEN
    RAISE EXCEPTION 'Table comments manquante - Arrêt du déploiement';
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tags') THEN
    RAISE EXCEPTION 'Table tags manquante - Arrêt du déploiement';
  END IF;
  
  RAISE NOTICE 'Vérifications préalables OK - Déploiement autorisé';
END $$;

-- ============================================================================
-- 1. SYSTÈME D'AMIS AVANCÉ
-- ============================================================================

-- Table des relations d'amitié
CREATE TABLE IF NOT EXISTS friendships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  requester_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  addressee_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'blocked', 'declined')),
  relationship_type VARCHAR(20) DEFAULT 'friend' CHECK (relationship_type IN ('friend', 'close_friend', 'family', 'colleague', 'collaborator')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(requester_id, addressee_id)
);

-- Table des cercles d'amis
CREATE TABLE IF NOT EXISTS friend_circles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  color VARCHAR(7), -- Hex color
  icon VARCHAR(50), -- Icon name
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Association amis-cercles
CREATE TABLE IF NOT EXISTS friend_circle_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  circle_id UUID NOT NULL REFERENCES friend_circles(id) ON DELETE CASCADE,
  friend_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(circle_id, friend_id)
);

-- ============================================================================
-- 2. SYSTÈME DE MESSAGERIE TEMPS RÉEL
-- ============================================================================

-- Table des conversations (1-to-1, groupes, channels)
CREATE TABLE IF NOT EXISTS conversations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  type VARCHAR(20) NOT NULL DEFAULT 'direct' CHECK (type IN ('direct', 'group', 'channel', 'community')),
  name VARCHAR(255), -- Pour groupes/channels
  description TEXT,
  avatar_url TEXT,
  created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_archived BOOLEAN DEFAULT FALSE,
  settings JSONB DEFAULT '{
    "allow_media": true,
    "allow_links": true,
    "moderation_enabled": false,
    "auto_delete_after": null
  }'::jsonb
);

-- Table des participants aux conversations
CREATE TABLE IF NOT EXISTS conversation_participants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role VARCHAR(20) DEFAULT 'member' CHECK (role IN ('admin', 'moderator', 'member', 'guest')),
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notification_level VARCHAR(20) DEFAULT 'all' CHECK (notification_level IN ('all', 'mentions', 'none')),
  is_muted BOOLEAN DEFAULT FALSE,
  is_pinned BOOLEAN DEFAULT FALSE,
  UNIQUE(conversation_id, user_id)
);

-- Table des messages
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT,
  message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'audio', 'image', 'file', 'system', 'song_share', 'album_share')),
  metadata JSONB DEFAULT '{}'::jsonb, -- Pour fichiers, réponses, partages musicaux
  reply_to UUID REFERENCES messages(id) ON DELETE SET NULL,
  thread_id UUID REFERENCES messages(id) ON DELETE CASCADE, -- Pour les threads
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  edited_at TIMESTAMP WITH TIME ZONE,
  is_deleted BOOLEAN DEFAULT FALSE,
  reactions JSONB DEFAULT '{}'::jsonb -- {emoji: [user_ids]}
);

-- ============================================================================
-- 3. AMÉLIORATION SYSTÈME DE COMMENTAIRES EXISTANT
-- ============================================================================

-- Mise à jour de la table comments existante (sécurisée)
DO $$
BEGIN
  -- Ajouter colonnes seulement si elles n'existent pas
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'comments' AND column_name = 'reactions') THEN
    ALTER TABLE comments ADD COLUMN reactions JSONB DEFAULT '{}'::jsonb;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'comments' AND column_name = 'is_pinned') THEN
    ALTER TABLE comments ADD COLUMN is_pinned BOOLEAN DEFAULT FALSE;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'comments' AND column_name = 'is_highlighted') THEN
    ALTER TABLE comments ADD COLUMN is_highlighted BOOLEAN DEFAULT FALSE;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'comments' AND column_name = 'edited_at') THEN
    ALTER TABLE comments ADD COLUMN edited_at TIMESTAMP WITH TIME ZONE;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'comments' AND column_name = 'metadata') THEN
    ALTER TABLE comments ADD COLUMN metadata JSONB DEFAULT '{}'::jsonb;
  END IF;
  
  RAISE NOTICE 'Table comments mise à jour avec succès';
END $$;

-- Mise à jour des contraintes pour supporter plus de types (sécurisée)
DO $$
BEGIN
  -- Supprimer l'ancienne contrainte si elle existe
  IF EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'comments_resource_type_check') THEN
    ALTER TABLE comments DROP CONSTRAINT comments_resource_type_check;
  END IF;
  
  -- Ajouter la nouvelle contrainte
  ALTER TABLE comments ADD CONSTRAINT comments_resource_type_check 
  CHECK (resource_type IN ('song', 'album', 'playlist', 'artist', 'band', 'community'));
  
  RAISE NOTICE 'Contraintes comments mises à jour avec succès';
END $$;

-- ============================================================================
-- 4. COMMUNAUTÉS AUTOMATIQUES PAR TAGS
-- ============================================================================

-- Table des communautés basées sur tags/genres
CREATE TABLE IF NOT EXISTS tag_communities (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tag_id INTEGER NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL, -- Ex: "Communauté Jazz", "Fans de Rock"
  description TEXT,
  avatar_url TEXT,
  banner_url TEXT,
  member_count INTEGER DEFAULT 0,
  content_count INTEGER DEFAULT 0, -- Morceaux, albums, etc.
  is_auto_generated BOOLEAN DEFAULT TRUE,
  settings JSONB DEFAULT '{
    "auto_join_on_tag_use": true,
    "min_content_for_visibility": 3,
    "featured_content_limit": 10
  }'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tag_id)
);

-- Membres des communautés de tags
CREATE TABLE IF NOT EXISTS tag_community_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  community_id UUID NOT NULL REFERENCES tag_communities(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_auto_joined BOOLEAN DEFAULT TRUE, -- Rejoint automatiquement via utilisation du tag
  notification_level VARCHAR(20) DEFAULT 'important' CHECK (notification_level IN ('all', 'important', 'none')),
  UNIQUE(community_id, user_id)
);

-- ============================================================================
-- 5. NOTIFICATIONS INTELLIGENTES
-- ============================================================================

-- Table des notifications unifiées
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL, -- friend_request, message, comment, mention, community_activity, etc.
  title VARCHAR(255) NOT NULL,
  content TEXT,
  actor_id UUID REFERENCES auth.users(id) ON DELETE CASCADE, -- Qui a déclenché la notification
  target_type VARCHAR(50), -- conversation, comment, song, etc.
  target_id UUID,
  action_url TEXT, -- URL pour l'action (optionnel)
  metadata JSONB DEFAULT '{}'::jsonb,
  is_read BOOLEAN DEFAULT FALSE,
  is_seen BOOLEAN DEFAULT FALSE, -- Vu dans la liste mais pas forcément lu
  priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  delivery_methods VARCHAR(50)[] DEFAULT ARRAY['in_app'], -- in_app, push, email
  scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 6. ACTIVITÉ ET FEED SOCIAL
-- ============================================================================

-- Amélioration de la table activity existante pour le feed social
CREATE TABLE IF NOT EXISTS social_feed_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type VARCHAR(50) NOT NULL, -- new_song, new_album, joined_community, made_friend, etc.
  content_type VARCHAR(50), -- song, album, playlist, friendship, etc.
  content_id UUID,
  title VARCHAR(255),
  description TEXT,
  thumbnail_url TEXT,
  metadata JSONB DEFAULT '{}'::jsonb,
  visibility VARCHAR(20) DEFAULT 'friends' CHECK (visibility IN ('public', 'friends', 'private')),
  engagement_count INTEGER DEFAULT 0, -- Likes, comments, shares
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 7. INDEX ET OPTIMISATIONS
-- ============================================================================

-- Index pour les performances
CREATE INDEX IF NOT EXISTS idx_friendships_status ON friendships(status);
CREATE INDEX IF NOT EXISTS idx_friendships_users ON friendships(requester_id, addressee_id);
CREATE INDEX IF NOT EXISTS idx_conversations_type ON conversations(type);
CREATE INDEX IF NOT EXISTS idx_conversations_updated ON conversations(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_conversation ON messages(conversation_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_sender ON messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user ON notifications(user_id, is_read, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_tag_communities_tag ON tag_communities(tag_id);
CREATE INDEX IF NOT EXISTS idx_social_feed_user ON social_feed_items(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_comments_resource ON comments(resource_type, resource_id, created_at DESC);

-- Index pour la recherche full-text
CREATE INDEX IF NOT EXISTS idx_messages_content_search ON messages USING gin(to_tsvector('french', content));
CREATE INDEX IF NOT EXISTS idx_conversations_name_search ON conversations USING gin(to_tsvector('french', name));

-- ============================================================================
-- 8. FONCTIONS ET TRIGGERS
-- ============================================================================

-- Fonction pour mettre à jour last_message_at dans conversations
CREATE OR REPLACE FUNCTION update_conversation_last_message()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE conversations
  SET last_message_at = NEW.created_at,
      updated_at = NEW.created_at
  WHERE id = NEW.conversation_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour automatiquement last_message_at
DROP TRIGGER IF EXISTS trigger_update_conversation_last_message ON messages;
CREATE TRIGGER trigger_update_conversation_last_message
  AFTER INSERT ON messages
  FOR EACH ROW
  EXECUTE FUNCTION update_conversation_last_message();

-- Fonction pour créer automatiquement des communautés de tags
CREATE OR REPLACE FUNCTION create_tag_community_if_needed()
RETURNS TRIGGER AS $$
DECLARE
  tag_name TEXT;
  community_name TEXT;
BEGIN
  -- Récupérer le nom du tag
  SELECT name INTO tag_name FROM tags WHERE id = NEW.tag_id;

  -- Créer le nom de la communauté
  community_name := 'Communauté ' || INITCAP(tag_name);

  -- Créer la communauté si elle n'existe pas
  INSERT INTO tag_communities (tag_id, name, description)
  VALUES (
    NEW.tag_id,
    community_name,
    'Communauté automatique pour les fans de ' || tag_name
  )
  ON CONFLICT (tag_id) DO NOTHING;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour créer automatiquement des communautés
DROP TRIGGER IF EXISTS trigger_create_tag_community ON resource_tags;
CREATE TRIGGER trigger_create_tag_community
  AFTER INSERT ON resource_tags
  FOR EACH ROW
  EXECUTE FUNCTION create_tag_community_if_needed();

-- ============================================================================
-- 9. POLITIQUES RLS (ROW LEVEL SECURITY)
-- ============================================================================

-- Activer RLS sur toutes les nouvelles tables
ALTER TABLE friendships ENABLE ROW LEVEL SECURITY;
ALTER TABLE friend_circles ENABLE ROW LEVEL SECURITY;
ALTER TABLE friend_circle_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversation_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE tag_communities ENABLE ROW LEVEL SECURITY;
ALTER TABLE tag_community_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE social_feed_items ENABLE ROW LEVEL SECURITY;

-- Politiques pour friendships
CREATE POLICY "Users can view their own friendships" ON friendships
  FOR SELECT USING (requester_id = auth.uid() OR addressee_id = auth.uid());

CREATE POLICY "Users can create friendship requests" ON friendships
  FOR INSERT WITH CHECK (requester_id = auth.uid());

CREATE POLICY "Users can update their own friendship status" ON friendships
  FOR UPDATE USING (requester_id = auth.uid() OR addressee_id = auth.uid());

-- Politiques pour conversations
CREATE POLICY "Users can view conversations they participate in" ON conversations
  FOR SELECT USING (
    id IN (
      SELECT conversation_id FROM conversation_participants
      WHERE user_id = auth.uid()
    )
  );

-- Politiques pour messages
CREATE POLICY "Users can view messages in their conversations" ON messages
  FOR SELECT USING (
    conversation_id IN (
      SELECT conversation_id FROM conversation_participants
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can send messages to their conversations" ON messages
  FOR INSERT WITH CHECK (
    sender_id = auth.uid() AND
    conversation_id IN (
      SELECT conversation_id FROM conversation_participants
      WHERE user_id = auth.uid()
    )
  );

-- Politiques pour notifications
CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own notifications" ON notifications
  FOR UPDATE USING (user_id = auth.uid());

-- ============================================================================
-- 10. DONNÉES INITIALES
-- ============================================================================

-- Créer des communautés pour les genres principaux
INSERT INTO tag_communities (tag_id, name, description, avatar_url)
SELECT
  t.id,
  'Communauté ' || INITCAP(t.name),
  'Espace dédié aux passionnés de ' || t.name,
  CASE t.name
    WHEN 'rock' THEN '/images/communities/rock.jpg'
    WHEN 'jazz' THEN '/images/communities/jazz.jpg'
    WHEN 'electronic' THEN '/images/communities/electronic.jpg'
    ELSE '/images/communities/default.jpg'
  END
FROM tags t
WHERE t.name IN ('rock', 'pop', 'jazz', 'electronic', 'hip-hop', 'classical')
ON CONFLICT (tag_id) DO NOTHING;

-- ============================================================================
-- VÉRIFICATION FINALE
-- ============================================================================

DO $$
DECLARE
  table_count INTEGER;
  index_count INTEGER;
  trigger_count INTEGER;
BEGIN
  -- Compter les nouvelles tables créées
  SELECT COUNT(*) INTO table_count
  FROM information_schema.tables
  WHERE table_name IN (
    'friendships', 'friend_circles', 'friend_circle_members',
    'conversations', 'conversation_participants', 'messages',
    'tag_communities', 'tag_community_members',
    'notifications', 'social_feed_items'
  );

  -- Compter les index créés
  SELECT COUNT(*) INTO index_count
  FROM pg_indexes
  WHERE indexname LIKE 'idx_%'
  AND tablename IN (
    'friendships', 'conversations', 'messages', 'notifications',
    'tag_communities', 'social_feed_items', 'comments'
  );

  -- Compter les triggers créés
  SELECT COUNT(*) INTO trigger_count
  FROM pg_trigger
  WHERE tgname IN ('trigger_update_conversation_last_message', 'trigger_create_tag_community');

  RAISE NOTICE '✅ DÉPLOIEMENT COMPLET RÉUSSI !';
  RAISE NOTICE '📊 Tables créées: %', table_count;
  RAISE NOTICE '🚀 Index créés: %', index_count;
  RAISE NOTICE '⚡ Triggers créés: %', trigger_count;
  RAISE NOTICE '🎵 Système de messagerie unifié MOUVIK opérationnel !';
  RAISE NOTICE '🔒 Sécurité RLS activée sur toutes les tables';

  IF table_count = 10 THEN
    RAISE NOTICE '🎉 SUCCÈS TOTAL - Prêt pour les tests !';
  ELSE
    RAISE WARNING '⚠️ Déploiement partiel - Vérifier les erreurs';
  END IF;
END $$;
