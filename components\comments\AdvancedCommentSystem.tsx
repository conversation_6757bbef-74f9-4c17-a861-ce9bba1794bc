'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Heart, MessageCircle, Share2, MoreHorizontal, Pin, Flag, 
  Reply, Edit, Trash2, ThumbsUp, ThumbsDown, Smile, 
  Send, Image, Mic, AtSign, Hash, Clock, Eye
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';

interface Comment {
  id: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar_url?: string;
    is_verified?: boolean;
    role?: 'artist' | 'producer' | 'fan' | 'admin';
  };
  created_at: string;
  updated_at?: string;
  edited_at?: string;
  reactions: Record<string, string[]>; // {emoji: [user_ids]}
  reply_count: number;
  is_pinned: boolean;
  is_highlighted: boolean;
  metadata: {
    mentions?: string[];
    hashtags?: string[];
    media_urls?: string[];
  };
  replies?: Comment[];
}

interface AdvancedCommentSystemProps {
  resourceType: 'song' | 'album' | 'playlist' | 'artist' | 'band' | 'community';
  resourceId: string;
  resourceTitle?: string;
  currentUserId?: string;
  allowReplies?: boolean;
  allowReactions?: boolean;
  allowMedia?: boolean;
  maxDepth?: number;
  className?: string;
}

/**
 * Système de commentaires avancé et ergonomique
 * Support des réactions, réponses, mentions, hashtags, médias
 */
export const AdvancedCommentSystem: React.FC<AdvancedCommentSystemProps> = ({
  resourceType,
  resourceId,
  resourceTitle,
  currentUserId,
  allowReplies = true,
  allowReactions = true,
  allowMedia = true,
  maxDepth = 3,
  className = ''
}) => {
  // États principaux
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'popular'>('newest');
  const [showReplies, setShowReplies] = useState<Record<string, boolean>>({});

  // Références
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const replyTextareaRef = useRef<HTMLTextAreaElement>(null);

  // Émojis de réaction populaires
  const reactionEmojis = ['❤️', '👍', '👎', '😂', '😮', '😢', '🔥', '🎵'];

  // Charger les commentaires
  const loadComments = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/comments/${resourceType}/${resourceId}?sort=${sortBy}`);
      const data = await response.json();
      setComments(data.comments || []);
    } catch (error) {
      console.error('Erreur chargement commentaires:', error);
    } finally {
      setIsLoading(false);
    }
  }, [resourceType, resourceId, sortBy]);

  // Soumettre un nouveau commentaire
  const handleSubmitComment = useCallback(async (content: string, parentId?: string) => {
    if (!content.trim() || !currentUserId) return;

    try {
      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          resource_type: resourceType,
          resource_id: resourceId,
          content: content.trim(),
          parent_id: parentId,
          metadata: {
            mentions: extractMentions(content),
            hashtags: extractHashtags(content)
          }
        }),
      });

      if (response.ok) {
        const newCommentData = await response.json();
        
        if (parentId) {
          // Ajouter à la liste des réponses
          setComments(prev => prev.map(comment => 
            comment.id === parentId 
              ? { ...comment, replies: [...(comment.replies || []), newCommentData] }
              : comment
          ));
          setReplyingTo(null);
        } else {
          // Ajouter comme nouveau commentaire principal
          setComments(prev => [newCommentData, ...prev]);
          setNewComment('');
        }
      }
    } catch (error) {
      console.error('Erreur soumission commentaire:', error);
    }
  }, [resourceType, resourceId, currentUserId]);

  // Gérer les réactions
  const handleReaction = useCallback(async (commentId: string, emoji: string) => {
    if (!currentUserId) return;

    try {
      const response = await fetch(`/api/comments/${commentId}/reactions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ emoji, user_id: currentUserId }),
      });

      if (response.ok) {
        const updatedReactions = await response.json();
        setComments(prev => prev.map(comment => 
          comment.id === commentId 
            ? { ...comment, reactions: updatedReactions }
            : comment
        ));
      }
    } catch (error) {
      console.error('Erreur réaction:', error);
    }
  }, [currentUserId]);

  // Extraire mentions (@username)
  const extractMentions = (text: string): string[] => {
    const mentionRegex = /@(\w+)/g;
    const mentions = [];
    let match;
    while ((match = mentionRegex.exec(text)) !== null) {
      mentions.push(match[1]);
    }
    return mentions;
  };

  // Extraire hashtags (#tag)
  const extractHashtags = (text: string): string[] => {
    const hashtagRegex = /#(\w+)/g;
    const hashtags = [];
    let match;
    while ((match = hashtagRegex.exec(text)) !== null) {
      hashtags.push(match[1]);
    }
    return hashtags;
  };

  // Formater le contenu avec mentions et hashtags
  const formatContent = (content: string) => {
    return content
      .replace(/@(\w+)/g, '<span class="text-blue-600 font-medium">@$1</span>')
      .replace(/#(\w+)/g, '<span class="text-purple-600 font-medium">#$1</span>');
  };

  // Charger les commentaires au montage
  useEffect(() => {
    loadComments();
  }, [loadComments]);

  // Composant CommentItem
  const CommentItem: React.FC<{ comment: Comment; depth?: number }> = ({ comment, depth = 0 }) => {
    const [showReactionPicker, setShowReactionPicker] = useState(false);
    const [replyContent, setReplyContent] = useState('');

    const totalReactions = Object.values(comment.reactions).flat().length;
    const userReactions = Object.entries(comment.reactions)
      .filter(([_, userIds]) => userIds.includes(currentUserId || ''))
      .map(([emoji]) => emoji);

    return (
      <div className={`space-y-3 ${depth > 0 ? 'ml-8 pl-4 border-l-2 border-muted' : ''}`}>
        <div className="flex gap-3">
          {/* Avatar */}
          <Avatar className="h-8 w-8 flex-shrink-0">
            <AvatarImage src={comment.author.avatar_url} />
            <AvatarFallback>
              {comment.author.name.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>

          {/* Contenu principal */}
          <div className="flex-1 space-y-2">
            {/* En-tête */}
            <div className="flex items-center gap-2 flex-wrap">
              <span className="font-medium text-sm">{comment.author.name}</span>
              
              {comment.author.is_verified && (
                <Badge variant="secondary" className="text-xs">
                  ✓ Vérifié
                </Badge>
              )}
              
              {comment.author.role && (
                <Badge variant="outline" className="text-xs">
                  {comment.author.role}
                </Badge>
              )}
              
              {comment.is_pinned && (
                <Pin className="w-3 h-3 text-yellow-600" />
              )}
              
              <span className="text-xs text-muted-foreground">
                {formatDistanceToNow(new Date(comment.created_at), { 
                  addSuffix: true, 
                  locale: fr 
                })}
              </span>
              
              {comment.edited_at && (
                <span className="text-xs text-muted-foreground">(modifié)</span>
              )}
            </div>

            {/* Contenu du commentaire */}
            <div 
              className={`text-sm leading-relaxed ${comment.is_highlighted ? 'bg-yellow-50 p-2 rounded' : ''}`}
              dangerouslySetInnerHTML={{ __html: formatContent(comment.content) }}
            />

            {/* Médias (si présents) */}
            {comment.metadata.media_urls && comment.metadata.media_urls.length > 0 && (
              <div className="flex gap-2 flex-wrap">
                {comment.metadata.media_urls.map((url, index) => (
                  <img 
                    key={index}
                    src={url} 
                    alt="Média commentaire"
                    className="max-w-xs rounded border"
                  />
                ))}
              </div>
            )}

            {/* Actions */}
            <div className="flex items-center gap-4 text-xs">
              {/* Réactions */}
              {allowReactions && (
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2"
                    onClick={() => setShowReactionPicker(!showReactionPicker)}
                  >
                    <Smile className="w-3 h-3 mr-1" />
                    {totalReactions > 0 && totalReactions}
                  </Button>
                  
                  {/* Picker de réactions */}
                  {showReactionPicker && (
                    <div className="absolute z-10 bg-white border rounded-lg shadow-lg p-2 flex gap-1">
                      {reactionEmojis.map(emoji => (
                        <button
                          key={emoji}
                          onClick={() => {
                            handleReaction(comment.id, emoji);
                            setShowReactionPicker(false);
                          }}
                          className="hover:bg-muted p-1 rounded"
                        >
                          {emoji}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Répondre */}
              {allowReplies && depth < maxDepth && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2"
                  onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                >
                  <Reply className="w-3 h-3 mr-1" />
                  Répondre
                </Button>
              )}

              {/* Réponses existantes */}
              {comment.reply_count > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2"
                  onClick={() => setShowReplies(prev => ({
                    ...prev,
                    [comment.id]: !prev[comment.id]
                  }))}
                >
                  <MessageCircle className="w-3 h-3 mr-1" />
                  {comment.reply_count} réponse(s)
                </Button>
              )}

              {/* Actions utilisateur */}
              {currentUserId === comment.author.id && (
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2"
                    onClick={() => setEditingComment(comment.id)}
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 text-red-600"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              )}
            </div>

            {/* Réactions affichées */}
            {totalReactions > 0 && (
              <div className="flex gap-1 flex-wrap">
                {Object.entries(comment.reactions).map(([emoji, userIds]) => 
                  userIds.length > 0 && (
                    <button
                      key={emoji}
                      onClick={() => handleReaction(comment.id, emoji)}
                      className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs border transition-colors ${
                        userReactions.includes(emoji) 
                          ? 'bg-blue-100 border-blue-300 text-blue-700' 
                          : 'bg-muted border-muted-foreground/20 hover:bg-muted/80'
                      }`}
                    >
                      <span>{emoji}</span>
                      <span>{userIds.length}</span>
                    </button>
                  )
                )}
              </div>
            )}

            {/* Zone de réponse */}
            {replyingTo === comment.id && (
              <div className="space-y-2 pt-2">
                <Textarea
                  ref={replyTextareaRef}
                  value={replyContent}
                  onChange={(e) => setReplyContent(e.target.value)}
                  placeholder={`Répondre à ${comment.author.name}...`}
                  className="min-h-[80px] text-sm"
                />
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => {
                      handleSubmitComment(replyContent, comment.id);
                      setReplyContent('');
                    }}
                    disabled={!replyContent.trim()}
                  >
                    <Send className="w-3 h-3 mr-1" />
                    Répondre
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setReplyingTo(null);
                      setReplyContent('');
                    }}
                  >
                    Annuler
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Réponses */}
        {showReplies[comment.id] && comment.replies && comment.replies.length > 0 && (
          <div className="space-y-3">
            {comment.replies.map(reply => (
              <CommentItem key={reply.id} comment={reply} depth={depth + 1} />
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="w-5 h-5" />
            Commentaires ({comments.length})
          </CardTitle>
          
          {/* Tri */}
          <div className="flex items-center gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="text-sm border rounded px-2 py-1"
            >
              <option value="newest">Plus récents</option>
              <option value="oldest">Plus anciens</option>
              <option value="popular">Plus populaires</option>
            </select>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Zone de nouveau commentaire */}
        {currentUserId && (
          <div className="space-y-3">
            <Textarea
              ref={textareaRef}
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder={`Commenter ${resourceTitle ? `"${resourceTitle}"` : 'ce contenu'}...`}
              className="min-h-[100px]"
            />
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>Utilisez @username pour mentionner</span>
                <span>•</span>
                <span>Utilisez #tag pour tagger</span>
              </div>
              
              <div className="flex items-center gap-2">
                {allowMedia && (
                  <Button variant="outline" size="sm">
                    <Image className="w-4 h-4" />
                  </Button>
                )}
                
                <Button
                  onClick={() => handleSubmitComment(newComment)}
                  disabled={!newComment.trim() || isLoading}
                  size="sm"
                >
                  <Send className="w-4 h-4 mr-2" />
                  Publier
                </Button>
              </div>
            </div>
          </div>
        )}

        <Separator />

        {/* Liste des commentaires */}
        <ScrollArea className="max-h-[600px]">
          <div className="space-y-6">
            {isLoading ? (
              <div className="text-center py-8 text-muted-foreground">
                Chargement des commentaires...
              </div>
            ) : comments.length > 0 ? (
              comments.map(comment => (
                <CommentItem key={comment.id} comment={comment} />
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">Aucun commentaire</p>
                <p className="text-sm">
                  Soyez le premier à commenter {resourceTitle ? `"${resourceTitle}"` : 'ce contenu'} !
                </p>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};
