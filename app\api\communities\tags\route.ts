import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tagId = searchParams.get('tag_id');
    const joinedOnly = searchParams.get('joined_only') === 'true';
    const search = searchParams.get('search') || '';

    // Construire la requête de base
    let query = supabase
      .from('tag_communities')
      .select(`
        *,
        tag:tag_id(id, name, tag_type),
        tag_community_members!left(
          user_id,
          notification_level,
          is_auto_joined
        )
      `);

    // Filtrer par tag spécifique
    if (tagId) {
      query = query.eq('tag_id', parseInt(tagId));
    }

    // Filtrer par communautés rejointes
    if (joinedOnly) {
      query = query.eq('tag_community_members.user_id', user.id);
    }

    // Recherche
    if (search) {
      query = query.ilike('name', `%${search}%`);
    }

    // Ordonner par nombre de membres
    query = query.order('member_count', { ascending: false });

    const { data: communities, error } = await query;

    if (error) {
      console.error('Erreur chargement communautés tags:', error);
      return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
    }

    // Formater les données pour le frontend
    const formattedCommunities = communities?.map(community => {
      const membership = community.tag_community_members?.find(
        (member: any) => member.user_id === user.id
      );

      return {
        id: community.id,
        tag_id: community.tag_id,
        name: community.name,
        description: community.description,
        avatar_url: community.avatar_url,
        banner_url: community.banner_url,
        member_count: community.member_count,
        content_count: community.content_count,
        is_auto_generated: community.is_auto_generated,
        settings: community.settings,
        tag: {
          id: community.tag.id,
          name: community.tag.name,
          color: getTagColor(community.tag.name) // Fonction helper pour les couleurs
        },
        is_member: !!membership,
        notification_level: membership?.notification_level || 'important'
      };
    }) || [];

    return NextResponse.json({ communities: formattedCommunities });
  } catch (error) {
    console.error('Erreur API communautés tags:', error);
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
  }
}

// Fonction helper pour attribuer des couleurs aux tags
function getTagColor(tagName: string): string {
  const colors: Record<string, string> = {
    'rock': '#E91E63',
    'pop': '#2196F3',
    'jazz': '#FFC107',
    'electronic': '#9C27B0',
    'hip-hop': '#FF5722',
    'classical': '#795548',
    'folk': '#4CAF50',
    'metal': '#F44336',
    'indie': '#009688',
    'r&b': '#3F51B5'
  };
  
  return colors[tagName.toLowerCase()] || '#6366f1';
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
    }

    const body = await request.json();
    const { tag_id, name, description } = body;

    // Vérifier si la communauté existe déjà pour ce tag
    const { data: existing } = await supabase
      .from('tag_communities')
      .select('id')
      .eq('tag_id', tag_id)
      .single();

    if (existing) {
      return NextResponse.json({ error: 'Communauté déjà existante pour ce tag' }, { status: 400 });
    }

    // Créer la communauté
    const { data: community, error } = await supabase
      .from('tag_communities')
      .insert({
        tag_id,
        name,
        description,
        is_auto_generated: false
      })
      .select()
      .single();

    if (error) {
      console.error('Erreur création communauté tag:', error);
      return NextResponse.json({ error: 'Erreur création communauté' }, { status: 500 });
    }

    // Ajouter le créateur comme membre admin
    await supabase
      .from('tag_community_members')
      .insert({
        community_id: community.id,
        user_id: user.id,
        is_auto_joined: false
      });

    return NextResponse.json({ community });
  } catch (error) {
    console.error('Erreur API création communauté tag:', error);
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
  }
}
