'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  MessageCircle, Users, Hash, Bell, Search, Plus, 
  Settings, Filter, Star, Archive, Trash2, Pin,
  Phone, Video, MoreHorizontal, Send, Smile, Paperclip
} from 'lucide-react';

// Import des composants spécialisés
import { AdvancedCommentSystem } from '../comments/AdvancedCommentSystem';
import { AutoTagCommunities } from '../communities/AutoTagCommunities';

interface Conversation {
  id: string;
  type: 'direct' | 'group' | 'channel' | 'community';
  name?: string;
  avatar_url?: string;
  participants: Array<{
    id: string;
    name: string;
    avatar_url?: string;
    is_online?: boolean;
  }>;
  last_message?: {
    content: string;
    sender_name: string;
    created_at: string;
  };
  unread_count: number;
  is_pinned: boolean;
  is_muted: boolean;
  updated_at: string;
}

interface Friend {
  id: string;
  name: string;
  avatar_url?: string;
  is_online: boolean;
  status?: string;
  relationship_type: 'friend' | 'close_friend' | 'family' | 'colleague' | 'collaborator';
  mutual_friends_count: number;
  last_seen?: string;
}

interface Notification {
  id: string;
  type: string;
  title: string;
  content: string;
  actor?: {
    id: string;
    name: string;
    avatar_url?: string;
  };
  is_read: boolean;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  created_at: string;
  action_url?: string;
}

interface UnifiedMessagingHubProps {
  currentUserId?: string;
  initialTab?: 'conversations' | 'friends' | 'communities' | 'notifications';
  className?: string;
}

/**
 * Hub de messagerie unifié
 * Combine chat, amis, communautés, commentaires et notifications
 */
export const UnifiedMessagingHub: React.FC<UnifiedMessagingHubProps> = ({
  currentUserId,
  initialTab = 'conversations',
  className = ''
}) => {
  // États principaux
  const [activeTab, setActiveTab] = useState(initialTab);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [friends, setFriends] = useState<Friend[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Filtres et tri
  const [conversationFilter, setConversationFilter] = useState<'all' | 'unread' | 'pinned'>('all');
  const [friendFilter, setFriendFilter] = useState<'all' | 'online' | 'close'>('all');
  const [notificationFilter, setNotificationFilter] = useState<'all' | 'unread' | 'important'>('unread');

  // Charger les conversations
  const loadConversations = useCallback(async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (conversationFilter !== 'all') params.append('filter', conversationFilter);
      if (searchTerm) params.append('search', searchTerm);

      const response = await fetch(`/api/conversations?${params}`);
      const data = await response.json();
      setConversations(data.conversations || []);
    } catch (error) {
      console.error('Erreur chargement conversations:', error);
    } finally {
      setIsLoading(false);
    }
  }, [conversationFilter, searchTerm]);

  // Charger les amis
  const loadFriends = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (friendFilter !== 'all') params.append('filter', friendFilter);
      if (searchTerm) params.append('search', searchTerm);

      const response = await fetch(`/api/friends?${params}`);
      const data = await response.json();
      setFriends(data.friends || []);
    } catch (error) {
      console.error('Erreur chargement amis:', error);
    }
  }, [friendFilter, searchTerm]);

  // Charger les notifications
  const loadNotifications = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (notificationFilter !== 'all') params.append('filter', notificationFilter);

      const response = await fetch(`/api/notifications?${params}`);
      const data = await response.json();
      setNotifications(data.notifications || []);
    } catch (error) {
      console.error('Erreur chargement notifications:', error);
    }
  }, [notificationFilter]);

  // Marquer notification comme lue
  const markNotificationAsRead = useCallback(async (notificationId: string) => {
    try {
      await fetch(`/api/notifications/${notificationId}/read`, { method: 'POST' });
      setNotifications(prev => prev.map(notif => 
        notif.id === notificationId ? { ...notif, is_read: true } : notif
      ));
    } catch (error) {
      console.error('Erreur marquage notification:', error);
    }
  }, []);

  // Démarrer une conversation
  const startConversation = useCallback(async (friendId: string) => {
    try {
      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'direct',
          participant_ids: [friendId]
        }),
      });

      if (response.ok) {
        const newConversation = await response.json();
        setSelectedConversation(newConversation);
        setActiveTab('conversations');
        loadConversations();
      }
    } catch (error) {
      console.error('Erreur création conversation:', error);
    }
  }, [loadConversations]);

  // Charger les données au montage et changement d'onglet
  useEffect(() => {
    switch (activeTab) {
      case 'conversations':
        loadConversations();
        break;
      case 'friends':
        loadFriends();
        break;
      case 'notifications':
        loadNotifications();
        break;
    }
  }, [activeTab, loadConversations, loadFriends, loadNotifications]);

  // Compter les notifications non lues
  const unreadNotificationsCount = notifications.filter(n => !n.is_read).length;
  const unreadConversationsCount = conversations.reduce((sum, conv) => sum + conv.unread_count, 0);

  return (
    <div className={`h-screen flex flex-col ${className}`}>
      <Card className="flex-1 flex flex-col">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              Hub de Communication
            </div>
            
            <div className="flex items-center gap-2">
              {/* Recherche globale */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Rechercher..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              
              <Button variant="outline" size="sm">
                <Plus className="w-4 h-4" />
              </Button>
              
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col p-0">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <div className="px-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="conversations" className="relative">
                  Conversations
                  {unreadConversationsCount > 0 && (
                    <Badge variant="destructive" className="absolute -top-1 -right-1 h-5 w-5 text-xs p-0 flex items-center justify-center">
                      {unreadConversationsCount > 99 ? '99+' : unreadConversationsCount}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="friends">
                  Amis ({friends.length})
                </TabsTrigger>
                <TabsTrigger value="communities">
                  Communautés
                </TabsTrigger>
                <TabsTrigger value="notifications" className="relative">
                  Notifications
                  {unreadNotificationsCount > 0 && (
                    <Badge variant="destructive" className="absolute -top-1 -right-1 h-5 w-5 text-xs p-0 flex items-center justify-center">
                      {unreadNotificationsCount > 99 ? '99+' : unreadNotificationsCount}
                    </Badge>
                  )}
                </TabsTrigger>
              </TabsList>
            </div>
            
            <div className="flex-1 overflow-hidden">
              {/* Onglet Conversations */}
              <TabsContent value="conversations" className="h-full m-0 p-0">
                <div className="h-full flex">
                  {/* Liste des conversations */}
                  <div className="w-80 border-r flex flex-col">
                    {/* Filtres */}
                    <div className="p-4 border-b">
                      <div className="flex gap-2">
                        <Button
                          variant={conversationFilter === 'all' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setConversationFilter('all')}
                        >
                          Toutes
                        </Button>
                        <Button
                          variant={conversationFilter === 'unread' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setConversationFilter('unread')}
                        >
                          Non lues
                        </Button>
                        <Button
                          variant={conversationFilter === 'pinned' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setConversationFilter('pinned')}
                        >
                          Épinglées
                        </Button>
                      </div>
                    </div>
                    
                    {/* Liste */}
                    <ScrollArea className="flex-1">
                      <div className="p-2">
                        {conversations.map(conversation => (
                          <div
                            key={conversation.id}
                            className={`p-3 rounded-lg cursor-pointer transition-colors hover:bg-muted/50 ${
                              selectedConversation?.id === conversation.id ? 'bg-muted' : ''
                            }`}
                            onClick={() => setSelectedConversation(conversation)}
                          >
                            <div className="flex items-start gap-3">
                              {/* Avatar */}
                              <div className="relative">
                                <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                                  {conversation.type === 'direct' ? (
                                    <Users className="w-5 h-5" />
                                  ) : conversation.type === 'group' ? (
                                    <Users className="w-5 h-5" />
                                  ) : (
                                    <Hash className="w-5 h-5" />
                                  )}
                                </div>
                                {conversation.participants.some(p => p.is_online) && (
                                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
                                )}
                              </div>
                              
                              {/* Contenu */}
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="font-medium text-sm truncate">
                                    {conversation.name || conversation.participants.map(p => p.name).join(', ')}
                                  </span>
                                  <div className="flex items-center gap-1">
                                    {conversation.is_pinned && <Pin className="w-3 h-3 text-yellow-600" />}
                                    {conversation.unread_count > 0 && (
                                      <Badge variant="destructive" className="text-xs">
                                        {conversation.unread_count}
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                                
                                {conversation.last_message && (
                                  <p className="text-xs text-muted-foreground truncate">
                                    <span className="font-medium">{conversation.last_message.sender_name}:</span>{' '}
                                    {conversation.last_message.content}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                  
                  {/* Zone de conversation */}
                  <div className="flex-1 flex flex-col">
                    {selectedConversation ? (
                      <>
                        {/* En-tête conversation */}
                        <div className="p-4 border-b flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                              <Users className="w-5 h-5" />
                            </div>
                            <div>
                              <h3 className="font-medium">
                                {selectedConversation.name || selectedConversation.participants.map(p => p.name).join(', ')}
                              </h3>
                              <p className="text-xs text-muted-foreground">
                                {selectedConversation.participants.length} participant(s)
                              </p>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Phone className="w-4 h-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Video className="w-4 h-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                        
                        {/* Messages */}
                        <div className="flex-1 p-4">
                          <div className="text-center text-muted-foreground">
                            Interface de chat en cours de développement
                          </div>
                        </div>
                        
                        {/* Zone de saisie */}
                        <div className="p-4 border-t">
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Paperclip className="w-4 h-4" />
                            </Button>
                            <Input
                              placeholder="Tapez votre message..."
                              className="flex-1"
                            />
                            <Button variant="outline" size="sm">
                              <Smile className="w-4 h-4" />
                            </Button>
                            <Button size="sm">
                              <Send className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </>
                    ) : (
                      <div className="flex-1 flex items-center justify-center">
                        <div className="text-center text-muted-foreground">
                          <MessageCircle className="w-16 h-16 mx-auto mb-4 opacity-50" />
                          <p className="text-lg font-medium mb-2">Sélectionnez une conversation</p>
                          <p className="text-sm">Choisissez une conversation pour commencer à discuter.</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
              
              {/* Onglet Amis */}
              <TabsContent value="friends" className="h-full m-0 p-6">
                <div className="space-y-4">
                  {/* Filtres amis */}
                  <div className="flex gap-2">
                    <Button
                      variant={friendFilter === 'all' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setFriendFilter('all')}
                    >
                      Tous
                    </Button>
                    <Button
                      variant={friendFilter === 'online' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setFriendFilter('online')}
                    >
                      En ligne
                    </Button>
                    <Button
                      variant={friendFilter === 'close' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setFriendFilter('close')}
                    >
                      Proches
                    </Button>
                  </div>
                  
                  {/* Liste des amis */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {friends.map(friend => (
                      <Card key={friend.id} className="hover:shadow-sm transition-shadow">
                        <CardContent className="p-4">
                          <div className="flex items-start gap-3">
                            <div className="relative">
                              <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                                <Users className="w-6 h-6" />
                              </div>
                              {friend.is_online && (
                                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white" />
                              )}
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-sm mb-1">{friend.name}</h3>
                              <p className="text-xs text-muted-foreground mb-2">
                                {friend.mutual_friends_count} amis en commun
                              </p>
                              
                              <div className="flex gap-1">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => startConversation(friend.id)}
                                >
                                  <MessageCircle className="w-3 h-3 mr-1" />
                                  Message
                                </Button>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </TabsContent>
              
              {/* Onglet Communautés */}
              <TabsContent value="communities" className="h-full m-0 p-6">
                <AutoTagCommunities />
              </TabsContent>
              
              {/* Onglet Notifications */}
              <TabsContent value="notifications" className="h-full m-0 p-6">
                <div className="space-y-4">
                  {/* Filtres notifications */}
                  <div className="flex gap-2">
                    <Button
                      variant={notificationFilter === 'all' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setNotificationFilter('all')}
                    >
                      Toutes
                    </Button>
                    <Button
                      variant={notificationFilter === 'unread' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setNotificationFilter('unread')}
                    >
                      Non lues
                    </Button>
                    <Button
                      variant={notificationFilter === 'important' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setNotificationFilter('important')}
                    >
                      Importantes
                    </Button>
                  </div>
                  
                  {/* Liste des notifications */}
                  <div className="space-y-2">
                    {notifications.map(notification => (
                      <Card 
                        key={notification.id} 
                        className={`cursor-pointer transition-colors hover:bg-muted/50 ${
                          !notification.is_read ? 'border-blue-200 bg-blue-50/50' : ''
                        }`}
                        onClick={() => markNotificationAsRead(notification.id)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start gap-3">
                            {notification.actor && (
                              <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                                <Users className="w-5 h-5" />
                              </div>
                            )}
                            
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-sm">{notification.title}</h4>
                                {notification.priority === 'high' && (
                                  <Badge variant="destructive" className="text-xs">Urgent</Badge>
                                )}
                                {!notification.is_read && (
                                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                                )}
                              </div>
                              
                              <p className="text-sm text-muted-foreground mb-2">
                                {notification.content}
                              </p>
                              
                              <p className="text-xs text-muted-foreground">
                                {formatDistanceToNow(new Date(notification.created_at), { 
                                  addSuffix: true, 
                                  locale: fr 
                                })}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};
