"use client";

import {
  Act<PERSON>,
  <PERSON><PERSON>hart2,
  Bell,
  Compass,
  CreditCard,
  Disc,
  FileText,
  HelpCircle,
  ListMusic,
  ListTodo,
  LogOut,
  MessageCircle,
  Mic2,
  Music,
  PanelLeft,
  PanelRight,
  Plus,
  Radio,
  Settings,
  Sparkles,
  User as UserIcon,
  Users,
  Coins,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { createBrowserClient } from '@/lib/supabase/client';
import { useSidebar } from '@/components/ui/sidebar-logic';
import { cn } from '@/lib/utils';
import { User } from '@supabase/supabase-js';
import { isFeatureEnabled, FLAGS } from '@/lib/feature-flags';

import { Avatar, AvatarFallback, AvatarImage } from './avatar';
import { Badge } from './badge';
import { Button } from './button';
import {
  Sidebar,
  SidebarContent,
  <PERSON><PERSON><PERSON>ooter,
  SidebarHeader,
  SidebarMenuItem,
  SidebarSeparator,
} from './sidebar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './tooltip';

type NavItemAction = {
  href: string;
  icon: React.ElementType;
  tooltip: string;
};

type NavItem = {
  label: string;
  href: string;
  icon: React.ElementType;
  action?: NavItemAction;
  title?: string;
};

// Import UserProfileForSidebar type from sidebar index
import { UserProfileForSidebar } from '@/components/sidebar';

interface AppSidebarProps {
  user?: UserProfileForSidebar | null;
}

export function AppSidebar({ user: propUser }: AppSidebarProps = {}) {
  const { state, toggleSidebar } = useSidebar();
  const isUiCollapsed = state === "collapsed";
  const pathname = usePathname();
  const [user, setUser] = useState<UserProfileForSidebar | null>(propUser || null);
  const supabase = createBrowserClient();

  useEffect(() => {
    if (propUser) {
      setUser(propUser);
      return;
    }
    
    const fetchUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        
        const userWithProfile: UserProfileForSidebar = {
          id: user.id,
          email: user.email,
          username: profile?.username,
          display_name: profile?.display_name,
          full_name: profile?.full_name,
          avatar_url: profile?.avatar_url,
          subscription_tier: profile?.subscription_tier || 'free',
          mouvik_coins: profile?.mouvik_coins || 0
        };
        setUser(userWithProfile);
      }
    };
    fetchUser();
  }, [supabase, propUser]);

  const handleLogout = async () => {
    await supabase.auth.signOut();
    window.location.href = '/login';
  };

  const getStatusBadge = (tier: string) => {
    switch (tier) {
      case 'admin':
        return <Badge className="bg-red-500/20 text-red-300 border border-red-500/30 shadow-lg">Admin</Badge>;
      case 'studio':
        return <Badge className="bg-purple-500/20 text-purple-300 border border-purple-500/30 shadow-lg">Studio</Badge>;
      case 'pro':
        return <Badge className="bg-blue-500/20 text-blue-300 border border-blue-500/30 shadow-lg">Pro</Badge>;
      default:
        return <Badge className="bg-slate-500/20 text-slate-300 border border-slate-500/30 shadow-lg">Free</Badge>;
    }
  };
  
  const renderNavItem = (item: NavItem) => {
    const isActive = pathname.startsWith(item.href);
    return (
      <TooltipProvider key={item.href} delayDuration={0}>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className={cn(
              "flex items-center relative group w-full",
              isUiCollapsed && "justify-center"
            )}>
              <Link href={item.href} legacyBehavior passHref>
                <a
                  className={cn(
                    "flex items-center w-full gap-3 flex-grow transition-all duration-300 rounded-xl px-3 py-2 text-slate-300",
                    "hover:text-white hover:bg-gradient-to-r hover:from-[#3dd6f5]/30 hover:to-purple-400/20 hover:shadow-md hover:shadow-[#3dd6f5]/10",
                    isActive && "text-white bg-gradient-to-r from-[#3dd6f5]/30 to-purple-400/20 shadow-md shadow-[#3dd6f5]/5",
                    isUiCollapsed && "justify-center p-3 w-auto"
                  )}
                >
                  <div className={cn(
                    "transition-all duration-300 group-hover:scale-110 group-hover:rotate-3",
                    isActive ? 'text-white' : 'text-slate-400 group-hover:text-[#3dd6f5]',
                    isUiCollapsed ? "mx-auto" : ""
                  )}>
                    <item.icon size={isUiCollapsed ? 24 : 20} />
                  </div>
                  {!isUiCollapsed && item.label && (
                    <span className="flex-grow text-sm font-medium ml-1 group-hover:translate-x-0.5 transition-transform duration-300">{item.label}</span>
                  )}
                </a>
              </Link>
              {!isUiCollapsed && item.action && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link href={item.action.href}>
                        <Button variant="ghost" size="icon" className="h-7 w-7 ml-auto text-slate-400 hover:text-white hover:bg-white/10 rounded-full">
                          <item.action.icon size={16} />
                        </Button>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="bg-[#1a2633] border border-[#2a3a4a] text-slate-200 shadow-lg">
                      <p>{item.action.tooltip}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </TooltipTrigger>
          {isUiCollapsed && (
            <TooltipContent side="right" className="bg-[#1a2633] border border-[#2a3a4a] text-slate-200 shadow-lg">
              <p>{item.label}</p>
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>
    );
  };

  const creationNav: NavItem[] = [
    { label: "Vue d'ensemble", href: "/dashboard", icon: BarChart2 },
    { label: "Morceaux", href: "/manage-songs", icon: Music },
    { label: "Albums", href: "/albums", icon: Disc },
    { label: "Playlists", href: "/playlists", icon: ListMusic },
    { label: "Groupes", href: "/manage-bands", icon: Users },
  ];

  const aiNav: NavItem[] = [
    { label: "AI Composer", href: "/ai-composer", icon: Sparkles },
  ];

  const analysisNav: NavItem[] = [
    { label: "Statistiques", href: "/stats", icon: BarChart2 },
    { label: "Activité", href: "/activity", icon: Activity },
    { label: "Todolist", href: "/tasks", icon: ListTodo },
  ];

  // Navigation avec feature flags
  const messagingNav: NavItem[] = [
    ...(isFeatureEnabled(FLAGS.MESSAGING_SYSTEM, user?.id) ? [
      { label: "Messages", href: "/messages", icon: MessageCircle }
    ] : []),
    ...(isFeatureEnabled(FLAGS.FRIENDS_SYSTEM, user?.id) ? [
      { label: "Amis", href: "/friends", icon: Users }
    ] : []),
    ...(isFeatureEnabled(FLAGS.AUTO_COMMUNITIES, user?.id) ? [
      { label: "Communautés", href: "/communities", icon: Radio }
    ] : []),
  ];

  const socialNav: NavItem[] = [
    { label: "Découvrir", href: "/discover", icon: Compass },
    ...(isFeatureEnabled(FLAGS.SMART_NOTIFICATIONS, user?.id) ? [
      { label: "Notifications", href: "/notifications", icon: Bell }
    ] : []),
  ];

  const footerNav: NavItem[] = [
  { label: 'Aide & Support', href: '/support', icon: HelpCircle },
  { label: 'Éditer le profil', href: '/profile/edit', icon: UserIcon },
  { label: 'Préférences', href: '/preferences', icon: Settings },
];

  return (
    <div className="relative h-full">
      <Sidebar 
        className="bg-gradient-to-b from-[#010204] to-[#020406] border-r border-[#1e2b38]/50 transition-all duration-300 ease-in-out flex flex-col" 
        style={{ width: isUiCollapsed ? '90px' : '280px' }}
      >
        <SidebarHeader className="flex flex-col items-center justify-center py-8 px-3 border-b border-[#2a3a4a]/20 relative bg-gradient-to-b from-[#0a1118]/90 to-[#0a1118]/30">
          <div className="absolute -right-6 top-0 z-10">
            <Button 
              variant="outline" 
              size="icon" 
              onClick={toggleSidebar} 
              className="h-10 w-10 text-[#3dd6f5] hover:text-white bg-[#0a1118] hover:bg-[#3dd6f5]/20 border-2 border-[#3dd6f5]/50 rounded-full shadow-lg shadow-[#3dd6f5]/20 transition-all duration-300 hover:scale-110 hover:shadow-[0_0_15px_rgba(61,214,245,0.4)]"
            >
              {isUiCollapsed ? 
                <div className="flex items-center justify-center">
                  <PanelRight size={20} />
                  <span className="absolute text-xs font-bold opacity-50">→</span>
                </div> : 
                <div className="flex items-center justify-center">
                  <PanelLeft size={20} />
                  <span className="absolute text-xs font-bold opacity-50">←</span>
                </div>
              }
            </Button>
          </div>
          
          <div className="relative mt-20 mb-6">
            <Link href="/dashboard" className="flex items-center">
              <Image 
                src="/LOGO_Mouvik.png" 
                alt="Mouvik" 
                width={isUiCollapsed ? 56 : 128} 
                height={isUiCollapsed ? 56 : 128} 
                className="object-contain drop-shadow-[0_0_12px_rgba(61,214,245,0.5)]" 
              />
            </Link>
            <Link href="/about" className="absolute -right-2 -bottom-2">
              <Badge className="bg-purple-500/30 text-purple-300 border border-purple-500/30 text-[10px] px-1.5 py-0 h-5 hover:bg-purple-500/50 transition-colors">
                BETA
              </Badge>
            </Link>
          </div>
          {!isUiCollapsed && (
            <span className="text-xl font-bold tracking-tighter text-transparent bg-clip-text bg-gradient-to-r from-[#3dd6f5] to-purple-400 drop-shadow-sm">
              MOUVIK
            </span>
          )}
        </SidebarHeader>

        <SidebarContent className="flex-grow overflow-y-auto p-3 space-y-4 mt-8">
          {!isUiCollapsed && <p className="px-3 text-xs font-semibold text-blue-300/80 uppercase tracking-wider">CRÉATION & GESTION</p>}
          <SidebarMenuItem className="flex flex-col space-y-1">
            {creationNav.map(renderNavItem)}
          </SidebarMenuItem>
          
          <SidebarSeparator className="my-3 border-[#2a3a4a]/30" />
          {!isUiCollapsed && <p className="px-3 text-xs font-semibold text-purple-300/80 uppercase tracking-wider">INTELLIGENCE ARTIFICIELLE</p>}
          <SidebarMenuItem className="flex flex-col space-y-1">
            {aiNav.map(renderNavItem)}
          </SidebarMenuItem>
          
          <SidebarSeparator className="my-3 border-[#2a3a4a]/30" />
          {!isUiCollapsed && <p className="px-3 text-xs font-semibold text-green-300/80 uppercase tracking-wider">ANALYSE & SUIVI</p>}
          <SidebarMenuItem className="flex flex-col space-y-1">
            {analysisNav.map(renderNavItem)}
          </SidebarMenuItem>

          {/* Section Communication - Affichée seulement si au moins une feature est activée */}
          {messagingNav.length > 0 && (
            <>
              <SidebarSeparator className="my-3 border-[#2a3a4a]/30" />
              {!isUiCollapsed && <p className="px-3 text-xs font-semibold text-orange-300/80 uppercase tracking-wider">COMMUNICATION</p>}
              <SidebarMenuItem className="flex flex-col space-y-1">
                {messagingNav.map(renderNavItem)}
              </SidebarMenuItem>
            </>
          )}

          <SidebarSeparator className="my-3 border-[#2a3a4a]/30" />
          {!isUiCollapsed && <p className="px-3 text-xs font-semibold text-yellow-300/80 uppercase tracking-wider">SOCIAL & DÉCOUVERTE</p>}
          <SidebarMenuItem className="flex flex-col space-y-1">
            {socialNav.map(renderNavItem)}
          </SidebarMenuItem>
        </SidebarContent>

        <SidebarFooter className="mt-auto p-3 bg-[#0a1118]/50 border-t border-[#2a3a4a]/20">
          {user && (
            <Link href={user.username ? `/artists/${user.username}` : "/preferences"} className="w-full group">
              <div className={`flex items-center gap-3 p-2 mb-3 rounded-xl bg-gradient-to-r from-[#1a2633]/50 to-[#2a3a4a]/30 hover:from-[#1a2633]/70 hover:to-[#2a3a4a]/50 transition-colors group-hover:shadow-md group-hover:shadow-[#3dd6f5]/10 ${isUiCollapsed ? 'justify-center' : ''}`}>
                <div className="relative">
                  <Avatar className="h-10 w-10 border-2 border-[#3dd6f5]/30 shadow-lg shadow-[#3dd6f5]/10 group-hover:border-[#3dd6f5]/60 transition-all">
                    <AvatarImage 
                      src={user.avatar_url || `/avatars/default.png`} 
                      alt={user.display_name ?? user.username ?? 'User Avatar'} 
                    />
                    <AvatarFallback><UserIcon size={20} /></AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-1 -right-1 h-4 w-4 bg-green-500 rounded-full border border-[#0a1118] group-hover:scale-110 transition-transform"></div>
                </div>
                {!isUiCollapsed && (
                  <div className="flex-grow overflow-hidden">
                    <div className="flex items-center gap-1">
                      <p className="text-sm font-semibold text-slate-200 truncate group-hover:text-white transition-colors">{user.display_name || user.username || 'user'}</p>
                      <span className="text-xs text-[#3dd6f5] group-hover:translate-x-0.5 transition-transform">→</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(user.subscription_tier || 'free')}
                    </div>
                  </div>
                )}
              </div>
            </Link>
          )}
          
          {user && user.mouvik_coins !== undefined && (
            <div className={`flex items-center gap-2 p-2 rounded-xl bg-gradient-to-r from-amber-500/20 to-yellow-600/10 mb-3 ${isUiCollapsed ? 'justify-center' : ''}`}>
              <Coins size={isUiCollapsed ? 22 : 18} className="text-amber-400" />
              {!isUiCollapsed && (
                <div className="flex-grow">
                  <p className="text-sm font-semibold text-amber-300">{user.mouvik_coins} <span className="text-xs text-amber-400/70">MOUVIKS</span></p>
                </div>
              )}
              {!isUiCollapsed && (
                <Link href="/buy-credits">
                  <Button variant="ghost" size="sm" className="h-7 px-2 text-xs text-amber-300 hover:bg-amber-500/20 rounded-lg">
                    +
                  </Button>
                </Link>
              )}
            </div>
          )}

          <div className={isUiCollapsed ? "flex flex-col items-center space-y-4" : "space-y-1"}>
            {footerNav.map(renderNavItem)}
          </div>
          
          <div className="mt-2">
            <Button 
              variant="ghost" 
              className={cn(
                "w-full flex items-center gap-2 px-3 py-2 text-slate-300 hover:text-white",
                isUiCollapsed && "justify-center"
              )}
              onClick={handleLogout}
            >
              <LogOut size={isUiCollapsed ? 22 : 18} className="text-slate-400" />
              {!isUiCollapsed && <span className="text-sm font-medium">Déconnexion</span>}
            </Button>
          </div>
        </SidebarFooter>
      </Sidebar>
    </div>
  );
}
