'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { 
  FileText, Music, BarChart3, Settings, 
  Sparkles, Save, Play, Pause 
} from 'lucide-react';

// Imports des modules professionnels
import { AIAssistantPanel } from '../assistant/AIAssistantPanel';
import { AIConnectionHub } from '../assistant/AIConnectionHub';
import { LyricsEditor, LyricsAnalysisPanel } from '../lyrics';
import { SongStructureTimeline } from '../structure';
import { ChordSystemProvider } from '../chords/ChordSystemProvider';
import { ChordDiagramEditor } from '../chords/ChordDiagramEditor';
import { SongMetadataPanel } from './SongMetadataPanel';
import { MegaProInterface } from './MegaProInterface';

interface SongMetadata {
  id?: string;
  title: string;
  artist: string;
  description?: string;
  genre?: string[];
  bpm?: number;
  key?: string;
  coverUrl?: string;
  audioUrl?: string;
  duration?: number;
  createdAt?: string;
  updatedAt?: string;
}

interface AIComposerWorkspaceProps {
  songId?: string;
  initialData?: any;
  onSave?: (data: any) => void;
  onMetadataSave?: (metadata: SongMetadata) => void;
  onCoverUpload?: (file: File) => Promise<string>;
  onAudioUpload?: (file: File) => Promise<string>;
  className?: string;
}

/**
 * Workspace principal de l'AI Composer
 * Point d'entrée unifié pour toutes les fonctionnalités
 */
export const AIComposerWorkspace: React.FC<AIComposerWorkspaceProps> = ({
  songId,
  initialData,
  onSave,
  onMetadataSave,
  onCoverUpload,
  onAudioUpload,
  className = ''
}) => {
  // État principal
  const [activeTab, setActiveTab] = useState('lyrics');
  const [lyricsContent, setLyricsContent] = useState(initialData?.lyrics || '');
  const [chords, setChords] = useState(initialData?.chords || []);
  const [sections, setSections] = useState(initialData?.sections || []);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [isDirty, setIsDirty] = useState(false);

  // État des métadonnées
  const [metadata, setMetadata] = useState<SongMetadata>({
    id: songId,
    title: initialData?.title || 'Nouveau morceau',
    artist: initialData?.artist || 'Artiste inconnu',
    description: initialData?.description || '',
    genre: initialData?.genre || [],
    bpm: initialData?.bpm,
    key: initialData?.key,
    coverUrl: initialData?.coverUrl,
    audioUrl: initialData?.audioUrl,
    duration: initialData?.duration,
    createdAt: initialData?.createdAt,
    updatedAt: initialData?.updatedAt
  });
  const [isEditingMetadata, setIsEditingMetadata] = useState(false);

  // Configuration IA
  const [aiConfig, setAiConfig] = useState({
    provider: 'openai',
    model: 'gpt-4',
    apiKey: '',
    isConfigured: true // Pour les tests, on considère que c'est configuré
  });

  // Fonction utilitaire pour parser les accords depuis le texte IA
  const parseChords = useCallback((text: string) => {
    const chordRegex = /\[([A-G][#b]?(?:m|maj|min|dim|aug|sus|add|7|9|11|13)*)\]/g;
    const matches = [...text.matchAll(chordRegex)];
    return matches.map((match, index) => ({
      id: `ai-chord-${Date.now()}-${index}`,
      name: match[1],
      position: match.index || 0,
      source: 'ai-generated'
    }));
  }, []);

  // Handlers
  const handleLyricsChange = useCallback((newLyrics: string) => {
    setLyricsContent(newLyrics);
    setIsDirty(true);
  }, []);

  const handleChordsChange = useCallback((newChords: any[]) => {
    setChords(newChords);
    setIsDirty(true);
  }, []);

  const handleSectionsChange = useCallback((newSections: any[]) => {
    setSections(newSections);
    setIsDirty(true);
  }, []);

  const handleSave = useCallback(async () => {
    const data = {
      lyrics: lyricsContent,
      chords,
      sections,
      updatedAt: new Date().toISOString()
    };
    
    await onSave?.(data);
    setIsDirty(false);
  }, [lyricsContent, chords, sections, onSave]);

  const handleAIGenerate = useCallback(async (prompt: string, type: string) => {
    try {
      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt,
          provider: aiConfig.provider,
          model: aiConfig.model,
          temperature: 0.7
        }),
      });

      if (!response.ok) {
        throw new Error(`Erreur de l'API : ${response.statusText}`);
      }

      const data = await response.json();
      if (!data.text) {
        throw new Error("La réponse de l'IA est vide.");
      }

      // Mettre à jour le contenu selon le type d'action
      switch (type) {
        case 'continue-lyrics':
          setLyricsContent(prev => `${prev}\n\n${data.text}`);
          break;
        case 'suggest-chords':
          // Parser les accords suggérés et les ajouter
          const suggestedChords = parseChords(data.text);
          setChords(prev => [...prev, ...suggestedChords]);
          break;
        case 'improve-lyrics':
        case 'analyze-structure':
          setLyricsContent(data.text);
          break;
        default:
          setLyricsContent(data.text);
      }

      setIsDirty(true);
    } catch (error: any) {
      console.error('Erreur lors de la génération IA:', error);
      // TODO: Afficher une notification d'erreur
    }
  }, [aiConfig]);

  const handlePlayPause = useCallback(() => {
    setIsPlaying(!isPlaying);
  }, [isPlaying]);

  // Handlers pour les métadonnées
  const handleMetadataChange = useCallback((newMetadata: SongMetadata) => {
    setMetadata(newMetadata);
    setIsDirty(true);
  }, []);

  const handleMetadataSave = useCallback(async () => {
    await onMetadataSave?.(metadata);
    setIsEditingMetadata(false);
    setIsDirty(false);
  }, [metadata, onMetadataSave]);

  // Handlers pour le hub IA mega pro
  const handleConfigChange = useCallback((config: any) => {
    console.log('Configuration IA mise à jour:', config);
    // Intégrer avec la configuration existante
  }, []);

  const handleInsightApply = useCallback((insight: any) => {
    console.log('Application insight:', insight);
    // Appliquer l'insight selon son type
    switch (insight.category) {
      case 'harmony':
        // Appliquer suggestions d'accords
        break;
      case 'lyrics':
        // Appliquer suggestions de paroles
        break;
      case 'structure':
        // Appliquer suggestions de structure
        break;
    }
  }, []);

  const tabs = [
    {
      id: 'lyrics',
      label: 'Paroles',
      icon: FileText,
      content: (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <LyricsEditor
              value={lyricsContent}
              onChange={handleLyricsChange}
              chords={chords}
              onChordsChange={handleChordsChange}
              onSave={handleSave}
            />
          </div>
          <div className="space-y-4">
            <LyricsAnalysisPanel lyricsContent={lyricsContent} />
            <AIAssistantPanel
              isConfigured={aiConfig.isConfigured}
              onAIGenerate={handleAIGenerate}
              lyricsContent={lyricsContent}
              chordPlacements={chords}
            />
          </div>
        </div>
      )
    },
    {
      id: 'chords',
      label: 'Accords',
      icon: Music,
      content: (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <ChordSystemProvider
              selectedChords={chords}
              onChordsChange={handleChordsChange}
            />
          </div>
          <div className="space-y-4">
            {/* Éditeur de diagrammes d'accords */}
            <ChordDiagramEditor
              mode="create"
              onSave={(chord) => console.log('Accord sauvegardé:', chord)}
            />

            <AIAssistantPanel
              isConfigured={aiConfig.isConfigured}
              onAIGenerate={handleAIGenerate}
              lyricsContent={lyricsContent}
              chordPlacements={chords}
            />
          </div>
        </div>
      )
    },
    {
      id: 'structure',
      label: 'Structure',
      icon: BarChart3,
      content: (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <SongStructureTimeline
              sections={sections}
              onSectionsChange={handleSectionsChange}
              currentTime={currentTime}
              onTimeChange={setCurrentTime}
              isPlaying={isPlaying}
              onPlayPause={handlePlayPause}
            />
          </div>
          <div>
            <AIAssistantPanel
              isConfigured={aiConfig.isConfigured}
              onAIGenerate={handleAIGenerate}
              lyricsContent={lyricsContent}
              chordPlacements={chords}
            />
          </div>
        </div>
      )
    },
    {
      id: 'metadata',
      label: 'Métadonnées',
      icon: FileText,
      content: (
        <div className="max-w-4xl mx-auto">
          <SongMetadataPanel
            metadata={metadata}
            onMetadataChange={handleMetadataChange}
            onSave={handleMetadataSave}
            onCoverUpload={onCoverUpload}
            onAudioUpload={onAudioUpload}
            isEditing={isEditingMetadata}
            onEditToggle={() => setIsEditingMetadata(!isEditingMetadata)}
          />
        </div>
      )
    },
    {
      id: 'ai-hub',
      label: 'Hub IA Pro',
      icon: Sparkles,
      content: (
        <div className="max-w-6xl mx-auto">
          <AIConnectionHub
            onConfigChange={handleConfigChange}
            onInsightApply={handleInsightApply}
            currentLyrics={lyricsContent}
            currentChords={chords}
            songMetadata={metadata}
          />
        </div>
      )
    },
    {
      id: 'settings',
      label: 'Configuration',
      icon: Settings,
      content: (
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="p-6">
              <div className="text-center py-8">
                <Settings className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">Configuration</h3>
                <p className="text-muted-foreground mb-4">
                  Configurez votre assistant IA et vos préférences
                </p>
                <Button variant="outline">
                  <Settings className="w-4 h-4 mr-2" />
                  Ouvrir les paramètres
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }
  ];

  return (
    <div className={`min-h-screen bg-background ${className}`}>
      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Sparkles className="w-8 h-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold">AI Composer</h1>
                <p className="text-sm text-muted-foreground">
                  Assistant de composition musicale
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handlePlayPause}
                disabled={sections.length === 0}
              >
                {isPlaying ? (
                  <Pause className="w-4 h-4 mr-2" />
                ) : (
                  <Play className="w-4 h-4 mr-2" />
                )}
                {isPlaying ? 'Pause' : 'Lecture'}
              </Button>
              
              <Button
                onClick={handleSave}
                disabled={!isDirty}
                className="min-w-[100px]"
              >
                <Save className="w-4 h-4 mr-2" />
                {isDirty ? 'Sauvegarder' : 'Sauvegardé'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="container mx-auto px-4 py-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-6 mb-6">
            {tabs.map((tab) => (
              <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                <tab.icon className="w-4 h-4" />
                <span className="hidden sm:inline">{tab.label}</span>
              </TabsTrigger>
            ))}
          </TabsList>
          
          {tabs.map((tab) => (
            <TabsContent key={tab.id} value={tab.id}>
              {tab.content}
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
};
