# 📊 SCHÉMA DE BASE DE DONNÉES COMPLET - MOUVIK

**Version :** 3.0 - Juillet 2024  
**Status :** ✅ **DOCUMENTATION COMPLÈTE ET À JOUR**

---

## 🌟 **VUE D'ENSEMBLE**

Base de données PostgreSQL (Supabase) avec **18 modules intégrés** couvrant l'écosystème musical complet de MOUVIK.

### **🏗️ Architecture Globale**
```
MOUVIK DATABASE - ÉCOSYSTÈME MUSICAL COMPLET
├── 👥 UTILISATEURS & AUTHENTIFICATION (Supabase Auth)
├── 🎵 CONTENU MUSICAL (Songs, Albums, Playlists)
├── 🎸 GROUPES & COLLABORATION (Bands, Members)
├── 💬 COMMUNICATION UNIFIÉE (Chat, Amis, Commentaires)
├── 🏷️ COMMUNAUTÉS & TAGS (Auto-générées)
├── 📊 ANALYTICS & ACTIVITÉ (Stats, Feed Social)
├── 🔔 NOTIFICATIONS & PRÉFÉRENCES
└── 🛡️ SÉCURITÉ & PERMISSIONS (RLS)
```

---

## 📚 **TABLES EXISTANTES - DÉPLOYÉES**

### **🎵 CONTENU MUSICAL**

#### **songs** - Morceaux Musicaux ✅
```sql
CREATE TABLE songs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  genre VARCHAR(100),
  album_id UUID REFERENCES albums(id) ON DELETE SET NULL,
  duration INTEGER,
  cover_url TEXT,
  audio_url TEXT,
  waveform_url TEXT,
  lyrics TEXT,
  chords TEXT,
  bpm INTEGER,
  key VARCHAR(10),
  status VARCHAR(20) NOT NULL DEFAULT 'draft',
  is_ai_generated BOOLEAN DEFAULT FALSE,
  plays INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  release_date DATE
);
```

#### **albums** - Albums Musicaux ✅
```sql
CREATE TABLE albums (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  cover_url TEXT,
  genre VARCHAR(100),
  label VARCHAR(100),
  upc VARCHAR(50),
  status VARCHAR(20) NOT NULL DEFAULT 'draft',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  release_date DATE
);
```

#### **playlists** - Playlists ✅
```sql
CREATE TABLE playlists (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  cover_url TEXT,
  is_public BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **playlist_songs** - Association Playlists-Songs ✅
```sql
CREATE TABLE playlist_songs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  playlist_id UUID NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
  song_id UUID NOT NULL REFERENCES songs(id) ON DELETE CASCADE,
  position INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(playlist_id, song_id)
);
```

### **🎸 GROUPES & COLLABORATION**

#### **bands** - Groupes Musicaux ✅
```sql
CREATE TABLE bands (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  genre VARCHAR(100),
  location VARCHAR(100),
  avatar_url TEXT,
  banner_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **band_members** - Membres des Groupes ✅
```sql
CREATE TABLE band_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  band_id UUID NOT NULL REFERENCES bands(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role VARCHAR(100),
  is_admin BOOLEAN DEFAULT FALSE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(band_id, user_id)
);
```

### **🏷️ TAGS & CLASSIFICATION**

#### **tags** - Tags/Genres ✅
```sql
CREATE TABLE tags (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **resource_tags** - Association Tags-Ressources ✅
```sql
CREATE TABLE resource_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tag_id INTEGER NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
  resource_type VARCHAR(20) NOT NULL,
  resource_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tag_id, resource_type, resource_id)
);
```

### **👥 SOCIAL & INTERACTIONS**

#### **comments** - Commentaires ✅
```sql
CREATE TABLE comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  resource_type VARCHAR(20) NOT NULL,
  resource_id UUID NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **likes** - Système de Likes ✅
```sql
CREATE TABLE likes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  resource_type VARCHAR(20) NOT NULL,
  resource_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, resource_type, resource_id)
);
```

#### **follows** - Système de Follow ✅
```sql
CREATE TABLE follows (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  follower_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  following_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(follower_id, following_id)
);
```

### **📊 ACTIVITÉ & ANALYTICS**

#### **activities** - Activités Utilisateurs ✅
```sql
CREATE TABLE activities (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type VARCHAR(50) NOT NULL,
  resource_type VARCHAR(20) NOT NULL,
  resource_id UUID NOT NULL,
  content TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **todos** - Tâches Utilisateurs ✅
```sql
CREATE TABLE todos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  due_date TIMESTAMP WITH TIME ZONE,
  is_completed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

---

## 🚀 **TABLES À DÉPLOYER - SYSTÈME DE MESSAGERIE UNIFIÉ**

### **👫 SYSTÈME D'AMIS AVANCÉ**

#### **friendships** - Relations d'Amitié 🟡
```sql
CREATE TABLE friendships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  requester_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  addressee_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status VARCHAR(20) DEFAULT 'pending',
  relationship_type VARCHAR(20) DEFAULT 'friend',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(requester_id, addressee_id)
);
```

#### **friend_circles** - Cercles d'Amis 🟡
```sql
CREATE TABLE friend_circles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  color VARCHAR(7),
  icon VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **friend_circle_members** - Membres des Cercles 🟡
```sql
CREATE TABLE friend_circle_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  circle_id UUID NOT NULL REFERENCES friend_circles(id) ON DELETE CASCADE,
  friend_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(circle_id, friend_id)
);
```

### **💬 CHAT TEMPS RÉEL**

#### **conversations** - Conversations 🟡
```sql
CREATE TABLE conversations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  type VARCHAR(20) NOT NULL DEFAULT 'direct',
  name VARCHAR(255),
  description TEXT,
  avatar_url TEXT,
  created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_archived BOOLEAN DEFAULT FALSE,
  settings JSONB DEFAULT '{}'::jsonb
);
```

#### **conversation_participants** - Participants 🟡
```sql
CREATE TABLE conversation_participants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role VARCHAR(20) DEFAULT 'member',
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notification_level VARCHAR(20) DEFAULT 'all',
  is_muted BOOLEAN DEFAULT FALSE,
  is_pinned BOOLEAN DEFAULT FALSE,
  UNIQUE(conversation_id, user_id)
);
```

#### **messages** - Messages 🟡
```sql
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT,
  message_type VARCHAR(20) DEFAULT 'text',
  metadata JSONB DEFAULT '{}'::jsonb,
  reply_to UUID REFERENCES messages(id) ON DELETE SET NULL,
  thread_id UUID REFERENCES messages(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  edited_at TIMESTAMP WITH TIME ZONE,
  is_deleted BOOLEAN DEFAULT FALSE,
  reactions JSONB DEFAULT '{}'::jsonb
);
```

---

## 📋 **RÉSUMÉ DES STATUTS**

### **✅ Tables Déployées et Fonctionnelles (12)**
- songs, albums, playlists, playlist_songs
- bands, band_members
- tags, resource_tags
- comments, likes, follows
- activities, todos

### **🟡 Tables Prêtes pour Déploiement (8)**
- friendships, friend_circles, friend_circle_members
- conversations, conversation_participants, messages
- tag_communities, tag_community_members
- notifications, social_feed_items

### **🎯 Prochaines Étapes**
1. ✅ Analyse BDD complète - **TERMINÉ**
2. 🔄 Mise à jour documentation - **EN COURS**
3. ⏳ Script SQL optimisé pour déploiement
4. ⏳ Mise à jour sidebar avec nouveaux modules
5. ⏳ Déploiement avec feature flags

---

**🎵 MOUVIK 2024 - Base de données la plus complète de l'industrie musicale !**
