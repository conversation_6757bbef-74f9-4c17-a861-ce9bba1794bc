'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Music, Guitar, Piano, Search, Play, Pause, Volume2,
  Plus, Trash2, Grid3X3, List, Settings
} from 'lucide-react';

// Import des données d'accords depuis lib/chords
import guitarChords from '@/lib/chords/guitar.json';
import pianoChords from '@/lib/chords/piano.json';
import ukuleleChords from '@/lib/chords/ukulele_gcea_complete.json';
import { MidiChordPlayer } from '@/lib/chords/midi-chord-player';

// Types pour les données d'accords
interface ChordPosition {
  frets?: number[];
  fingers?: number[];
  midi: number[];
  difficulty: string;
  baseFret?: number;
  chord: string;
  id: string;
}

interface ChordData {
  instrument?: string;
  tuning?: string[];
  keys?: string[];
  suffixes?: string[];
  chords?: Record<string, any[]>;
  [key: string]: any;
}

interface ChordSystemProviderProps {
  selectedChords?: ChordPosition[];
  onChordsChange?: (chords: ChordPosition[]) => void;
  className?: string;
}

// Données des instruments disponibles
const INSTRUMENTS_DATA = [
  { name: 'Guitare', key: 'guitar', data: guitarChords, icon: Guitar },
  { name: 'Piano', key: 'piano', data: pianoChords, icon: Piano },
  { name: 'Ukulélé', key: 'ukulele', data: ukuleleChords, icon: Music },
];

/**
 * Système d'accords professionnel pour AI Composer
 * Intègre pleinement la bibliothèque lib/chords avec interface moderne
 */
export const ChordSystemProvider: React.FC<ChordSystemProviderProps> = ({
  selectedChords = [],
  onChordsChange,
  className = ''
}) => {
  // États principaux
  const [currentInstrument, setCurrentInstrument] = useState('Guitare');
  const [currentKey, setCurrentKey] = useState('C');
  const [searchTerm, setSearchTerm] = useState('');
  const [activeView, setActiveView] = useState<'browser' | 'selected' | 'progressions'>('browser');
  const [isPlaying, setIsPlaying] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Player MIDI
  const [midiPlayer] = useState(() => new MidiChordPlayer());

  // Données de l'instrument actuel
  const currentInstrumentData = INSTRUMENTS_DATA.find(inst => inst.name === currentInstrument);

  // Traitement des données d'accords
  const processChordData = useCallback((data: ChordData) => {
    if (!data.chords) return [];

    const chords: ChordPosition[] = [];
    Object.entries(data.chords).forEach(([chordName, positions]) => {
      positions.forEach((position: any, index: number) => {
        chords.push({
          id: `${chordName}-${index}`,
          chord: chordName,
          frets: position.frets || [],
          fingers: position.fingers || [],
          midi: position.midi || [],
          difficulty: position.difficulty || 'intermediate',
          baseFret: position.baseFret || 0
        });
      });
    });

    return chords;
  }, []);

  // Accords disponibles pour l'instrument actuel
  const availableChords = currentInstrumentData ? processChordData(currentInstrumentData.data) : [];

  // Filtrage des accords
  const filteredChords = availableChords.filter(chord => {
    const matchesSearch = searchTerm === '' ||
      chord.chord.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesKey = currentKey === 'all' ||
      chord.chord.startsWith(currentKey);
    return matchesSearch && matchesKey;
  });

  // Gestion de la lecture des accords
  const playChord = useCallback(async (chord: ChordPosition) => {
    if (!chord.midi || chord.midi.length === 0) return;

    try {
      setIsPlaying(true);
      await midiPlayer.playChord(chord, 1500);
      setTimeout(() => setIsPlaying(false), 1500);
    } catch (error) {
      console.error('Erreur lors de la lecture de l\'accord:', error);
      setIsPlaying(false);
    }
  }, [midiPlayer]);

  // Gestion de la sélection d'accord
  const handleChordSelect = useCallback((chord: ChordPosition) => {
    const isAlreadySelected = selectedChords.some(c => c.id === chord.id);

    if (isAlreadySelected) {
      onChordsChange?.(selectedChords.filter(c => c.id !== chord.id));
    } else {
      onChordsChange?.([...selectedChords, chord]);
    }
  }, [selectedChords, onChordsChange]);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Music className="w-5 h-5" />
            Système d'Accords Professionnel
          </div>
          <Badge variant="secondary">
            {selectedChords.length} sélectionné(s)
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Contrôles principaux */}
        <div className="flex flex-wrap items-center gap-4">
          {/* Sélection d'instrument */}
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">Instrument:</label>
            <Select value={currentInstrument} onValueChange={setCurrentInstrument}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {INSTRUMENTS_DATA.map((instrument) => (
                  <SelectItem key={instrument.key} value={instrument.name}>
                    {instrument.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Sélection de tonalité */}
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">Tonalité:</label>
            <Select value={currentKey} onValueChange={setCurrentKey}>
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes</SelectItem>
                {['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'].map(key => (
                  <SelectItem key={key} value={key}>{key}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Mode d'affichage */}
          <div className="flex items-center gap-1 p-1 bg-muted rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="h-8 px-2"
            >
              <Grid3X3 className="w-3 h-3" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="h-8 px-2"
            >
              <List className="w-3 h-3" />
            </Button>
          </div>

          {/* Recherche */}
          <div className="flex items-center gap-2 flex-1 min-w-[200px]">
            <Search className="w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher un accord..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
          </div>
        </div>

        {/* Onglets de navigation */}
        <Tabs value={activeView} onValueChange={(value) => setActiveView(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="browser">Navigateur</TabsTrigger>
            <TabsTrigger value="selected">Sélectionnés ({selectedChords.length})</TabsTrigger>
            <TabsTrigger value="progressions">Progressions</TabsTrigger>
          </TabsList>

          {/* Contenu des onglets */}
          <TabsContent value="browser" className="space-y-4">
            <ScrollArea className="h-[400px]">
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {filteredChords.slice(0, 20).map((chord) => {
                    const isSelected = selectedChords.some(c => c.id === chord.id);
                    return (
                      <Card
                        key={chord.id}
                        className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                          isSelected ? 'ring-2 ring-primary shadow-lg' : ''
                        }`}
                        onClick={() => handleChordSelect(chord)}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-medium text-sm">{chord.chord}</div>
                            <div className="flex items-center gap-1">
                              <Badge
                                variant={chord.difficulty === 'easy' ? 'default' :
                                        chord.difficulty === 'medium' ? 'secondary' : 'destructive'}
                                className="text-xs"
                              >
                                {chord.difficulty}
                              </Badge>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  playChord(chord);
                                }}
                              >
                                {isPlaying ?
                                  <Pause className="h-3 w-3" /> :
                                  <Play className="h-3 w-3" />
                                }
                              </Button>
                            </div>
                          </div>

                          {/* Visualisation des frettes pour guitare/ukulélé */}
                          {chord.frets && currentInstrument !== 'Piano' && (
                            <div className="mt-2">
                              <div className="text-xs text-muted-foreground mb-1">Frettes:</div>
                              <div className="flex gap-1">
                                {chord.frets.slice(0, 6).map((fret, index) => (
                                  <div
                                    key={index}
                                    className="w-6 h-6 border rounded text-xs flex items-center justify-center bg-muted"
                                  >
                                    {fret === -1 ? 'X' : fret}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Visualisation pour piano */}
                          {currentInstrument === 'Piano' && chord.midi.length > 0 && (
                            <div className="mt-2">
                              <div className="text-xs text-muted-foreground mb-1">Notes MIDI:</div>
                              <div className="text-xs font-mono">
                                {chord.midi.slice(0, 4).join(', ')}
                                {chord.midi.length > 4 && '...'}
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredChords.slice(0, 50).map((chord) => {
                    const isSelected = selectedChords.some(c => c.id === chord.id);
                    return (
                      <div
                        key={chord.id}
                        className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-muted/50 ${
                          isSelected ? 'bg-primary/10 border-primary' : ''
                        }`}
                        onClick={() => handleChordSelect(chord)}
                      >
                        <div className="flex items-center gap-3">
                          <div className="font-medium">{chord.chord}</div>
                          <Badge variant="outline" className="text-xs">
                            {chord.difficulty}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          {isSelected && (
                            <Badge variant="default" className="text-xs">
                              Sélectionné
                            </Badge>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              playChord(chord);
                            }}
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}

              {filteredChords.length === 0 && (
                <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                  <Music className="h-12 w-12 mb-4" />
                  <p className="text-lg font-medium mb-2">Aucun accord trouvé</p>
                  <p className="text-sm text-center">
                    Essayez de modifier vos critères de recherche.
                  </p>
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="selected" className="space-y-4">
            {selectedChords.length > 0 ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Accords sélectionnés</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onChordsChange?.([])}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Tout effacer
                  </Button>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {selectedChords.map((chord) => (
                    <Card key={chord.id} className="relative">
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="font-medium text-sm">{chord.chord}</div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => handleChordSelect(chord)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>

                        {/* Même visualisation que dans le navigateur */}
                        {chord.frets && currentInstrument !== 'Piano' && (
                          <div className="mt-2">
                            <div className="text-xs text-muted-foreground mb-1">Frettes:</div>
                            <div className="flex gap-1">
                              {chord.frets.slice(0, 6).map((fret, index) => (
                                <div
                                  key={index}
                                  className="w-6 h-6 border rounded text-xs flex items-center justify-center bg-muted"
                                >
                                  {fret === -1 ? 'X' : fret}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                <Music className="h-12 w-12 mb-4" />
                <p className="text-lg font-medium mb-2">Aucun accord sélectionné</p>
                <p className="text-sm text-center">
                  Utilisez l'onglet Navigateur pour sélectionner des accords.
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="progressions" className="space-y-4">
            <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
              <Settings className="h-12 w-12 mb-4" />
              <p className="text-lg font-medium mb-2">Progressions d'accords</p>
              <p className="text-sm text-center mb-4">
                Fonctionnalité en développement
              </p>
              <Button variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Créer une progression
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
