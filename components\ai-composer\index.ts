// 🎵 AI COMPOSER - INTERFACE PRINCIPALE
// Architecture professionnelle et modulaire pour la composition musicale assistée par IA

// ============================================================================
// MODULES PRINCIPAUX - ARCHITECTURE PROFESSIONNELLE
// ============================================================================

// Core - Workspace principal
export { AIComposerWorkspace } from './core';

// Assistant IA
export { AIAssistantPanel, AIPromptEngine, AIConnectionHub } from './assistant';
export type { PromptContext, PromptTemplate } from './assistant';

// Éditeur de paroles avec 4 modes de visualisation
export { LyricsEditor, LyricsAnalysisPanel } from './lyrics';

// Système d'accords avancé
export { ChordSystemProvider, ChordDiagramEditor } from './chords';

// Structure de chanson et timeline
export { SongStructureTimeline } from './structure';

// Hooks et utilitaires
export { useAIComposer } from './hooks';

// ============================================================================
// COMPOSANTS LEGACY (À MIGRER PROGRESSIVEMENT)
// ============================================================================
export { AILyricsAssistant } from './AILyricsAssistant';
export { AIChordIntegration } from './AIChordIntegration';
export { AIInsightsPanel } from './AIInsightsPanel';
export { SongStructurePanel } from './SongStructurePanel';
export { StyleThemeConfig } from './StyleThemeConfig';
export { UnifiedSongStructureTimeline } from './UnifiedSongStructureTimeline';
export { ChordLibraryManager } from './ChordLibraryManager';

// ============================================================================
// INTERFACE UNIFIÉE POUR UTILISATION
// ============================================================================
export const AIComposer = {
  // Workspace principal
  Workspace: AIComposerWorkspace,

  // Modules spécialisés
  Assistant: AIAssistantPanel,
  AIHub: AIConnectionHub,
  LyricsEditor: LyricsEditor,
  LyricsAnalysis: LyricsAnalysisPanel,
  ChordSystem: ChordSystemProvider,
  ChordEditor: ChordDiagramEditor,
  Timeline: SongStructureTimeline,

  // Utilitaires
  PromptEngine: AIPromptEngine,
  useComposer: useAIComposer,
};
