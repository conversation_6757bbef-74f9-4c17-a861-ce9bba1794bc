"use client";

import { GlobalAudioPlayer } from "@/components/audio/global-audio-player";
import { AppSidebar, UserProfileForSidebar } from "@/components/ui/app-sidebar";
import MobileMenuButton from "./mobile-menu-button";

interface MainLayoutProps {
  children: React.ReactNode;
  user: UserProfileForSidebar;
}

export default function MainLayout({ children, user }: MainLayoutProps) {
  return (
    <div className="flex h-screen main-layout-container">
      <MobileMenuButton />
      <AppSidebar user={user} />
      <div className="flex-1 flex flex-col">
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
        <GlobalAudioPlayer />
      </div>
    </div>
  );
}
