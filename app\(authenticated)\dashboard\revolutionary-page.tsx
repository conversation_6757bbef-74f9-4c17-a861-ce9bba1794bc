"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown,
  Music2, 
  Disc3, 
  Users, 
  Heart, 
  Play, 
  Share2,
  MessageCircle,
  Star,
  Zap,
  Target,
  Award,
  Sparkles,
  BarChart3,
  Activity,
  Globe,
  Calendar,
  Clock,
  Headphones,
  Radio,
  Crown
} from 'lucide-react';

interface DashboardData {
  user: {
    id: string;
    name: string;
    username: string;
    avatar: string;
    level: string;
    xp: number;
    nextLevelXp: number;
  };
  globalStats: {
    totalPlays: number;
    totalLikes: number;
    totalFollowers: number;
    totalCollaborations: number;
    engagementScore: number;
    trendingScore: number;
    weeklyGrowth: number;
    monthlyRevenue: number;
  };
  recentActivity: any[];
  topContent: any[];
  collaborationOpportunities: any[];
  aiInsights: any[];
  goals: any[];
}

export default function RevolutionaryDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('7d');
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);

  // Données de démonstration enrichies
  const mockData: DashboardData = {
    user: {
      id: '1',
      name: 'Alex Rivera',
      username: '@alex_music',
      avatar: '/avatars/alex.jpg',
      level: 'Compositeur Avancé',
      xp: 2847,
      nextLevelXp: 3000
    },
    globalStats: {
      totalPlays: 45230,
      totalLikes: 3420,
      totalFollowers: 1250,
      totalCollaborations: 23,
      engagementScore: 87.5,
      trendingScore: 94.2,
      weeklyGrowth: 12.8,
      monthlyRevenue: 1240
    },
    recentActivity: [
      { type: 'play', content: 'Midnight Jazz', count: 156, trend: '+23%', time: '2h' },
      { type: 'like', content: 'Electric Dreams', count: 45, trend: '+18%', time: '4h' },
      { type: 'follow', content: 'Nouveaux abonnés', count: 12, trend: '+8%', time: '6h' },
      { type: 'collaboration', content: 'Projet avec Luna', count: 1, trend: 'nouveau', time: '1d' }
    ],
    topContent: [
      { 
        id: '1', 
        title: 'Midnight Jazz', 
        type: 'song', 
        plays: 15420, 
        likes: 892, 
        trend: 23.5,
        genre: 'Jazz',
        collaborators: ['Luna Collective']
      },
      { 
        id: '2', 
        title: 'Electric Dreams', 
        type: 'album', 
        plays: 8930, 
        likes: 567, 
        trend: 18.2,
        genre: 'Electronic',
        collaborators: ['Cyber Waves', 'Neon Beats']
      },
      { 
        id: '3', 
        title: 'Chill Vibes Mix', 
        type: 'playlist', 
        plays: 5240, 
        likes: 234, 
        trend: 31.7,
        genre: 'Ambient',
        collaborators: []
      }
    ],
    collaborationOpportunities: [
      {
        id: '1',
        name: 'Sophie Chen',
        type: 'producer',
        matchScore: 94,
        project: 'Album Ambient',
        deadline: '2 semaines',
        budget: '€2,500'
      },
      {
        id: '2',
        name: 'Electric Minds',
        type: 'band',
        matchScore: 87,
        project: 'Single Rock',
        deadline: '1 mois',
        budget: '€1,200'
      }
    ],
    aiInsights: [
      {
        type: 'trend',
        title: 'Votre style Jazz évolue',
        description: 'L\'IA détecte une progression vers le Jazz Fusion dans vos dernières compositions',
        confidence: 89,
        action: 'Explorer les collaborations Fusion'
      },
      {
        type: 'opportunity',
        title: 'Moment optimal pour sortir',
        description: 'Vos fans sont 34% plus actifs le vendredi soir',
        confidence: 76,
        action: 'Programmer sortie vendredi'
      },
      {
        type: 'growth',
        title: 'Potentiel de croissance détecté',
        description: 'Votre audience Electronic pourrait croître de 45% avec plus de contenu',
        confidence: 82,
        action: 'Créer contenu Electronic'
      }
    ],
    goals: [
      { name: '10K écoutes ce mois', current: 7420, target: 10000, progress: 74 },
      { name: '5 nouvelles collaborations', current: 3, target: 5, progress: 60 },
      { name: '1500 abonnés', current: 1250, target: 1500, progress: 83 },
      { name: 'Sortir 2 albums', current: 1, target: 2, progress: 50 }
    ]
  };

  useEffect(() => {
    // Simulation du chargement des données
    setTimeout(() => {
      setDashboardData(mockData);
    }, 1000);
  }, []);

  const chartData = [
    { name: 'Lun', plays: 1200, likes: 89, shares: 23 },
    { name: 'Mar', plays: 1890, likes: 134, shares: 45 },
    { name: 'Mer', plays: 2340, likes: 178, shares: 67 },
    { name: 'Jeu', plays: 1980, likes: 145, shares: 34 },
    { name: 'Ven', plays: 3420, likes: 267, shares: 89 },
    { name: 'Sam', plays: 4120, likes: 312, shares: 123 },
    { name: 'Dim', plays: 2890, likes: 198, shares: 56 }
  ];

  const genreData = [
    { name: 'Jazz', value: 35, color: '#8884d8' },
    { name: 'Electronic', value: 28, color: '#82ca9d' },
    { name: 'Ambient', value: 20, color: '#ffc658' },
    { name: 'Rock', value: 12, color: '#ff7300' },
    { name: 'Autres', value: 5, color: '#00ff88' }
  ];

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 70) return 'text-yellow-400';
    if (score >= 50) return 'text-orange-400';
    return 'text-red-400';
  };

  const getTrendIcon = (trend: number) => {
    return trend > 0 ? (
      <TrendingUp className="h-4 w-4 text-green-400" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-400" />
    );
  };

  if (!dashboardData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-slate-400">Chargement de votre dashboard révolutionnaire...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header avec profil utilisateur */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <Avatar className="h-20 w-20 border-4 border-blue-500/30">
                <AvatarImage src={dashboardData.user.avatar} />
                <AvatarFallback className="bg-slate-600 text-white text-2xl">
                  {dashboardData.user.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-4xl font-bold text-white mb-1">
                  Bonjour, {dashboardData.user.name.split(' ')[0]} ! 👋
                </h1>
                <p className="text-xl text-slate-300">{dashboardData.user.username}</p>
                <div className="flex items-center gap-4 mt-2">
                  <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                    <Crown className="h-4 w-4 mr-1" />
                    {dashboardData.user.level}
                  </Badge>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-slate-400">XP:</span>
                    <Progress 
                      value={(dashboardData.user.xp / dashboardData.user.nextLevelXp) * 100} 
                      className="w-24 h-2"
                    />
                    <span className="text-sm text-slate-300">
                      {dashboardData.user.xp}/{dashboardData.user.nextLevelXp}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="h-5 w-5 text-yellow-400" />
                <span className="text-2xl font-bold text-white">
                  {dashboardData.globalStats.engagementScore}
                </span>
                <span className="text-slate-400">Score d'engagement</span>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="border-slate-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  {timeRange === '7d' ? '7 jours' : timeRange === '30d' ? '30 jours' : '90 jours'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Métriques principales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 border-blue-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-300 text-sm font-medium">Total Écoutes</p>
                  <p className="text-3xl font-bold text-white">
                    {dashboardData.globalStats.totalPlays.toLocaleString()}
                  </p>
                  <div className="flex items-center gap-1 mt-1">
                    {getTrendIcon(dashboardData.globalStats.weeklyGrowth)}
                    <span className="text-sm text-green-400">
                      +{dashboardData.globalStats.weeklyGrowth}% cette semaine
                    </span>
                  </div>
                </div>
                <Play className="h-12 w-12 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-red-500/20 to-red-600/20 border-red-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-300 text-sm font-medium">Total Likes</p>
                  <p className="text-3xl font-bold text-white">
                    {dashboardData.globalStats.totalLikes.toLocaleString()}
                  </p>
                  <div className="flex items-center gap-1 mt-1">
                    <Heart className="h-4 w-4 text-red-400" />
                    <span className="text-sm text-red-400">
                      Taux d'engagement: {((dashboardData.globalStats.totalLikes / dashboardData.globalStats.totalPlays) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
                <Heart className="h-12 w-12 text-red-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-500/20 to-green-600/20 border-green-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-300 text-sm font-medium">Abonnés</p>
                  <p className="text-3xl font-bold text-white">
                    {dashboardData.globalStats.totalFollowers.toLocaleString()}
                  </p>
                  <div className="flex items-center gap-1 mt-1">
                    <Users className="h-4 w-4 text-green-400" />
                    <span className="text-sm text-green-400">
                      +{Math.floor(dashboardData.globalStats.weeklyGrowth * 10)} cette semaine
                    </span>
                  </div>
                </div>
                <Users className="h-12 w-12 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-500/20 to-purple-600/20 border-purple-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-300 text-sm font-medium">Score Tendance</p>
                  <p className="text-3xl font-bold text-white">
                    {dashboardData.globalStats.trendingScore}
                  </p>
                  <div className="flex items-center gap-1 mt-1">
                    <Star className="h-4 w-4 text-purple-400" />
                    <span className={`text-sm ${getScoreColor(dashboardData.globalStats.trendingScore)}`}>
                      {dashboardData.globalStats.trendingScore >= 90 ? 'Excellent' : 
                       dashboardData.globalStats.trendingScore >= 70 ? 'Très bon' : 'Bon'}
                    </span>
                  </div>
                </div>
                <TrendingUp className="h-12 w-12 text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Onglets principaux */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="bg-slate-800 border-slate-700 grid grid-cols-5 w-full">
            <TabsTrigger value="overview" className="data-[state=active]:bg-slate-700">
              <BarChart3 className="h-4 w-4 mr-2" />
              Vue d'ensemble
            </TabsTrigger>
            <TabsTrigger value="content" className="data-[state=active]:bg-slate-700">
              <Music2 className="h-4 w-4 mr-2" />
              Mes Créations
            </TabsTrigger>
            <TabsTrigger value="network" className="data-[state=active]:bg-slate-700">
              <Users className="h-4 w-4 mr-2" />
              Mon Réseau
            </TabsTrigger>
            <TabsTrigger value="opportunities" className="data-[state=active]:bg-slate-700">
              <Target className="h-4 w-4 mr-2" />
              Opportunités
            </TabsTrigger>
            <TabsTrigger value="insights" className="data-[state=active]:bg-slate-700">
              <Sparkles className="h-4 w-4 mr-2" />
              Insights IA
            </TabsTrigger>
          </TabsList>

          {/* Vue d'ensemble */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Graphique principal */}
              <div className="lg:col-span-2">
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Activité des 7 derniers jours
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <AreaChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                        <XAxis dataKey="name" stroke="#9CA3AF" />
                        <YAxis stroke="#9CA3AF" />
                        <Tooltip 
                          contentStyle={{ 
                            backgroundColor: '#1F2937', 
                            border: '1px solid #374151',
                            borderRadius: '8px'
                          }}
                        />
                        <Area 
                          type="monotone" 
                          dataKey="plays" 
                          stackId="1"
                          stroke="#3B82F6" 
                          fill="#3B82F6" 
                          fillOpacity={0.3}
                        />
                        <Area 
                          type="monotone" 
                          dataKey="likes" 
                          stackId="2"
                          stroke="#EF4444" 
                          fill="#EF4444" 
                          fillOpacity={0.3}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>

              {/* Répartition par genre */}
              <div>
                <Card className="bg-slate-800/50 border-slate-700 mb-6">
                  <CardHeader>
                    <CardTitle className="text-white">Répartition par Genre</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={200}>
                      <PieChart>
                        <Pie
                          data={genreData}
                          cx="50%"
                          cy="50%"
                          innerRadius={40}
                          outerRadius={80}
                          paddingAngle={5}
                          dataKey="value"
                        >
                          {genreData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                    <div className="space-y-2 mt-4">
                      {genreData.map((genre, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-3 h-3 rounded-full" 
                              style={{ backgroundColor: genre.color }}
                            ></div>
                            <span className="text-slate-300 text-sm">{genre.name}</span>
                          </div>
                          <span className="text-slate-400 text-sm">{genre.value}%</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Objectifs */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Target className="h-5 w-5" />
                      Objectifs du Mois
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {dashboardData.goals.map((goal, index) => (
                      <div key={index}>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-slate-300 text-sm">{goal.name}</span>
                          <span className="text-slate-400 text-sm">
                            {goal.current}/{goal.target}
                          </span>
                        </div>
                        <Progress value={goal.progress} className="h-2" />
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Autres onglets à développer... */}
          <TabsContent value="content">
            <div className="text-center py-12">
              <Music2 className="h-16 w-16 mx-auto mb-4 text-slate-500" />
              <h3 className="text-lg font-medium text-slate-300 mb-2">Mes Créations en développement</h3>
              <p className="text-slate-400">Cette section affichera vos morceaux, albums et playlists avec analytics détaillés.</p>
            </div>
          </TabsContent>

          <TabsContent value="network">
            <div className="text-center py-12">
              <Users className="h-16 w-16 mx-auto mb-4 text-slate-500" />
              <h3 className="text-lg font-medium text-slate-300 mb-2">Mon Réseau en développement</h3>
              <p className="text-slate-400">Cette section affichera l'activité de vos amis et collaborateurs.</p>
            </div>
          </TabsContent>

          <TabsContent value="opportunities">
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-white mb-2">Opportunités de Collaboration</h2>
                <p className="text-slate-400">Projets et collaborations recommandés par l'IA</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {dashboardData.collaborationOpportunities.map((opportunity) => (
                  <Card key={opportunity.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="font-semibold text-white text-lg">{opportunity.name}</h3>
                          <p className="text-slate-400 capitalize">{opportunity.type}</p>
                        </div>
                        <Badge className={`${opportunity.matchScore >= 90 ? 'bg-green-500' : 'bg-yellow-500'} text-white`}>
                          {opportunity.matchScore}% match
                        </Badge>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <p className="text-sm text-slate-300 font-medium">Projet :</p>
                          <p className="text-sm text-slate-400">{opportunity.project}</p>
                        </div>
                        <div className="flex justify-between">
                          <div>
                            <p className="text-sm text-slate-300 font-medium">Deadline :</p>
                            <p className="text-sm text-slate-400">{opportunity.deadline}</p>
                          </div>
                          <div>
                            <p className="text-sm text-slate-300 font-medium">Budget :</p>
                            <p className="text-sm text-green-400">{opportunity.budget}</p>
                          </div>
                        </div>

                        <div className="flex gap-2 pt-2">
                          <Button size="sm" className="flex-1 bg-blue-500 hover:bg-blue-600">
                            <MessageCircle className="h-4 w-4 mr-2" />
                            Contacter
                          </Button>
                          <Button size="sm" variant="outline" className="border-slate-600">
                            Détails
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="insights">
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-white mb-2">Insights IA</h2>
                <p className="text-slate-400">Analyses et recommandations personnalisées</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {dashboardData.aiInsights.map((insight, index) => (
                  <Card key={index} className="bg-slate-800/50 border-slate-700">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-3 mb-4">
                        <div className="h-10 w-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                          <Sparkles className="h-5 w-5 text-purple-400" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-white mb-1">{insight.title}</h3>
                          <p className="text-slate-400 text-sm">{insight.description}</p>
                        </div>
                      </div>

                      <div className="flex items-center justify-between mb-4">
                        <span className="text-xs text-slate-500">Confiance IA</span>
                        <div className="flex items-center gap-2">
                          <Progress value={insight.confidence} className="w-16 h-2" />
                          <span className="text-xs text-slate-400">{insight.confidence}%</span>
                        </div>
                      </div>

                      <Button size="sm" className="w-full bg-purple-500 hover:bg-purple-600">
                        {insight.action}
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
