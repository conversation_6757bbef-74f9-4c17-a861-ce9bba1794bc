# Architecture de MOUVIK

## Vue d'ensemble

**Note:** The project has switched from using `auth-helpers-nextjs` to `SSR` for authentication.

MOUVIK est une application full-stack construite avec Next.js, React et Supabase. L'architecture est conçue pour être modulaire, évolutive et maintenable.

## Diagramme d'architecture

\`\`\`mermaid
graph TD
    Client[Client Browser] --> NextJS[Next.js App]
    NextJS --> ServerComponents[Server Components]
    NextJS --> ClientComponents[Client Components]
    ServerComponents --> SupabaseServer[Supabase Server Client]
    ClientComponents --> SupabaseClient[Supabase Browser Client]
    SupabaseServer --> SupabaseBackend[Supabase Backend]
    SupabaseClient --> SupabaseBackend
    SupabaseBackend --> PostgreSQL[(PostgreSQL Database)]
    SupabaseBackend --> Auth[Auth Service]
    SupabaseBackend --> Storage[Storage Service]
    NextJS --> AIServices[AI Services]
    AIServices --> OpenAI[OpenAI]
    AIServices --> CustomModels[Custom Music Models]
\`\`\`

## Couches d'application

### 1. Interface utilisateur (UI)
- **Composants React** : Composants d'interface utilisateur réutilisables (ex: `Button`, `Card`, `Input`, `LyricsEditorWithAI`, `AiAssistantPanel`, `VisibilityToggle`).
- **Pages Next.js** : Pages de l'application avec rendu côté serveur (App Router).
- **Layouts** : Structures de mise en page partagées.

### 2. Logique métier
- **Hooks personnalisés** : Logique réutilisable pour les composants
- **Context API** : État global de l'application
- **Actions serveur** : Fonctions exécutées côté serveur

### 3. Accès aux données
- **Client Supabase** : Interaction avec la base de données et les services Supabase
- **Utilitaires de requête** : Fonctions pour simplifier les requêtes courantes

### 4. Services externes
- **Services IA** : Intégration avec des modèles d'IA pour la génération musicale
- **Traitement audio** : Services pour le traitement et l'analyse audio

## Flux de données

1. **Création de contenu** : L'utilisateur crée du contenu (chansons, albums) via l'interface
2. **Stockage** : Les fichiers sont stockés dans Supabase Storage
3. **Métadonnées** : Les métadonnées sont stockées dans la base de données PostgreSQL
4. **Récupération** : Les données sont récupérées via des requêtes Supabase
5. **Rendu** : Les données sont rendues dans l'interface utilisateur

## Sécurité

### Authentification

L'authentification est gérée par **Supabase Auth**, en utilisant le package `@supabase/ssr`. Ce package est spécifiquement conçu pour Next.js et facilite la gestion des sessions et des clients Supabase dans les différents contextes (Server Components, Client Components, API Routes, Actions).

- **Client Côté Serveur** (`/lib/supabase/server.ts`):
  - Utilise `createServerClient` de `@supabase/ssr`.
  - Wrapper `createSupabaseServerClient` pour simplifier l'instanciation dans les Server Components, API Routes et Server Actions.
  - Gère les cookies pour maintenir la session serveur.

- **Client Côté Client** (`/lib/supabase/client.ts`):
  - Utilise `createBrowserClient` de `@supabase/ssr`.
  - Wrapper `createBrowserClient` pour simplifier l'instanciation dans les Client Components.
  - Assure la synchronisation de la session entre le client et le serveur.

- **Flux d'Authentification**:
  - Popup d'authentification (`components/auth/auth-popup.tsx`) pour login/signup via email/password.
  - Gestion des callbacks OAuth (si ajoutés plus tard).
  - Protection des routes via les layouts (`app/(authenticated)/layout.tsx`) qui vérifient la session serveur.

Le passage de l'ancien `@supabase/auth-helpers-nextjs` à `@supabase/ssr` modernise l'approche et s'intègre mieux avec l'App Router de Next.js.

### Base de Données
Voir [DATABASE.md](./DATABASE.md) pour une description détaillée du modèle de données.

## Stratégie de déploiement

- **Environnement de développement** : Vercel Preview Deployments
- **Environnement de production** : Vercel Production Deployment
- **Base de données** : Supabase Project

## Points d'architecture clés (Focus Formulaire Chanson)

- **Modularité du Formulaire (`SongForm`)**: Le composant `SongForm` a été refactorisé pour déléguer la logique et l'affichage spécifiques à des sous-composants :
    - `LyricsEditorWithAI`: Gère l'éditeur de paroles (Rich Text) et les actions IA directement liées au contenu textuel (Génération, Correction, Traduction, Formatage, Rimes, Analyse de Ton).
    - `AiAssistantPanel`: Gère le panneau latéral de l'assistant IA, affichant les actions IA plus générales (Suggestions globales, Mélodie, Enregistrement, Instrumentation, FX, Arrangement) via `AiQuickActions`.
    - `VisibilityToggle`: Composant helper réutilisable pour afficher un label de champ à côté d'un `Switch` contrôlant sa visibilité publique.
- **Organisation par Onglets**: Le formulaire utilise des onglets (`Tabs`) pour séparer les sections : Général, Paroles / IA, Publication & Options. L'onglet Média a été supprimé car les uploads sont gérés dans l'en-tête.
- **Onglet "Général" Structuré**: Cet onglet utilise une grille et des `Card` pour regrouper logiquement les champs (Infos principales, Description/Catégorisation, Détails Musicaux, Instrumentation, Bloc-notes).
- **Visibilité des Champs**: Introduction de flags booléens (`is_*_public`, `are_*_public`) dans la base de données (`songs` table) et le schéma Zod (`songFormSchema`) pour permettre un contrôle fin de la visibilité publique de chaque champ pertinent. Des `Switch` sont ajoutés dans le formulaire pour gérer ces flags.
- **Bloc-notes**: Les champs `bloc_note` et `right_column_notepad` sont maintenant situés dans l'onglet "Général".

## Architecture du Module AI Composer

Conformément au plan maître, le module `AI Composer` est en cours de refactoring vers une architecture modulaire par domaine fonctionnel pour améliorer la clarté, la maintenabilité et l'évolutivité.

**Structure Cible des Composants :**
```
components/
└── ai-composer/
    ├── assistant/      # Le hub IA unifié
    ├── chords/         # Le système d'accords
    ├── core/           # Le cœur du workspace (layout, provider)
    ├── lyrics/         # L'éditeur de paroles et ses outils
    ├── structure/      # La timeline de structure
    └── hooks/          # Hooks réutilisables spécifiques au module
```

**Principaux Systèmes Unifiés :**
- **Système d'Édition (`lyrics/`)**: Un éditeur central (`LyricsEditor.tsx`) gère le texte, les accords en overlay et la structure.
- **Système d'Accords (`chords/`)**: Le `ChordSystemProvider.tsx` est la source de vérité unique pour toutes les données harmoniques.
- **Hub d'Intelligence Artificielle (`assistant/`)**: Un panneau unique et contextuel (`AIAssistantPanel.tsx`) regroupe toutes les interactions IA.
- **Moteur Audio (`audio/` - Futur)**: Un ensemble de services pour l'enregistrement, la lecture de samples et le métronome.
- **Timeline Interactive (`structure/`)**: Le `SongStructureTimeline.tsx` intègre la visualisation des paroles, des accords et des pistes audio.

## Évolutions Récentes (Mai 2025)

### Pages et Composants Playlist
-   **Page "Mes Playlists" (`app/(authenticated)/playlists/page.tsx`)**:
    -   Transformée en Client Component pour gérer l'état local (recherche, tri, mode d'affichage).
    -   Récupère les playlists de l'utilisateur via la vue `user_playlist_details`.
    -   Implémente la recherche par nom et le tri (par nom, date de création) côté client.
    -   Permet de basculer entre une vue "Grille" (utilisant `PlaylistCard`) et une vue "Liste" (utilisant `PlaylistListItem`).
    -   Gère la suppression de playlists avec mise à jour de l'UI.
-   **Composant `PlaylistCard` (`components/playlists/playlist-card.tsx`)**:
    -   Affiche les informations de la playlist : pochette, nom, créateur, nombre de morceaux, statut public/privé.
    -   Affiche les étiquettes de taxonomie (genres, ambiances - max 2 de chaque).
    -   Affiche les statistiques (vues, likes, followers, lectures - si > 0).
    -   Le lien principal (pochette/titre) pointe vers la page publique (`/playlist/[slug]`) si disponible, sinon vers la page authentifiée (`/playlists/[id]`).
    -   Menu d'options pour "Modifier", "Dupliquer" (placeholder), et "Supprimer".
-   **Composant `PlaylistListItem` (`components/playlists/playlist-list-item.tsx`)**:
    -   Affichage en ligne pour la vue liste.
    -   Inclut miniature, nom, créateur, nombre de morceaux, statut public/privé, date de création, quelques statistiques clés, et étiquettes de genre (max 3).
    -   Boutons d'action et menu d'options similaires à `PlaylistCard`.
-   **Page de Détail Playlist (`app/(authenticated)/playlists/[id]/page.tsx`)**:
    -   Utilise maintenant la fonction RPC `get_playlist_details_for_view` pour récupérer les données. Ceci a été implémenté comme contournement pour des problèmes persistants de cache du schéma PostgREST qui empêchaient le chargement correct des jointures (notamment `profiles:user_id`).
-   **Page d'Édition de Playlist (`app/(authenticated)/playlists/[id]/edit/page.tsx`)**:
    -   Récupère et affiche la liste des morceaux de la playlist, ordonnés par `track_number` (ou `position`).
    -   Permet la suppression d'un morceau de la playlist (via la table `playlist_songs`).

### Système de Coûts de Création (Admin)
-   **Table `creation_costs`**: Nouvelle table pour stocker les coûts en pièces pour créer différents types de contenu (playlist, album, etc.), configurables par l'admin.
-   **Composant `CreationCostsManager` (`components/admin/creation-costs-manager.tsx`)**: Ajouté à la page d'administration (`/admin`) pour permettre aux admins de visualiser et modifier ces coûts.
-   **RPC `create_playlist_with_coin_deduction`**: Mise à jour pour lire le coût depuis `creation_costs` et appliquer un coût de 0 pour les admins. Les formulaires de création de playlist utilisent cette RPC et affichent le coût dynamiquement.

## Améliorations Futures / TODO

- **Commentaires sur la Timeline du Lecteur**: Implémenter une fonctionnalité permettant aux utilisateurs d'ajouter des commentaires à des moments spécifiques sur la timeline du lecteur audio (similaire à SoundCloud). Cela nécessitera :
    - Une nouvelle table de base de données (ex: `timeline_comments`) avec `song_id`, `user_id`, `timestamp_seconds`, `comment_text`, `avatar_url_snapshot`.
    - Des composants UI pour afficher les marqueurs de commentaires sur la waveform et pour afficher/ajouter des commentaires.
    - Une logique pour lier les commentaires aux timestamps audio.
- **Configuration CORS pour Supabase Storage**: S'assurer que les politiques CORS pour les buckets `covers` et `audio` dans Supabase Storage sont correctement configurées pour autoriser les requêtes `GET` depuis les domaines de l'application (ex: `http://localhost:3000` et le domaine de production). Ceci est crucial pour l'affichage des images et la lecture des fichiers audio.
- **Contraintes d'Upload de Fichiers**: Afficher des informations à l'utilisateur sur les contraintes recommandées pour les fichiers audio et images (ex: taille max, dimensions pour les images, formats) et potentiellement implémenter des validations côté client ou serveur.
- **Synchronisation du Lecteur Waveform**: Améliorer la synchronisation entre le lecteur waveform de l'en-tête de la page du morceau et le lecteur audio global pour une expérience utilisateur plus fluide.
- **Affichage des Données Publiques**: Continuer à affiner l'affichage des informations sur la page publique du morceau pour assurer la cohérence avec les champs disponibles dans le formulaire d'édition et éviter les redondances.

### Composants Sociaux et de Statistiques (Ajoutés Mai 2025)
-   **`ResourceStatsDisplay.tsx`**:
    -   **Rôle**: Composant réutilisable pour afficher de manière standardisée les statistiques d'une ressource (likes, dislikes, vues, lectures, followers).
    -   **Props**: Accepte les compteurs de statistiques et le `resourceType` (album, song, playlist, band, profile).
    -   **Utilisation**: Intégré dans les en-têtes des pages publiques/détaillées pour chaque type de ressource.
-   **`FollowProfileButton.tsx`**:
    -   **Rôle**: Bouton permettant aux utilisateurs de suivre/ne plus suivre un profil d'artiste.
    -   **Logique**: Appelle le RPC `toggle_profile_follow` pour mettre à jour la table `profile_followers` et le compteur `profiles.follower_count`. Gère l'état local pour une UI réactive.
-   **`FollowBandButton.tsx`**:
    -   **Rôle**: Bouton permettant aux utilisateurs de suivre/ne plus suivre un groupe.
    -   **Logique**: Appelle le RPC `toggle_band_follow` pour mettre à jour la table `band_followers` et le compteur `bands.follower_count`.
-   **`LikeButton.tsx` / `DislikeButton.tsx` / `FollowPlaylistButton.tsx`**:
    -   Ces composants existants ont été vérifiés et leur intégration a été assurée sur toutes les pages pertinentes pour une expérience utilisateur cohérente.

---
## TODOs & Prochaines Étapes (Module Plans, Stats, Admin)

### Système de Plans d'Abonnement et Limites (Quotas)
- **Logique Serveur**:
    - Implémenter la vérification et l'application pour toutes les limites définies dans `plan_limits` (ex: `max_playlists`, `max_friends`) qui ne sont pas encore couvertes par les RLS/triggers existants (uploads, vault).
    - **CRON Job**: L'utilisateur doit configurer le CRON job mensuel dans Supabase pour l'allocation des crédits IA et des Coins (la requête SQL a été fournie).
- **Interface Utilisateur (Frontend)**:
    - Afficher l'utilisation actuelle par rapport aux quotas pour : espace du coffre-fort (Vault), crédits IA, Coins, nombre de playlists, nombre d'amis.
    - Désactiver conditionnellement les actions pertinentes lorsque les quotas sont atteints (ex: création de nouvelle playlist, ajout d'ami).
    - Intégrer toutes les fonctionnalités conditionnées par le plan (ex: `songs.analytics_public`, `songs.paywall_price`, `albums.is_embeddable`) dans les formulaires et les pages d'affichage, en se basant sur `usePlanLimits`.

### Module de Statistiques et Suggestions
- **Statistiques Utilisateur (`/stats`)**:
    - Implémenter les graphiques et vues détaillées pour les niveaux d'analyse 'extended' et 'pro' (ex: vues/likes/followers par période, comparaisons de performance entre contenus).
    - Envisager une table `daily_stats_summary` pour optimiser les performances des requêtes de statistiques agrégées.
- **Module de Suggestions**:
    - Développer la logique de suggestion de contenu (morceaux, albums, artistes) basée sur l'activité de l'utilisateur et les métadonnées.
    - Implémenter les suggestions de collaboration.

### Module d'Administration (`/admin`)
- **Interface Utilisateur**:
    - **Structure de Base**: La structure de base pour la section `/admin` (route group `app/(admin)/admin/page.tsx` et layout `app/(admin)/layout.tsx` avec protection de rôle) a été créée.
    - Implémenter la gestion des utilisateurs : liste, affichage des détails, modification des rôles (`user_role`), des niveaux d'abonnement (`subscription_tier`), et des quotas personnalisés (`custom_...` dans `profiles`, y compris `custom_vault_max_files`).
    - Implémenter la modération de contenu (morceaux, albums, commentaires) avec gestion des statuts de modération.
    - Afficher les statistiques globales de la plateforme pour les administrateurs.
    - Créer une interface pour la gestion de la table `plan_limits` (y compris `vault_max_files`).
- **Base de Données & Logique**:
    - Mettre en place la table `audit_logs` et la logique pour enregistrer les actions des administrateurs.
    - Ajouter les colonnes de statut de modération (`moderation_status`, `moderation_notes`) aux tables de contenu concernées si ce n'est pas déjà fait via un autre développement.

### Système de Comptage (Vues, Lectures, Likes)
- **Déploiement Edge Functions**: L'utilisateur doit déployer les Edge Functions `record-view` et `increment-play-count` (dont le code a été fourni) sur son projet Supabase.
- **Contexte Audio**: S'assurer que `AudioContext` charge et fournit les comptes `plays`, `views`, et `likes` pour `currentSong` afin que `GlobalAudioPlayer` les affiche correctement.
- **Intégration `ViewRecorder`**: Étendre l'utilisation du composant `ViewRecorder.tsx` aux pages d'albums, de profils d'artistes et de groupes pour un comptage complet des vues.
- **Comptage des Lectures (Plays)**: Affiner la logique dans `GlobalAudioPlayer` pour que l'incrémentation du compteur de lectures soit basée sur une durée d'écoute significative (ex: X secondes ou % du morceau) plutôt qu'un simple démarrage.
