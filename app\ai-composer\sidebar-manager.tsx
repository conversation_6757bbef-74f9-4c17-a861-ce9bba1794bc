'use client';

import { useSidebar } from '@/components/ui/sidebar-logic';
import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

/**
 * A client component that manages the sidebar state for a specific layout.
 * It can collapse the sidebar by default.
 */
export function SidebarManager({ defaultCollapsed = true }: { defaultCollapsed?: boolean }) {
  const { state, toggleSidebar, isMobile } = useSidebar();
  const pathname = usePathname();

  useEffect(() => {
    // Do not manage state on mobile, as it's an overlay
    if (isMobile) {
      return;
    }

    if (defaultCollapsed && state === 'expanded') {
      toggleSidebar();
    } else if (!defaultCollapsed && state === 'collapsed') {
      toggleSidebar();
    }
    // The dependency on pathname ensures this logic re-evaluates on navigation
    // within the layout, but the core logic only runs if the state is not the desired one.
  }, [defaultCollapsed, state, toggleSidebar, isMobile, pathname]);

  return null; // This component renders nothing.
}
