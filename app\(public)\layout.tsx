import type React from "react"
import "../globals.css"; // Apply global styles
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { AppSidebar } from "@/components/ui/app-sidebar";
import MobileMenuButton from "@/app/(authenticated)/mobile-menu-button"; // Réutiliser le bouton existant

interface PublicPageLayoutProps {
  children: React.ReactNode
}

interface ProfileDataForLayout {
  display_name?: string | null;
  username?: string | null;
  avatar_url?: string | null;
  role_primary?: string | null;
  subscription_tier?: string | null; 
  user_role?: string | null; 
  custom_uploads_per_month?: number | null;
  custom_vault_space_gb?: number | null;
  custom_vault_max_files?: number | null;
  custom_ia_credits_month?: number | null;
  custom_coins_month?: number | null;
  custom_max_playlists?: number | null;
  custom_max_friends?: number | null;
  ia_credits?: number | null; 
  coins_balance?: number | null; 
}

import { ClientLayout } from "@/components/client-layout";
import type { UserProfileForSidebar } from "@/components/ui/app-sidebar"; // Ensure this type is available

export default async function PublicPageLayout({ children }: PublicPageLayoutProps) {
  const supabase = createSupabaseServerClient();

  const {
    data: { user }, // AuthUser or null
  } = await supabase.auth.getUser();

  let userObjForSidebar: UserProfileForSidebar | null = null;

  if (user) {
    const profileFieldsToSelect = [
      "display_name", "username", "avatar_url", 
      "role_primary", "subscription_tier", "user_role",
      "custom_uploads_per_month", "custom_vault_space_gb", "custom_vault_max_files",
      "custom_ia_credits_month", "custom_coins_month",
      "custom_max_playlists", "custom_max_friends",
      "ia_credits", "coins_balance"
    ].join(", ");

    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select(profileFieldsToSelect) 
      .eq("id", user.id)
      .single<ProfileDataForLayout>(); 

    if (profileError) {
      console.error("Error fetching profile for PublicPageLayout/AppSidebar:", profileError);
      // Create a minimal user object if profile fetch fails but auth user exists
      userObjForSidebar = {
        id: user.id,
        email: user.email,
        name: user.email?.split('@')[0],
        avatar_url: null,
        username: null,
        role_primary: null,
        subscription_tier: 'free', 
        user_role: 'user', 
        custom_uploads_per_month: null, custom_vault_space_gb: null, custom_vault_max_files: null,
        custom_ia_credits_month: null, custom_coins_month: null, 
        custom_max_playlists: null, custom_max_friends: null,
        ia_credits: null, coins_balance: null,
      };
    } else if (profile) {
      userObjForSidebar = {
        id: user.id,
        email: user.email,
        name: profile.display_name || profile.username || user.email?.split('@')[0],
        avatar_url: profile.avatar_url,
        username: profile.username,
        role_primary: profile.role_primary,
        subscription_tier: profile.subscription_tier,
        user_role: profile.user_role,
        custom_uploads_per_month: profile.custom_uploads_per_month,
        custom_vault_space_gb: profile.custom_vault_space_gb,
        custom_vault_max_files: profile.custom_vault_max_files,
        custom_ia_credits_month: profile.custom_ia_credits_month,
        custom_coins_month: profile.custom_coins_month,
        custom_max_playlists: profile.custom_max_playlists,
        custom_max_friends: profile.custom_max_friends,
        ia_credits: profile.ia_credits, 
        coins_balance: profile.coins_balance, 
      };
    }
  }
  // If user is null, userObjForSidebar remains null, and AppSidebar will handle this (e.g., show login/register or limited view)

  return (
    <ClientLayout initialUser={userObjForSidebar}>
      <div className="flex h-full w-full">
        <AppSidebar user={userObjForSidebar} />
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    </ClientLayout>
  );
}
