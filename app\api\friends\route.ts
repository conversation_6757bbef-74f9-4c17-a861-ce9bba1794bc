import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const filter = searchParams.get('filter') || 'all';
    const search = searchParams.get('search') || '';

    // Construire la requête pour les amis
    let query = supabase
      .from('friendships')
      .select(`
        *,
        requester:requester_id(id, name, avatar_url),
        addressee:addressee_id(id, name, avatar_url)
      `)
      .eq('status', 'accepted')
      .or(`requester_id.eq.${user.id},addressee_id.eq.${user.id}`);

    const { data: friendships, error } = await query;

    if (error) {
      console.error('Erreur chargement amis:', error);
      return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
    }

    // Formater les données des amis
    const friends = friendships?.map(friendship => {
      const friend = friendship.requester_id === user.id 
        ? friendship.addressee 
        : friendship.requester;
      
      return {
        id: friend.id,
        name: friend.name,
        avatar_url: friend.avatar_url,
        is_online: false, // À implémenter avec presence
        relationship_type: friendship.relationship_type,
        mutual_friends_count: 0, // À calculer
        last_seen: null // À implémenter
      };
    }) || [];

    // Appliquer les filtres
    let filteredFriends = friends;
    
    if (filter === 'online') {
      filteredFriends = friends.filter(friend => friend.is_online);
    } else if (filter === 'close') {
      filteredFriends = friends.filter(friend => 
        ['close_friend', 'family'].includes(friend.relationship_type)
      );
    }

    // Recherche
    if (search) {
      filteredFriends = filteredFriends.filter(friend =>
        friend.name.toLowerCase().includes(search.toLowerCase())
      );
    }

    return NextResponse.json({ friends: filteredFriends });
  } catch (error) {
    console.error('Erreur API amis:', error);
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
    }

    const body = await request.json();
    const { addressee_id, relationship_type = 'friend' } = body;

    // Vérifier si une demande existe déjà
    const { data: existing } = await supabase
      .from('friendships')
      .select('*')
      .or(`and(requester_id.eq.${user.id},addressee_id.eq.${addressee_id}),and(requester_id.eq.${addressee_id},addressee_id.eq.${user.id})`)
      .single();

    if (existing) {
      return NextResponse.json({ error: 'Demande d\'amitié déjà existante' }, { status: 400 });
    }

    // Créer la demande d'amitié
    const { data: friendship, error } = await supabase
      .from('friendships')
      .insert({
        requester_id: user.id,
        addressee_id,
        relationship_type,
        status: 'pending'
      })
      .select()
      .single();

    if (error) {
      console.error('Erreur création demande amitié:', error);
      return NextResponse.json({ error: 'Erreur création demande' }, { status: 500 });
    }

    // Créer une notification pour le destinataire
    await supabase
      .from('notifications')
      .insert({
        user_id: addressee_id,
        type: 'friend_request',
        title: 'Nouvelle demande d\'amitié',
        content: `${user.user_metadata?.name || 'Un utilisateur'} vous a envoyé une demande d'amitié`,
        actor_id: user.id,
        target_type: 'friendship',
        target_id: friendship.id,
        priority: 'normal'
      });

    return NextResponse.json({ friendship });
  } catch (error) {
    console.error('Erreur API demande amitié:', error);
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 });
  }
}
