# 🚀 GUIDE DE DÉPLOIEMENT - SYSTÈME DE MESSAGERIE UNIFIÉ

**Version :** 1.0 - Juillet 2024  
**Status :** ✅ **PRÊT POUR DÉPLOIEMENT**

---

## 🌟 **VUE D'ENSEMBLE**

Ce guide détaille le déploiement sécurisé du système de messagerie unifié MOUVIK, incluant :
- 💬 Chat temps réel
- 👫 Système d'amis avancé  
- 🏷️ Communautés automatiques
- 🔔 Notifications intelligentes

---

## 📋 **PRÉREQUIS**

### **🔧 Environnement Technique**
- Node.js 18+ installé
- Accès administrateur à Supabase
- Variables d'environnement configurées
- Backup de la base de données actuelle

### **🗄️ Base de Données**
- PostgreSQL 14+ (Supabase)
- Tables existantes fonctionnelles :
  - `songs`, `albums`, `comments`, `tags`
  - `follows`, `activities`, `playlists`

### **🔑 Variables d'Environnement**
```bash
SUPABASE_PROJECT_ID=wwvfnwjcsshhekfnmogt
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-key
```

---

## 🚀 **PROCÉDURE DE DÉPLOIEMENT**

### **Étape 1 : Préparation**

```bash
# 1. Cloner/mettre à jour le repository
git pull origin master

# 2. Installer les dépendances
npm install

# 3. Vérifier les fichiers de migration
ls -la db/migrations/20240702_deploy_messaging_system.sql
```

### **Étape 2 : Test en Mode Dry-Run**

```bash
# Simulation complète sans modifications
node scripts/deploy-messaging-system.js --dry-run
```

**Vérifications attendues :**
- ✅ Fichier de migration trouvé
- ✅ Variables d'environnement validées
- ✅ Connectivité Supabase OK
- ✅ Répertoire de backup prêt

### **Étape 3 : Déploiement Réel**

```bash
# Déploiement avec backup automatique
node scripts/deploy-messaging-system.js
```

**Processus automatique :**
1. 🔍 Vérifications préalables
2. 💾 Création backup de sécurité
3. 📊 Exécution migration SQL
4. ✅ Vérifications post-déploiement
5. 🚩 Activation feature flags

### **Étape 4 : Vérification Manuelle**

```sql
-- Vérifier les nouvelles tables
SELECT table_name FROM information_schema.tables 
WHERE table_name IN (
  'friendships', 'friend_circles', 'conversations', 
  'messages', 'notifications', 'tag_communities'
);

-- Vérifier les index
SELECT indexname FROM pg_indexes 
WHERE indexname LIKE 'idx_%messaging%';

-- Vérifier RLS
SELECT tablename, policyname FROM pg_policies 
WHERE tablename IN ('friendships', 'conversations', 'messages');
```

---

## 🔧 **FEATURE FLAGS**

### **Configuration Actuelle**

| Feature | Status | Rollout | Environnement |
|---------|--------|---------|---------------|
| `MESSAGING_SYSTEM` | ✅ Activé | 100% | dev, staging |
| `FRIENDS_SYSTEM` | ✅ Activé | 100% | dev, staging |
| `AUTO_COMMUNITIES` | ✅ Activé | 80% | dev, staging |
| `SMART_NOTIFICATIONS` | ✅ Activé | 90% | dev, staging |
| `SOCIAL_FEED` | ❌ Désactivé | 0% | dev only |

### **Activation Progressive**

```typescript
// Vérifier si une feature est activée
import { isFeatureEnabled, FLAGS } from '@/lib/feature-flags';

if (isFeatureEnabled(FLAGS.MESSAGING_SYSTEM, userId)) {
  // Afficher l'interface de messagerie
}
```

---

## 🛡️ **SÉCURITÉ & PERMISSIONS**

### **Row Level Security (RLS)**

Toutes les nouvelles tables ont RLS activé :

```sql
-- Exemple : Politique pour les messages
CREATE POLICY "Users can view messages in their conversations" 
ON messages FOR SELECT USING (
  conversation_id IN (
    SELECT conversation_id FROM conversation_participants 
    WHERE user_id = auth.uid()
  )
);
```

### **Permissions API**

- ✅ Lecture : Utilisateurs authentifiés uniquement
- ✅ Écriture : Propriétaires et participants autorisés
- ✅ Suppression : Créateurs et administrateurs
- ✅ Modération : Rôles admin/moderator

---

## 📊 **MONITORING & MÉTRIQUES**

### **Métriques Clés à Surveiller**

1. **Performance Base de Données**
   - Temps de réponse des requêtes
   - Utilisation des index
   - Connexions actives

2. **Utilisation Features**
   - Nombre de conversations créées
   - Messages envoyés par jour
   - Demandes d'amitié

3. **Erreurs & Alertes**
   - Échecs de connexion
   - Erreurs RLS
   - Timeouts API

### **Dashboard Supabase**

Surveiller dans l'interface Supabase :
- Database > Performance
- Auth > Users activity  
- API > Logs & metrics

---

## 🔄 **ROLLBACK & RÉCUPÉRATION**

### **Rollback Automatique**

En cas d'erreur, le script effectue un rollback automatique :

```bash
# Le backup est automatiquement restauré
# Logs disponibles dans logs/deployment.log
```

### **Rollback Manuel**

```bash
# Rollback explicite
node scripts/deploy-messaging-system.js --rollback

# Ou restauration manuelle depuis backup
psql -h your-db-host -U postgres -d postgres < backups/backup_TIMESTAMP.sql
```

### **Désactivation Feature Flags**

```typescript
// Dans lib/feature-flags.ts
export const FEATURE_FLAGS = {
  MESSAGING_SYSTEM: {
    enabled: false, // Désactiver temporairement
    rolloutPercentage: 0
  }
};
```

---

## 🧪 **TESTS POST-DÉPLOIEMENT**

### **Tests Fonctionnels**

1. **Messagerie**
   - [ ] Créer une conversation
   - [ ] Envoyer un message
   - [ ] Recevoir des notifications

2. **Amis**
   - [ ] Envoyer demande d'amitié
   - [ ] Accepter/refuser demande
   - [ ] Créer cercle d'amis

3. **Communautés**
   - [ ] Rejoindre communauté automatique
   - [ ] Voir contenu communauté
   - [ ] Participer aux discussions

### **Tests de Performance**

```bash
# Test de charge sur les nouvelles API
npm run test:load

# Monitoring temps réel
npm run monitor:realtime
```

---

## 📞 **SUPPORT & DÉPANNAGE**

### **Problèmes Courants**

| Problème | Cause | Solution |
|----------|-------|----------|
| Tables non créées | Permissions insuffisantes | Vérifier SUPABASE_SERVICE_ROLE_KEY |
| RLS bloque requêtes | Politiques mal configurées | Revoir les politiques auth.uid() |
| Feature flags inactifs | Cache navigateur | Vider cache + redémarrer |

### **Logs & Debugging**

```bash
# Consulter les logs de déploiement
tail -f logs/deployment.log

# Logs Supabase en temps réel
supabase logs --follow

# Debug feature flags
console.log(getEnabledFeatures(userId));
```

### **Contact Support**

- 📧 **Email :** <EMAIL>
- 💬 **Slack :** #mouvik-tech
- 📱 **Urgence :** +33 1 XX XX XX XX

---

## ✅ **CHECKLIST FINALE**

### **Avant Déploiement**
- [ ] Backup base de données créé
- [ ] Variables d'environnement vérifiées
- [ ] Tests dry-run réussis
- [ ] Équipe technique notifiée

### **Après Déploiement**
- [ ] Nouvelles tables créées (10/10)
- [ ] Index et triggers fonctionnels
- [ ] RLS activé sur toutes les tables
- [ ] Feature flags configurés
- [ ] Tests fonctionnels passés
- [ ] Monitoring activé
- [ ] Documentation mise à jour

### **Communication**
- [ ] Équipe développement informée
- [ ] Utilisateurs bêta notifiés
- [ ] Changelog publié
- [ ] Métriques de base établies

---

## 🎉 **SUCCÈS !**

Une fois toutes les étapes complétées, le système de messagerie unifié MOUVIK est opérationnel avec :

- ✅ **10 nouvelles tables** déployées
- ✅ **4 modules** de communication intégrés
- ✅ **Sécurité RLS** complète
- ✅ **Feature flags** pour contrôle progressif
- ✅ **Monitoring** et alertes actifs

**🎵 MOUVIK 2024 - La première "Super-App Musicale" au monde !**

---

*Dernière mise à jour : Juillet 2024*  
*Version du guide : 1.0*
