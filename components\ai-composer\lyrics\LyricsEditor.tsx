'use client';

import React, { useState, useCallback, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  FileText, Music, Eye, Type, Grid3X3, Save
} from 'lucide-react';

interface ChordPlacement {
  id: string;
  name: string;
  position: number;
  line: number;
  column: number;
  text: string;
}

interface LyricsEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  chords?: ChordPlacement[];
  onChordsChange?: (chords: ChordPlacement[]) => void;
  displayMode?: 'text' | 'chords' | 'hybrid' | 'enhanced';
  onDisplayModeChange?: (mode: 'text' | 'chords' | 'hybrid' | 'enhanced') => void;
  onSave?: () => void;
  autoSave?: boolean;
  className?: string;
}

/**
 * Éditeur de paroles principal
 * Remplace AILyricsAssistant avec une approche plus simple et professionnelle
 */
export const LyricsEditor: React.FC<LyricsEditorProps> = ({
  value = '',
  onChange,
  placeholder = 'Commencez à écrire vos paroles ici...',
  chords = [],
  onChordsChange,
  displayMode = 'hybrid',
  onDisplayModeChange,
  onSave,
  autoSave = true,
  className = ''
}) => {
  const [localValue, setLocalValue] = useState(value);
  const [isDirty, setIsDirty] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleChange = useCallback((newValue: string) => {
    setLocalValue(newValue);
    setIsDirty(true);
    onChange?.(newValue);
  }, [onChange]);

  const handleSave = useCallback(() => {
    onSave?.();
    setIsDirty(false);
  }, [onSave]);

  const displayModes = [
    { id: 'text', label: 'Texte', icon: Type },
    { id: 'chords', label: 'Accords', icon: Music },
    { id: 'hybrid', label: 'Hybride', icon: Grid3X3 },
    { id: 'enhanced', label: 'Avancé', icon: Eye },
  ] as const;

  const getWordCount = () => {
    return localValue.trim().split(/\s+/).filter(Boolean).length;
  };

  const getLineCount = () => {
    return localValue.split('\n').length;
  };

  const detectChords = (): ChordPlacement[] => {
    // Détection avancée des accords dans le texte [Am], [C], etc.
    const chordRegex = /\[([A-G][#b]?(?:m|maj|min|dim|aug|sus|add|7|9|11|13)*)\]/g;
    const matches = [...localValue.matchAll(chordRegex)];
    const lines = localValue.split('\n');

    return matches.map((match, index) => {
      const position = match.index || 0;
      let line = 0;
      let column = position;
      let currentPos = 0;

      // Calculer la ligne et la colonne
      for (let i = 0; i < lines.length; i++) {
        if (currentPos + lines[i].length >= position) {
          line = i;
          column = position - currentPos;
          break;
        }
        currentPos += lines[i].length + 1; // +1 pour le \n
      }

      return {
        id: `chord-${index}`,
        name: match[1],
        position,
        line,
        column,
        text: match[0]
      };
    });
  };

  const detectedChords = detectChords();

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Éditeur de Paroles
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {/* Modes d'affichage */}
            <div className="flex items-center gap-1 p-1 bg-muted rounded-md">
              {displayModes.map((mode) => (
                <Button
                  key={mode.id}
                  variant={displayMode === mode.id ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => onDisplayModeChange?.(mode.id)}
                  className="h-8 px-2"
                >
                  <mode.icon className="w-3 h-3" />
                  <span className="sr-only">{mode.label}</span>
                </Button>
              ))}
            </div>
            
            {/* Actions */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleSave}
              disabled={!isDirty}
            >
              <Save className="w-4 h-4 mr-1" />
              Sauvegarder
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Zone d'édition principale */}
        {displayMode === 'text' ? (
          // Mode Texte Seul
          <div className="relative">
            <Textarea
              ref={textareaRef}
              value={localValue}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
              className="min-h-[400px] font-mono text-sm leading-relaxed resize-none"
            />
          </div>
        ) : displayMode === 'chords' ? (
          // Mode Accords Seuls
          <div className="space-y-4">
            <div className="text-center py-8 border-2 border-dashed border-muted-foreground/30 rounded-lg">
              <Music className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">Mode Accords</h3>
              <p className="text-sm text-muted-foreground mb-4">
                {detectedChords.length} accord(s) détecté(s)
              </p>
              <div className="flex flex-wrap gap-2 justify-center">
                {detectedChords.map((chord) => (
                  <Badge key={chord.id} variant="secondary" className="text-sm">
                    {chord.name}
                  </Badge>
                ))}
              </div>
            </div>
            <Textarea
              ref={textareaRef}
              value={localValue}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
              className="min-h-[200px] font-mono text-sm leading-relaxed resize-none"
            />
          </div>
        ) : displayMode === 'hybrid' ? (
          // Mode Hybride - Texte + Accords
          <div className="relative">
            <Textarea
              ref={textareaRef}
              value={localValue}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
              className="min-h-[400px] font-mono text-sm leading-relaxed resize-none"
              style={{ lineHeight: '2.2' }}
            />
            {/* Overlay d'accords pour mode hybride */}
            {detectedChords.length > 0 && (
              <div className="absolute inset-0 pointer-events-none p-3">
                {detectedChords.map((chord) => (
                  <div
                    key={chord.id}
                    className="absolute bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium"
                    style={{
                      top: `${chord.line * 2.2 * 14 + 8}px`, // Approximation basée sur line-height
                      left: `${chord.column * 7 + 12}px`, // Approximation basée sur font-size
                      transform: 'translateY(-100%)'
                    }}
                  >
                    {chord.name}
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          // Mode Enhanced - Interface avancée
          <div className="relative">
            <Textarea
              ref={textareaRef}
              value={localValue}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={placeholder}
              className="min-h-[400px] font-mono text-sm leading-relaxed resize-none"
              style={{ lineHeight: '2.5' }}
            />
            {/* Overlay interactif pour mode enhanced */}
            {detectedChords.length > 0 && (
              <div className="absolute inset-0 p-3">
                {detectedChords.map((chord) => (
                  <div
                    key={chord.id}
                    className="absolute group"
                    style={{
                      top: `${chord.line * 2.5 * 14 + 8}px`,
                      left: `${chord.column * 7 + 12}px`,
                      transform: 'translateY(-100%)'
                    }}
                  >
                    <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium cursor-pointer hover:bg-blue-200 transition-colors">
                      {chord.name}
                    </div>
                    {/* Tooltip avec informations d'accord */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                        Cliquer pour éditer l'accord
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
        
        {/* Informations et statistiques */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>{getWordCount()} mots</span>
            <span>{getLineCount()} lignes</span>
            {detectedChords.length > 0 && (
              <span>{detectedChords.length} accords détectés</span>
            )}
            {chords.length > 0 && (
              <span>{chords.length} accords externes</span>
            )}
          </div>

          <div className="flex items-center gap-2">
            {detectedChords.length > 0 && onChordsChange && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onChordsChange(detectedChords)}
                className="text-xs"
              >
                Synchroniser
              </Button>
            )}
            {isDirty && (
              <Badge variant="outline" className="text-xs">
                Non sauvegardé
              </Badge>
            )}
            {autoSave && (
              <Badge variant="secondary" className="text-xs">
                Sauvegarde auto
              </Badge>
            )}
          </div>
        </div>
        
        {/* Accords détectés */}
        {detectedChords.length > 0 && displayMode !== 'text' && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Accords détectés :</h4>
            <div className="flex flex-wrap gap-2">
              {detectedChords.map((chord) => (
                <Badge key={chord.id} variant="outline" className="text-xs">
                  {chord.name}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
